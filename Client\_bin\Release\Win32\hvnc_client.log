[2025-07-30 11:23:10.894] [INFO] HVNC Client logging initialized
[2025-07-30 11:23:10.896] [INFO] HVNC Client starting - Host: **************, Port: 4043
[2025-07-30 11:23:10.903] [INFO] HVNC connection thread started successfully
[2025-07-30 11:23:10.903] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-30 11:23:10.909] [INFO] TurboJPEG image processing initialized successfully
[2025-07-30 11:23:10.909] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-30 11:23:10.909] [INFO] HVNC Profile: High Quality | JPEG Quality: 90 | Frame Rate: 30 FPS | Compression: 8
[2025-07-30 11:23:10.909] [INFO] DEBUG: Compile-time constants - Quality: 90, FPS: 30, Interval: 33
[2025-07-30 11:23:10.909] [INFO] MainThread: Starting HVNC connection process...
[2025-07-30 11:23:10.909] [INFO] MainThread: Target server: **************:4043
[2025-07-30 11:23:10.909] [INFO] MainThread: Windows 11 detected - will use virtual desktop capture mode
[2025-07-30 11:23:10.909] [INFO] MainThread: Step 1 - Testing connection with server...
[2025-07-30 11:23:10.921] [INFO] Socket created with default configuration
[2025-07-30 11:23:10.943] [INFO] Successfully connected to **************:4043
[2025-07-30 11:23:10.943] [INFO] MainThread: Connection test successful!
[2025-07-30 11:23:10.947] [INFO] MainThread: Step 2 - Preparing virtual desktop environment...
[2025-07-30 11:23:10.948] [INFO] VirtualDesktopCapture: Initializing Windows 11 Virtual Desktop Capture System (no desktop creation)
[2025-07-30 11:23:10.963] [WARN] VirtualDesktopCapture: Failed to create VirtualDesktopManagerInternal: 0x80040154
[2025-07-30 11:23:10.971] [INFO] VirtualDesktopCapture: VirtualDesktopManager created successfully
[2025-07-30 11:23:10.973] [WARN] VirtualDesktopCapture: Cannot store original desktop - no COM interface
[2025-07-30 11:23:10.973] [WARN] VirtualDesktopCapture: Failed to store original desktop
[2025-07-30 11:23:10.973] [INFO] VirtualDesktopCapture: Initializing screen capture system
[2025-07-30 11:23:10.973] [INFO] VirtualDesktopCapture: Desktop dimensions: 1920x1080
[2025-07-30 11:23:10.978] [INFO] VirtualDesktopCapture: Screen capture system initialized successfully
[2025-07-30 11:23:10.978] [INFO] VirtualDesktopCapture: Virtual desktop capture system initialized successfully (no desktop created)
[2025-07-30 11:23:10.978] [INFO] MainThread: Virtual desktop capture system initialized successfully (no desktop created)
[2025-07-30 11:23:10.978] [INFO] MainThread: Step 3 - Starting HVNC connection and control threads...
[2025-07-30 11:23:10.978] [INFO] MainThread: Creating input thread for HVNC control...
[2025-07-30 11:23:10.978] [INFO] MainThread: Input thread created successfully
[2025-07-30 11:23:10.984] [INFO] Socket created with default configuration
[2025-07-30 11:23:10.988] [INFO] Successfully connected to **************:4043
[2025-07-30 11:23:10.988] [INFO] InputThread: Using virtual desktop mode - skipping thread desktop setting
[2025-07-30 11:23:11.071] [INFO] InputThread: Connection established - assigning server to virtual desktop permanently
[2025-07-30 11:23:11.079] [ERROR] VirtualDesktopCapture: Cannot assign server to virtual desktop - missing interfaces
[2025-07-30 11:23:11.079] [INFO] VirtualDesktopCapture: Switching to target desktop using keyboard
[2025-07-30 11:23:11.347] [INFO] VirtualDesktopCapture: Desktop switch command sent
[2025-07-30 11:23:11.347] [INFO] InputThread: Server successfully assigned to virtual desktop - parallel operation active
[2025-07-30 11:23:11.347] [INFO] Socket created with default configuration
[2025-07-30 11:23:11.370] [INFO] Successfully connected to **************:4043
[2025-07-30 11:23:11.370] [INFO] DesktopThread: Using virtual desktop mode - skipping thread desktop setting
[2025-07-30 11:23:11.370] [INFO] Using single-threaded capture mode for compatibility
[2025-07-30 11:23:12.279] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:12.299] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 51238 bytes (3.9% ratio) in 16ms
[2025-07-30 11:23:12.299] [DEBUG] TurboJPEG compression successful: 784x561 -> 51238 bytes
[2025-07-30 11:23:12.299] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 51238 bytes JPEG
[2025-07-30 11:23:13.259] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:13.259] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 51238 bytes (3.9% ratio) in 0ms
[2025-07-30 11:23:13.259] [DEBUG] TurboJPEG compression successful: 784x561 -> 51238 bytes
[2025-07-30 11:23:13.259] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 51238 bytes JPEG
[2025-07-30 11:23:13.879] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:13.895] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 51238 bytes (3.9% ratio) in 16ms
[2025-07-30 11:23:13.895] [DEBUG] TurboJPEG compression successful: 784x561 -> 51238 bytes
[2025-07-30 11:23:13.895] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 51238 bytes JPEG
[2025-07-30 11:23:14.369] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:14.369] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 51238 bytes (3.9% ratio) in 0ms
[2025-07-30 11:23:14.378] [DEBUG] TurboJPEG compression successful: 784x561 -> 51238 bytes
[2025-07-30 11:23:14.378] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 51238 bytes JPEG
[2025-07-30 11:23:14.807] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:14.839] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 51238 bytes (3.9% ratio) in 31ms
[2025-07-30 11:23:14.839] [DEBUG] TurboJPEG compression successful: 784x561 -> 51238 bytes
[2025-07-30 11:23:14.839] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 51238 bytes JPEG
[2025-07-30 11:23:15.322] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:15.338] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92172 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:15.354] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92172 bytes
[2025-07-30 11:23:15.354] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92172 bytes JPEG
[2025-07-30 11:23:16.328] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:16.355] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:16.363] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:16.371] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:17.123] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:17.143] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:17.143] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:17.154] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:17.188] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x0
[2025-07-30 11:23:17.188] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x0
[2025-07-30 11:23:17.233] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x2
[2025-07-30 11:23:17.240] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x2
[2025-07-30 11:23:17.783] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:17.831] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 31ms
[2025-07-30 11:23:17.831] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:17.831] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:18.417] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:18.449] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:18.449] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:18.464] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:19.577] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:19.641] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 47ms
[2025-07-30 11:23:19.641] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:19.657] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:20.669] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:20.691] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:20.691] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:20.701] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:21.490] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:21.522] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:21.522] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:21.538] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:22.476] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:22.510] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:22.512] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:22.520] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:23.080] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:23.088] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:23.090] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:23.094] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:23.548] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:23.564] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:23.564] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:23.564] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:23.983] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:23.999] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:23.999] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:23.999] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:24.443] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:24.452] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 15ms
[2025-07-30 11:23:24.452] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:24.458] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:25.357] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:25.363] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:25.371] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:25.375] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:26.021] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:26.069] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92148 bytes (1.6% ratio) in 32ms
[2025-07-30 11:23:26.085] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92148 bytes
[2025-07-30 11:23:26.101] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92148 bytes JPEG
[2025-07-30 11:23:26.106] [DEBUG] VirtualDesktopCapture: Mouse input request - x:550, y:300, flags:0x8002
[2025-07-30 11:23:26.108] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:550,300 -> 18773,18204
[2025-07-30 11:23:26.124] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:550, y:300, flags:0x8002
[2025-07-30 11:23:26.322] [DEBUG] VirtualDesktopCapture: Mouse input request - x:550, y:300, flags:0x8004
[2025-07-30 11:23:26.322] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:550,300 -> 18773,18204
[2025-07-30 11:23:26.322] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:550, y:300, flags:0x8004
[2025-07-30 11:23:26.644] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:26.668] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 92446 bytes (1.6% ratio) in 15ms
[2025-07-30 11:23:26.670] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 92446 bytes
[2025-07-30 11:23:26.676] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 92446 bytes JPEG
[2025-07-30 11:23:28.341] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:28.341] [DEBUG] VirtualDesktopCapture: Mouse input request - x:504, y:272, flags:0x8002
[2025-07-30 11:23:28.349] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:504,272 -> 17202,16505
[2025-07-30 11:23:28.349] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:28.349] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:504, y:272, flags:0x8002
[2025-07-30 11:23:28.357] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:23:28.357] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:23:29.271] [DEBUG] VirtualDesktopCapture: Mouse input request - x:504, y:272, flags:0x8004
[2025-07-30 11:23:29.271] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:504,272 -> 17202,16505
[2025-07-30 11:23:29.271] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:504, y:272, flags:0x8004
[2025-07-30 11:23:29.351] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:29.382] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:29.382] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:23:29.404] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:23:30.337] [DEBUG] VirtualDesktopCapture: Mouse input request - x:180, y:41, flags:0x8002
[2025-07-30 11:23:30.337] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:180,41 -> 6143,2487
[2025-07-30 11:23:30.368] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:180, y:41, flags:0x8002
[2025-07-30 11:23:30.925] [DEBUG] VirtualDesktopCapture: Mouse input request - x:180, y:41, flags:0x8004
[2025-07-30 11:23:30.939] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:180,41 -> 6143,2487
[2025-07-30 11:23:30.941] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:180, y:41, flags:0x8004
[2025-07-30 11:23:30.941] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x0
[2025-07-30 11:23:30.941] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x0
[2025-07-30 11:23:31.036] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:31.084] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 15ms
[2025-07-30 11:23:31.100] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:23:31.105] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:23:31.164] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x2
[2025-07-30 11:23:31.164] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x2
[2025-07-30 11:23:32.069] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:32.119] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 31ms
[2025-07-30 11:23:32.119] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:32.128] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:32.327] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x0
[2025-07-30 11:23:32.331] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x0
[2025-07-30 11:23:32.455] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x2
[2025-07-30 11:23:32.457] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x2
[2025-07-30 11:23:33.433] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:23:33.433] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:23:33.588] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:23:33.588] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:23:34.138] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x0
[2025-07-30 11:23:34.138] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x0
[2025-07-30 11:23:34.288] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x2
[2025-07-30 11:23:34.288] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x2
[2025-07-30 11:23:35.289] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:35.304] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:35.304] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:35.320] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:36.343] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x0
[2025-07-30 11:23:36.359] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x0
[2025-07-30 11:23:36.614] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x2
[2025-07-30 11:23:36.614] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x2
[2025-07-30 11:23:37.267] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:23:37.267] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:23:37.411] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:23:37.411] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:23:38.132] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:38.134] [DEBUG] VirtualDesktopCapture: Mouse input request - x:180, y:41, flags:0x8002
[2025-07-30 11:23:38.136] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:180,41 -> 6143,2487
[2025-07-30 11:23:38.144] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 0ms
[2025-07-30 11:23:38.146] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:180, y:41, flags:0x8002
[2025-07-30 11:23:38.146] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:38.152] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:39.432] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:39.464] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 15ms
[2025-07-30 11:23:39.470] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:39.480] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:40.682] [DEBUG] VirtualDesktopCapture: Mouse input request - x:192, y:55, flags:0x8004
[2025-07-30 11:23:40.682] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:192,55 -> 6553,3337
[2025-07-30 11:23:40.685] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:192, y:55, flags:0x8004
[2025-07-30 11:23:40.685] [DEBUG] VirtualDesktopCapture: Mouse input request - x:192, y:55, flags:0x8002
[2025-07-30 11:23:40.685] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:192,55 -> 6553,3337
[2025-07-30 11:23:40.685] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:192, y:55, flags:0x8002
[2025-07-30 11:23:41.223] [DEBUG] VirtualDesktopCapture: Mouse input request - x:192, y:55, flags:0x8004
[2025-07-30 11:23:41.223] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:192,55 -> 6553,3337
[2025-07-30 11:23:41.223] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:192, y:55, flags:0x8004
[2025-07-30 11:23:41.223] [DEBUG] VirtualDesktopCapture: Mouse input request - x:189, y:87, flags:0x8002
[2025-07-30 11:23:41.223] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:189,87 -> 6451,5279
[2025-07-30 11:23:41.239] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:189, y:87, flags:0x8002
[2025-07-30 11:23:41.287] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:41.354] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 47ms
[2025-07-30 11:23:41.358] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:41.366] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:41.913] [DEBUG] VirtualDesktopCapture: Mouse input request - x:189, y:87, flags:0x8004
[2025-07-30 11:23:41.913] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:189,87 -> 6451,5279
[2025-07-30 11:23:41.924] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:189, y:87, flags:0x8004
[2025-07-30 11:23:41.928] [DEBUG] VirtualDesktopCapture: Mouse input request - x:188, y:108, flags:0x8002
[2025-07-30 11:23:41.928] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:188,108 -> 6416,6553
[2025-07-30 11:23:41.944] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:188, y:108, flags:0x8002
[2025-07-30 11:23:41.944] [DEBUG] VirtualDesktopCapture: Mouse input request - x:188, y:108, flags:0x8004
[2025-07-30 11:23:41.949] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:188,108 -> 6416,6553
[2025-07-30 11:23:41.951] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:188, y:108, flags:0x8004
[2025-07-30 11:23:43.664] [DEBUG] VirtualDesktopCapture: Mouse input request - x:175, y:82, flags:0x8002
[2025-07-30 11:23:43.664] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:175,82 -> 5973,4975
[2025-07-30 11:23:43.694] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:175, y:82, flags:0x8002
[2025-07-30 11:23:44.297] [DEBUG] VirtualDesktopCapture: Mouse input request - x:175, y:82, flags:0x8004
[2025-07-30 11:23:44.297] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:175,82 -> 5973,4975
[2025-07-30 11:23:44.297] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:175, y:82, flags:0x8004
[2025-07-30 11:23:44.411] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:44.455] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 31ms
[2025-07-30 11:23:44.455] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:23:44.475] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:23:45.879] [DEBUG] VirtualDesktopCapture: Mouse input request - x:175, y:82, flags:0x8004
[2025-07-30 11:23:45.882] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:175,82 -> 5973,4975
[2025-07-30 11:23:45.905] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:175, y:82, flags:0x8004
[2025-07-30 11:23:45.916] [DEBUG] VirtualDesktopCapture: Mouse input request - x:379, y:276, flags:0x8002
[2025-07-30 11:23:45.923] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:379,276 -> 12936,16747
[2025-07-30 11:23:45.958] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:379, y:276, flags:0x8002
[2025-07-30 11:23:46.879] [DEBUG] VirtualDesktopCapture: Mouse input request - x:379, y:276, flags:0x8004
[2025-07-30 11:23:46.882] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:379,276 -> 12936,16747
[2025-07-30 11:23:46.895] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:379, y:276, flags:0x8004
[2025-07-30 11:23:46.910] [DEBUG] VirtualDesktopCapture: Mouse input request - x:343, y:74, flags:0x8002
[2025-07-30 11:23:46.923] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:343,74 -> 11707,4490
[2025-07-30 11:23:46.958] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:343, y:74, flags:0x8002
[2025-07-30 11:23:46.974] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:47.022] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 32ms
[2025-07-30 11:23:47.051] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:47.054] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:47.915] [DEBUG] VirtualDesktopCapture: Mouse input request - x:343, y:74, flags:0x8004
[2025-07-30 11:23:47.931] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:343,74 -> 11707,4490
[2025-07-30 11:23:47.933] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:343, y:74, flags:0x8004
[2025-07-30 11:23:48.027] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:48.060] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:48.084] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:48.092] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:48.298] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:162, flags:0x8002
[2025-07-30 11:23:48.314] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,162 -> 2082,9830
[2025-07-30 11:23:48.339] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:162, flags:0x8002
[2025-07-30 11:23:49.361] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:162, flags:0x8004
[2025-07-30 11:23:49.373] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,162 -> 2082,9830
[2025-07-30 11:23:49.389] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:162, flags:0x8004
[2025-07-30 11:23:49.399] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:162, flags:0x8002
[2025-07-30 11:23:49.407] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,162 -> 2082,9830
[2025-07-30 11:23:49.452] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:162, flags:0x8002
[2025-07-30 11:23:49.484] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:49.520] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:49.541] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:23:49.556] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:23:50.714] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:162, flags:0x8004
[2025-07-30 11:23:50.723] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,162 -> 2082,9830
[2025-07-30 11:23:50.727] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:162, flags:0x8004
[2025-07-30 11:23:50.735] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:162, flags:0x8008
[2025-07-30 11:23:50.739] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,162 -> 2082,9830
[2025-07-30 11:23:50.780] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:162, flags:0x8008
[2025-07-30 11:23:50.802] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:162, flags:0x8010
[2025-07-30 11:23:50.808] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:50.818] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,162 -> 2082,9830
[2025-07-30 11:23:50.841] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:162, flags:0x8010
[2025-07-30 11:23:50.849] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:50.849] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:23:50.864] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:23:54.406] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:54.441] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:54.457] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:23:54.473] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:23:57.013] [DEBUG] VirtualDesktopCapture: Mouse input request - x:199, y:157, flags:0x8002
[2025-07-30 11:23:57.013] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:199,157 -> 6792,9526
[2025-07-30 11:23:57.027] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:199, y:157, flags:0x8002
[2025-07-30 11:23:57.614] [DEBUG] VirtualDesktopCapture: Mouse input request - x:199, y:157, flags:0x8004
[2025-07-30 11:23:57.614] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:199,157 -> 6792,9526
[2025-07-30 11:23:57.614] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:199, y:157, flags:0x8004
[2025-07-30 11:23:57.708] [DEBUG] VirtualDesktopCapture: Mouse input request - x:145, y:121, flags:0x8002
[2025-07-30 11:23:57.708] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:145,121 -> 4949,7342
[2025-07-30 11:23:57.740] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:145, y:121, flags:0x8002
[2025-07-30 11:23:57.740] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:57.792] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 15ms
[2025-07-30 11:23:57.806] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:57.820] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:58.815] [DEBUG] VirtualDesktopCapture: Mouse input request - x:145, y:121, flags:0x8004
[2025-07-30 11:23:58.831] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:145,121 -> 4949,7342
[2025-07-30 11:23:58.831] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:145, y:121, flags:0x8004
[2025-07-30 11:23:58.831] [DEBUG] VirtualDesktopCapture: Mouse input request - x:145, y:121, flags:0x8002
[2025-07-30 11:23:58.847] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:145,121 -> 4949,7342
[2025-07-30 11:23:58.847] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:145, y:121, flags:0x8002
[2025-07-30 11:23:58.910] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:58.958] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 15ms
[2025-07-30 11:23:58.977] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:58.990] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:59.658] [DEBUG] VirtualDesktopCapture: Mouse input request - x:145, y:121, flags:0x8004
[2025-07-30 11:23:59.658] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:145,121 -> 4949,7342
[2025-07-30 11:23:59.658] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:145, y:121, flags:0x8004
[2025-07-30 11:23:59.674] [DEBUG] VirtualDesktopCapture: Mouse input request - x:145, y:121, flags:0x8004
[2025-07-30 11:23:59.690] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:145,121 -> 4949,7342
[2025-07-30 11:23:59.690] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:145, y:121, flags:0x8004
[2025-07-30 11:23:59.706] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x20, flags:0x0
[2025-07-30 11:23:59.706] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x20, flags:0x0
[2025-07-30 11:23:59.786] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:23:59.786] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:23:59.802] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:23:59.818] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 16ms
[2025-07-30 11:23:59.834] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:23:59.834] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:23:59.993] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:24:00.003] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:24:00.340] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x20, flags:0x0
[2025-07-30 11:24:00.340] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x20, flags:0x0
[2025-07-30 11:24:01.021] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x64, flags:0x0
[2025-07-30 11:24:01.021] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x64, flags:0x0
[2025-07-30 11:24:01.069] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x61, flags:0x0
[2025-07-30 11:24:01.082] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x61, flags:0x0
[2025-07-30 11:24:01.290] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x66, flags:0x0
[2025-07-30 11:24:01.294] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:01.306] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x66, flags:0x0
[2025-07-30 11:24:01.322] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x61, flags:0x0
[2025-07-30 11:24:01.338] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x61, flags:0x0
[2025-07-30 11:24:01.344] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x73, flags:0x0
[2025-07-30 11:24:01.354] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 0ms
[2025-07-30 11:24:01.360] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x73, flags:0x0
[2025-07-30 11:24:01.360] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:24:01.377] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:24:01.512] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x66, flags:0x0
[2025-07-30 11:24:01.512] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x66, flags:0x0
[2025-07-30 11:24:01.645] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x61, flags:0x0
[2025-07-30 11:24:01.646] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x61, flags:0x0
[2025-07-30 11:24:01.648] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x73, flags:0x0
[2025-07-30 11:24:01.649] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x73, flags:0x0
[2025-07-30 11:24:01.791] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x64, flags:0x0
[2025-07-30 11:24:01.794] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x64, flags:0x0
[2025-07-30 11:24:01.834] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x66, flags:0x0
[2025-07-30 11:24:01.834] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x66, flags:0x0
[2025-07-30 11:24:01.881] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x61, flags:0x0
[2025-07-30 11:24:01.881] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x61, flags:0x0
[2025-07-30 11:24:01.945] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x73, flags:0x0
[2025-07-30 11:24:01.945] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x73, flags:0x0
[2025-07-30 11:24:02.072] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x66, flags:0x0
[2025-07-30 11:24:02.072] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x66, flags:0x0
[2025-07-30 11:24:02.341] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x33, flags:0x0
[2025-07-30 11:24:02.357] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x33, flags:0x0
[2025-07-30 11:24:02.611] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:02.643] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 16ms
[2025-07-30 11:24:02.643] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:24:02.659] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:24:02.707] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x33, flags:0x0
[2025-07-30 11:24:02.707] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x33, flags:0x0
[2025-07-30 11:24:02.966] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x33, flags:0x0
[2025-07-30 11:24:02.967] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x33, flags:0x0
[2025-07-30 11:24:03.181] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x33, flags:0x0
[2025-07-30 11:24:03.195] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x33, flags:0x0
[2025-07-30 11:24:03.459] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x72, flags:0x0
[2025-07-30 11:24:03.467] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x72, flags:0x0
[2025-07-30 11:24:03.621] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x77, flags:0x0
[2025-07-30 11:24:03.622] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x77, flags:0x0
[2025-07-30 11:24:03.670] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x72, flags:0x0
[2025-07-30 11:24:03.686] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x72, flags:0x0
[2025-07-30 11:24:03.846] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x77, flags:0x0
[2025-07-30 11:24:03.846] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x77, flags:0x0
[2025-07-30 11:24:03.894] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x65, flags:0x0
[2025-07-30 11:24:03.894] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x65, flags:0x0
[2025-07-30 11:24:03.957] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x72, flags:0x0
[2025-07-30 11:24:03.973] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x72, flags:0x0
[2025-07-30 11:24:04.226] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x65, flags:0x0
[2025-07-30 11:24:04.226] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x65, flags:0x0
[2025-07-30 11:24:04.242] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x77, flags:0x0
[2025-07-30 11:24:04.259] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x77, flags:0x0
[2025-07-30 11:24:04.383] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:04.415] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 15ms
[2025-07-30 11:24:04.417] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:24:04.433] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:24:04.487] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x77, flags:0x0
[2025-07-30 11:24:04.495] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x77, flags:0x0
[2025-07-30 11:24:04.591] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x65, flags:0x0
[2025-07-30 11:24:04.591] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x65, flags:0x0
[2025-07-30 11:24:07.500] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:07.533] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 15ms
[2025-07-30 11:24:07.537] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:24:07.543] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:24:11.735] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:11.750] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 16ms
[2025-07-30 11:24:11.766] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:24:11.766] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:24:13.187] [INFO] Server requested switch to main desktop
[2025-07-30 11:24:13.187] [INFO] VirtualDesktopCapture: Switching to main desktop
[2025-07-30 11:24:13.187] [INFO] VirtualDesktopCapture: Switching to main desktop using Win+Ctrl+Left
[2025-07-30 11:24:14.279] [INFO] VirtualDesktopCapture: Main desktop switch completed
[2025-07-30 11:24:14.279] [INFO] Switched to main desktop on server request
[2025-07-30 11:24:15.818] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:15.849] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 16ms
[2025-07-30 11:24:15.849] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:24:15.865] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:24:18.089] [INFO] Server requested switch to virtual desktop
[2025-07-30 11:24:18.097] [INFO] VirtualDesktopCapture: Switching desktop using Win+Ctrl+Right
[2025-07-30 11:24:19.147] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:19.179] [INFO] Switched to virtual desktop on server request
[2025-07-30 11:24:19.191] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94278 bytes (1.6% ratio) in 32ms
[2025-07-30 11:24:19.195] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94278 bytes
[2025-07-30 11:24:19.202] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94278 bytes JPEG
[2025-07-30 11:24:20.856] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:20.958] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 94206 bytes (1.6% ratio) in 15ms
[2025-07-30 11:24:20.960] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 94206 bytes
[2025-07-30 11:24:20.968] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 94206 bytes JPEG
[2025-07-30 11:24:21.910] [INFO] Server requested virtual desktop creation
[2025-07-30 11:24:21.910] [INFO] VirtualDesktopCapture: Creating virtual desktop on demand
[2025-07-30 11:24:21.910] [INFO] VirtualDesktopCapture: Creating target virtual desktop for capture
[2025-07-30 11:24:21.910] [INFO] VirtualDesktopCapture: Using keyboard simulation fallback
[2025-07-30 11:24:21.910] [INFO] VirtualDesktopCapture: Creating desktop using Win+Ctrl+D
[2025-07-30 11:24:24.004] [INFO] VirtualDesktopCapture: Virtual desktop created via keyboard - client stays on original, server targets virtual
[2025-07-30 11:24:24.004] [INFO] VirtualDesktopCapture: Virtual desktop created successfully on demand
[2025-07-30 11:24:24.004] [INFO] Virtual desktop created successfully on server request
[2025-07-30 11:24:25.791] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:25.827] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 350536 bytes (6.0% ratio) in 31ms
[2025-07-30 11:24:25.827] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 350536 bytes
[2025-07-30 11:24:25.843] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 350536 bytes JPEG
[2025-07-30 11:24:28.612] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:24:28.619] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:24:29.294] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:29.330] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 352764 bytes (6.1% ratio) in 31ms
[2025-07-30 11:24:29.330] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 352764 bytes
[2025-07-30 11:24:29.336] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 352764 bytes JPEG
[2025-07-30 11:24:30.301] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:24:30.301] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:24:31.006] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:31.042] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 352436 bytes (6.1% ratio) in 31ms
[2025-07-30 11:24:31.048] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 352436 bytes
[2025-07-30 11:24:31.053] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 352436 bytes JPEG
[2025-07-30 11:24:31.878] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:24:31.878] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:24:32.212] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:32.260] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 355141 bytes (6.1% ratio) in 16ms
[2025-07-30 11:24:32.260] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 355141 bytes
[2025-07-30 11:24:32.272] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 355141 bytes JPEG
[2025-07-30 11:24:33.470] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x0
[2025-07-30 11:24:33.470] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x0
[2025-07-30 11:24:33.628] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x2
[2025-07-30 11:24:33.628] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x2
[2025-07-30 11:24:34.625] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:34.657] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 39607 bytes (0.7% ratio) in 31ms
[2025-07-30 11:24:34.657] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 39607 bytes
[2025-07-30 11:24:34.675] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 39607 bytes JPEG
[2025-07-30 11:24:36.338] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:36.369] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 56356 bytes (1.0% ratio) in 15ms
[2025-07-30 11:24:36.372] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 56356 bytes
[2025-07-30 11:24:36.372] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 56356 bytes JPEG
[2025-07-30 11:24:38.918] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:38.953] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 56356 bytes (1.0% ratio) in 31ms
[2025-07-30 11:24:38.953] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 56356 bytes
[2025-07-30 11:24:38.964] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 56356 bytes JPEG
[2025-07-30 11:24:41.475] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:41.519] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 59399 bytes (1.0% ratio) in 32ms
[2025-07-30 11:24:41.524] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 59399 bytes
[2025-07-30 11:24:41.529] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 59399 bytes JPEG
[2025-07-30 11:24:42.844] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x0
[2025-07-30 11:24:42.844] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x0
[2025-07-30 11:24:42.966] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x2
[2025-07-30 11:24:42.974] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x2
[2025-07-30 11:24:45.217] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:45.249] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 59605 bytes (1.0% ratio) in 31ms
[2025-07-30 11:24:45.252] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 59605 bytes
[2025-07-30 11:24:45.254] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 59605 bytes JPEG
[2025-07-30 11:24:46.733] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:46.778] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 73717 bytes (1.3% ratio) in 31ms
[2025-07-30 11:24:46.781] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 73717 bytes
[2025-07-30 11:24:46.781] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 73717 bytes JPEG
[2025-07-30 11:24:48.577] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:48.611] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 73717 bytes (1.3% ratio) in 31ms
[2025-07-30 11:24:48.611] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 73717 bytes
[2025-07-30 11:24:48.628] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 73717 bytes JPEG
[2025-07-30 11:24:50.091] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:50.138] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 75910 bytes (1.3% ratio) in 47ms
[2025-07-30 11:24:50.138] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 75910 bytes
[2025-07-30 11:24:50.154] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 75910 bytes JPEG
[2025-07-30 11:24:51.517] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:51.544] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 75910 bytes (1.3% ratio) in 16ms
[2025-07-30 11:24:51.544] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 75910 bytes
[2025-07-30 11:24:51.560] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 75910 bytes JPEG
[2025-07-30 11:24:53.061] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x0
[2025-07-30 11:24:53.061] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x0
[2025-07-30 11:24:53.280] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x2
[2025-07-30 11:24:53.280] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x2
[2025-07-30 11:24:53.451] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:53.482] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 75910 bytes (1.3% ratio) in 15ms
[2025-07-30 11:24:53.484] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 75910 bytes
[2025-07-30 11:24:53.488] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 75910 bytes JPEG
[2025-07-30 11:24:54.058] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:24:54.070] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:24:54.360] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:24:54.360] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:24:54.599] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x0
[2025-07-30 11:24:54.599] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x0
[2025-07-30 11:24:54.867] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x2
[2025-07-30 11:24:54.867] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x2
[2025-07-30 11:24:55.816] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:55.843] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 75910 bytes (1.3% ratio) in 16ms
[2025-07-30 11:24:55.847] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 75910 bytes
[2025-07-30 11:24:55.851] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 75910 bytes JPEG
[2025-07-30 11:24:57.839] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:24:57.879] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 76611 bytes (1.3% ratio) in 16ms
[2025-07-30 11:24:57.881] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 76611 bytes
[2025-07-30 11:24:57.886] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 76611 bytes JPEG
[2025-07-30 11:25:01.390] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:01.392] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 76840 bytes (1.3% ratio) in 0ms
[2025-07-30 11:25:01.392] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 76840 bytes
[2025-07-30 11:25:01.406] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 76840 bytes JPEG
[2025-07-30 11:25:04.455] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:04.483] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 62759 bytes (1.1% ratio) in 31ms
[2025-07-30 11:25:04.483] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 62759 bytes
[2025-07-30 11:25:04.492] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 62759 bytes JPEG
[2025-07-30 11:25:08.481] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:25:08.481] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:25:08.690] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:25:08.690] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:25:08.817] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:25:08.817] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:25:09.168] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:25:09.168] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:25:10.637] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:25:10.639] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:25:11.042] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:25:11.058] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:25:11.058] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:25:11.058] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:25:11.106] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:25:11.114] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:25:11.255] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:25:11.259] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:25:11.503] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:25:11.503] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:25:11.630] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:11.662] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 77124 bytes (1.3% ratio) in 15ms
[2025-07-30 11:25:11.678] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 77124 bytes
[2025-07-30 11:25:11.678] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 77124 bytes JPEG
[2025-07-30 11:25:11.694] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:25:11.703] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:25:11.824] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:25:11.828] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:25:12.948] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:12.993] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 50680 bytes (0.9% ratio) in 15ms
[2025-07-30 11:25:13.001] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 50680 bytes
[2025-07-30 11:25:13.017] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 50680 bytes JPEG
[2025-07-30 11:25:13.043] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:25:13.043] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:25:13.285] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:25:13.288] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:25:15.139] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:15.149] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 50680 bytes (0.9% ratio) in 0ms
[2025-07-30 11:25:15.149] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 50680 bytes
[2025-07-30 11:25:15.153] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 50680 bytes JPEG
[2025-07-30 11:25:19.200] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:19.200] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 50680 bytes (0.9% ratio) in 0ms
[2025-07-30 11:25:19.200] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 50680 bytes
[2025-07-30 11:25:19.216] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 50680 bytes JPEG
[2025-07-30 11:25:24.160] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:24.192] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 50680 bytes (0.9% ratio) in 16ms
[2025-07-30 11:25:24.207] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 50680 bytes
[2025-07-30 11:25:24.207] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 50680 bytes JPEG
[2025-07-30 11:25:28.226] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:28.226] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 50680 bytes (0.9% ratio) in 0ms
[2025-07-30 11:25:28.226] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 50680 bytes
[2025-07-30 11:25:28.245] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 50680 bytes JPEG
[2025-07-30 11:25:33.210] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:33.242] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 54870 bytes (0.9% ratio) in 15ms
[2025-07-30 11:25:33.252] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 54870 bytes
[2025-07-30 11:25:33.259] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 54870 bytes JPEG
[2025-07-30 11:25:40.184] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:40.184] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 54870 bytes (0.9% ratio) in 0ms
[2025-07-30 11:25:40.184] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 54870 bytes
[2025-07-30 11:25:40.200] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 54870 bytes JPEG
[2025-07-30 11:25:51.468] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:51.497] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 56489 bytes (1.0% ratio) in 31ms
[2025-07-30 11:25:51.503] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 56489 bytes
[2025-07-30 11:25:51.507] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 56489 bytes JPEG
[2025-07-30 11:25:51.810] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:51.826] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 0ms
[2025-07-30 11:25:51.826] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:51.839] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:52.059] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:52.068] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 0ms
[2025-07-30 11:25:52.070] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:52.074] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:52.413] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:52.427] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 0ms
[2025-07-30 11:25:52.427] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:52.443] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:52.980] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:53.008] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 16ms
[2025-07-30 11:25:53.008] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:53.008] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:53.425] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:53.438] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 16ms
[2025-07-30 11:25:53.438] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:53.442] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:53.710] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:53.740] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 15ms
[2025-07-30 11:25:53.740] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:53.748] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:54.010] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:54.028] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 31ms
[2025-07-30 11:25:54.028] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:54.041] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:54.411] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:54.428] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 16ms
[2025-07-30 11:25:54.428] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:54.428] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:55.211] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:55.270] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 31347 bytes (0.5% ratio) in 16ms
[2025-07-30 11:25:55.274] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 31347 bytes
[2025-07-30 11:25:55.280] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 31347 bytes JPEG
[2025-07-30 11:25:55.644] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:55.671] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 351109 bytes (6.0% ratio) in 16ms
[2025-07-30 11:25:55.673] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 351109 bytes
[2025-07-30 11:25:55.679] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 351109 bytes JPEG
[2025-07-30 11:25:57.429] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:57.449] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362062 bytes (6.2% ratio) in 15ms
[2025-07-30 11:25:57.451] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362062 bytes
[2025-07-30 11:25:57.456] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362062 bytes JPEG
[2025-07-30 11:25:58.175] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:58.187] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359286 bytes (6.2% ratio) in 16ms
[2025-07-30 11:25:58.189] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359286 bytes
[2025-07-30 11:25:58.193] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359286 bytes JPEG
[2025-07-30 11:25:58.537] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:58.537] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 0ms
[2025-07-30 11:25:58.537] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:25:58.552] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:25:58.759] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:58.781] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 15ms
[2025-07-30 11:25:58.781] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:25:58.787] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:25:59.251] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:59.283] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 15ms
[2025-07-30 11:25:59.283] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:25:59.297] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:25:59.606] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:59.637] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 32ms
[2025-07-30 11:25:59.637] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:25:59.637] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:25:59.952] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:25:59.968] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 0ms
[2025-07-30 11:25:59.968] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:25:59.984] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:26:00.258] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:00.437] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 32ms
[2025-07-30 11:26:00.440] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:26:00.446] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:26:00.782] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:00.787] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:00.787] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:26:00.799] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:26:01.100] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:01.106] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 15ms
[2025-07-30 11:26:01.106] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:26:01.116] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:26:02.929] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:02.963] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 15ms
[2025-07-30 11:26:02.963] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:26:02.979] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:26:03.350] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:03.382] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:03.398] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:26:03.404] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:26:03.739] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:03.778] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:03.778] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:26:03.787] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:26:04.244] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:04.250] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:04.250] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:26:04.250] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:26:04.303] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:26:04.305] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:26:04.562] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:04.573] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 360349 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:04.575] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 360349 bytes
[2025-07-30 11:26:04.575] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 360349 bytes JPEG
[2025-07-30 11:26:04.816] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:04.847] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358401 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:04.847] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358401 bytes
[2025-07-30 11:26:04.862] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358401 bytes JPEG
[2025-07-30 11:26:05.483] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:05.498] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358401 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:05.498] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358401 bytes
[2025-07-30 11:26:05.498] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358401 bytes JPEG
[2025-07-30 11:26:05.796] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:05.829] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362972 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:05.829] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362972 bytes
[2025-07-30 11:26:05.829] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362972 bytes JPEG
[2025-07-30 11:26:06.099] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:06.119] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363048 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:06.119] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363048 bytes
[2025-07-30 11:26:06.128] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363048 bytes JPEG
[2025-07-30 11:26:06.210] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x0
[2025-07-30 11:26:06.219] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x0
[2025-07-30 11:26:06.355] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x2
[2025-07-30 11:26:06.359] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x2
[2025-07-30 11:26:06.419] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:06.435] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363048 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:06.435] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363048 bytes
[2025-07-30 11:26:06.448] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363048 bytes JPEG
[2025-07-30 11:26:06.863] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:06.888] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 48485 bytes (0.8% ratio) in 32ms
[2025-07-30 11:26:06.891] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 48485 bytes
[2025-07-30 11:26:06.897] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 48485 bytes JPEG
[2025-07-30 11:26:07.313] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:07.329] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131163 bytes (2.3% ratio) in 15ms
[2025-07-30 11:26:07.329] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131163 bytes
[2025-07-30 11:26:07.345] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131163 bytes JPEG
[2025-07-30 11:26:08.030] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:08.077] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131214 bytes (2.3% ratio) in 31ms
[2025-07-30 11:26:08.077] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131214 bytes
[2025-07-30 11:26:08.091] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131214 bytes JPEG
[2025-07-30 11:26:08.778] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:08.794] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131163 bytes (2.3% ratio) in 0ms
[2025-07-30 11:26:08.809] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131163 bytes
[2025-07-30 11:26:08.817] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131163 bytes JPEG
[2025-07-30 11:26:09.146] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:09.163] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131214 bytes (2.3% ratio) in 15ms
[2025-07-30 11:26:09.163] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131214 bytes
[2025-07-30 11:26:09.179] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131214 bytes JPEG
[2025-07-30 11:26:09.513] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:09.528] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131163 bytes (2.3% ratio) in 15ms
[2025-07-30 11:26:09.528] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131163 bytes
[2025-07-30 11:26:09.544] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131163 bytes JPEG
[2025-07-30 11:26:09.878] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:09.903] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131163 bytes (2.3% ratio) in 15ms
[2025-07-30 11:26:09.905] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131163 bytes
[2025-07-30 11:26:09.909] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131163 bytes JPEG
[2025-07-30 11:26:10.195] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:10.211] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131214 bytes (2.3% ratio) in 0ms
[2025-07-30 11:26:10.211] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131214 bytes
[2025-07-30 11:26:10.224] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131214 bytes JPEG
[2025-07-30 11:26:10.495] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:10.527] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131163 bytes (2.3% ratio) in 15ms
[2025-07-30 11:26:10.539] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131163 bytes
[2025-07-30 11:26:10.545] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131163 bytes JPEG
[2025-07-30 11:26:10.941] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:10.977] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131163 bytes (2.3% ratio) in 16ms
[2025-07-30 11:26:10.981] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131163 bytes
[2025-07-30 11:26:10.987] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131163 bytes JPEG
[2025-07-30 11:26:11.635] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1902, y:16, flags:0x8002
[2025-07-30 11:26:11.637] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1902,16 -> 64920,970
[2025-07-30 11:26:11.646] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1902, y:16, flags:0x8002
[2025-07-30 11:26:11.757] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:11.762] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 129164 bytes (2.2% ratio) in 16ms
[2025-07-30 11:26:11.762] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 129164 bytes
[2025-07-30 11:26:11.762] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 129164 bytes JPEG
[2025-07-30 11:26:11.793] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1902, y:16, flags:0x8004
[2025-07-30 11:26:11.793] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1902,16 -> 64920,970
[2025-07-30 11:26:11.793] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1902, y:16, flags:0x8004
[2025-07-30 11:26:12.091] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:12.136] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358401 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:12.139] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358401 bytes
[2025-07-30 11:26:12.139] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358401 bytes JPEG
[2025-07-30 11:26:13.732] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:13.753] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359250 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:13.753] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359250 bytes
[2025-07-30 11:26:13.757] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359250 bytes JPEG
[2025-07-30 11:26:14.058] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:14.100] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358401 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:14.100] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358401 bytes
[2025-07-30 11:26:14.110] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358401 bytes JPEG
[2025-07-30 11:26:14.872] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:14.919] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358401 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:14.919] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358401 bytes
[2025-07-30 11:26:14.935] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358401 bytes JPEG
[2025-07-30 11:26:15.336] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:15.480] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363166 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:15.496] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363166 bytes
[2025-07-30 11:26:15.501] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363166 bytes JPEG
[2025-07-30 11:26:15.514] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:26:15.519] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:26:15.662] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:26:15.666] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:26:15.838] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:15.886] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359829 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:15.886] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359829 bytes
[2025-07-30 11:26:15.886] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359829 bytes JPEG
[2025-07-30 11:26:16.207] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:16.223] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358914 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:16.234] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358914 bytes
[2025-07-30 11:26:16.234] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358914 bytes JPEG
[2025-07-30 11:26:16.411] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:26:16.415] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:26:16.534] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:16.542] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 364593 bytes (6.3% ratio) in 0ms
[2025-07-30 11:26:16.542] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 364593 bytes
[2025-07-30 11:26:16.553] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 364593 bytes JPEG
[2025-07-30 11:26:16.584] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:26:16.584] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:26:16.838] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:16.869] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362539 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:16.869] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362539 bytes
[2025-07-30 11:26:16.888] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362539 bytes JPEG
[2025-07-30 11:26:16.901] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:26:16.901] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:26:17.081] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:26:17.086] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:26:17.287] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:17.304] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358469 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:17.304] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358469 bytes
[2025-07-30 11:26:17.319] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358469 bytes JPEG
[2025-07-30 11:26:19.298] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:26:19.314] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:26:19.456] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:26:19.464] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:26:19.586] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:19.602] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358752 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:19.602] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358752 bytes
[2025-07-30 11:26:19.618] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358752 bytes JPEG
[2025-07-30 11:26:19.940] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:19.981] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:19.981] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:26:19.981] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:26:20.285] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:20.299] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:20.301] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:26:20.301] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:26:20.473] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x5C, flags:0x0
[2025-07-30 11:26:20.473] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x5C, flags:0x0
[2025-07-30 11:26:20.585] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:20.586] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 361543 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:20.586] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 361543 bytes
[2025-07-30 11:26:20.586] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 361543 bytes JPEG
[2025-07-30 11:26:20.818] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:20.842] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:20.842] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:26:20.850] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:26:21.094] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:26:21.097] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:26:21.152] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:21.212] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:21.216] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:26:21.222] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:26:21.379] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:26:21.379] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:26:21.564] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:21.568] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:21.568] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:26:21.580] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:26:21.928] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:21.968] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:21.968] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:21.978] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:22.315] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:22.315] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:22.315] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:22.331] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:22.652] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:22.668] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:22.668] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:22.681] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:23.032] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:23.080] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:23.080] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:23.090] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:23.465] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:23.512] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 47ms
[2025-07-30 11:26:23.512] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:23.528] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:23.747] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:26:23.753] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:26:23.867] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:23.883] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:23.883] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:23.883] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:24.157] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:26:24.162] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:26:24.216] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:24.232] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:24.232] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:24.243] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:24.499] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:24.515] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:24.515] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:24.527] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:24.866] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:24.882] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:24.882] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:24.882] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:25.001] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:26:25.001] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:26:25.150] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:25.180] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:25.180] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:25.180] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:25.228] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:26:25.228] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:26:25.528] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:25.540] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:25.540] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:25.545] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:25.797] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:26:25.797] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:26:25.861] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:25.861] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:25.861] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:25.877] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:26.061] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:26:26.066] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:26:26.196] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:26.228] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:26.228] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:26.246] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:26.614] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:26.629] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:26.629] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:26.645] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:27.379] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:27.395] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:27.395] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:27.410] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:27.726] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:27.737] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:27.739] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:27.743] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:28.081] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:28.097] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:28.097] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:28.097] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:28.371] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:28.378] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:28.378] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:28.394] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:28.662] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:28.677] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x0
[2025-07-30 11:26:28.677] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x0
[2025-07-30 11:26:28.689] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 32ms
[2025-07-30 11:26:28.693] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:28.693] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:28.907] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x2
[2025-07-30 11:26:28.910] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x2
[2025-07-30 11:26:29.057] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:29.071] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:29.073] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:29.077] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:29.395] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:29.418] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:29.422] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:29.426] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:29.761] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:29.792] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:29.792] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:29.792] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:30.078] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:30.094] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:30.094] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:30.111] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:30.460] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:30.515] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 47ms
[2025-07-30 11:26:30.517] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:30.524] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:32.040] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:32.072] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:32.072] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:32.072] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:32.464] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:32.502] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:32.504] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:32.510] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:33.021] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:33.065] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 32ms
[2025-07-30 11:26:33.065] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:33.069] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:33.422] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:33.458] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 15ms
[2025-07-30 11:26:33.461] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:33.465] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:33.787] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:33.823] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:33.823] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:33.832] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:34.141] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:34.167] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:34.167] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:34.173] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:34.428] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:34.446] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:34.446] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:34.446] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:34.721] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:34.868] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:35.007] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:35.007] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:35.369] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:35.372] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:35.372] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:35.388] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:35.653] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:35.657] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:35.657] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:35.671] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:36.071] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:36.087] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:36.087] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:36.104] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:36.496] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:36.540] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:36.540] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:36.547] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:37.134] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:37.142] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:37.142] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:37.153] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:37.453] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:37.482] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 15ms
[2025-07-30 11:26:37.482] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:37.498] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:37.779] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:37.785] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:37.785] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:37.801] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:38.149] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:38.149] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:38.165] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:38.169] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:38.502] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:38.564] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 47ms
[2025-07-30 11:26:38.568] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:38.574] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:38.933] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:38.935] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:38.935] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:38.950] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:39.268] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:39.300] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:39.300] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:39.312] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:39.599] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:39.602] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:39.602] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:39.618] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:39.936] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:39.952] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:39.952] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:39.965] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:40.285] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:40.300] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:40.300] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:40.316] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:40.649] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:40.665] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:40.665] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:40.681] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:40.951] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:40.967] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:40.967] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:40.983] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:41.249] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:41.296] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 47ms
[2025-07-30 11:26:41.296] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:41.296] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:41.662] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:41.664] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:41.677] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:41.677] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:41.949] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:41.982] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 46ms
[2025-07-30 11:26:41.982] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:41.990] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:42.502] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:42.545] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:42.551] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:42.559] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:42.931] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:42.983] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 46ms
[2025-07-30 11:26:42.995] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:42.995] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:43.296] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:43.328] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:43.328] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:43.340] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:43.678] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:43.685] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:43.685] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:43.696] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:45.098] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:45.114] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:45.114] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:45.130] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:45.548] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:45.564] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:45.564] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:45.564] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:45.861] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:45.893] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:45.893] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:45.902] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:46.227] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:46.259] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 16ms
[2025-07-30 11:26:46.259] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:46.259] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:46.627] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:46.631] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:46.631] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:46.645] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:46.961] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:46.977] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:46.977] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:46.993] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:47.328] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:47.344] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:47.344] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:47.359] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:47.720] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:47.725] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:47.725] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:47.741] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:48.106] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:48.112] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:48.112] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:48.112] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:48.709] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:48.709] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:48.709] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:48.727] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:49.058] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:49.094] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:49.094] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:49.094] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:49.374] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:49.377] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:49.377] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:49.394] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:49.647] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:49.663] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:49.663] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:49.675] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:49.926] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:49.948] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:49.948] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:49.958] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:50.258] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:50.294] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:50.294] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:50.306] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:50.662] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:50.676] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:50.676] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:50.676] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:50.975] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:51.007] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:51.007] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:51.007] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:51.323] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:51.338] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 15ms
[2025-07-30 11:26:51.338] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:51.344] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:51.797] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:51.817] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:51.821] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:51.823] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:52.132] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:52.142] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:52.142] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:52.142] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:52.458] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:52.474] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:52.474] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:52.474] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:52.823] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:52.857] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:52.857] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:26:52.862] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:26:53.141] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:53.173] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 360106 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:53.173] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 360106 bytes
[2025-07-30 11:26:53.173] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 360106 bytes JPEG
[2025-07-30 11:26:53.603] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:53.635] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:53.635] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:26:53.642] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:26:53.920] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:53.941] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:53.941] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:26:53.954] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:26:54.272] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:54.289] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:54.301] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:26:54.304] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:26:54.671] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:54.705] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 360854 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:54.709] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 360854 bytes
[2025-07-30 11:26:54.713] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 360854 bytes JPEG
[2025-07-30 11:26:55.009] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:55.021] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:55.021] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:55.036] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:55.306] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:55.321] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:55.321] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:55.337] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:55.619] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:55.621] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:55.621] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:55.621] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:55.920] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:55.936] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:55.936] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:55.952] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:57.470] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:57.479] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:57.479] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:57.486] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:57.970] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:57.991] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:57.991] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:58.001] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:58.317] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:58.349] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 31ms
[2025-07-30 11:26:58.349] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:58.365] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:58.733] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:58.765] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 32ms
[2025-07-30 11:26:58.765] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:58.773] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:59.119] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:59.135] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:59.135] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:59.135] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:59.467] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:59.483] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:59.483] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:59.499] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:26:59.812] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:26:59.818] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:26:59.818] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:26:59.818] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:00.118] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:00.133] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:00.133] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:00.149] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:00.468] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:00.484] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:00.484] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:00.500] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:00.834] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:00.850] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:00.850] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:00.861] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:01.186] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:01.200] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:01.200] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:01.214] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:01.547] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:01.595] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:01.595] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:01.613] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:01.993] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:02.029] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:02.029] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:02.049] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:02.432] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:02.448] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:02.448] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:02.463] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:02.829] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:02.861] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:02.861] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:02.861] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:03.196] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:03.228] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:03.228] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:03.237] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:03.580] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:03.596] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:03.596] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:03.611] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:03.870] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:03.880] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:03.880] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:03.896] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:04.183] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:04.220] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:04.220] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:04.232] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:04.679] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:04.727] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:04.727] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:04.743] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:05.096] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:05.121] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:05.121] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:05.127] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:05.431] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:05.447] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:05.447] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:05.447] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:05.730] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:05.746] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:05.746] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:05.760] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:06.227] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:06.244] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:06.244] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:06.260] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:06.529] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:06.554] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:06.557] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:06.561] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:06.845] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:06.861] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:06.877] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:06.885] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:07.211] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:07.227] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:07.242] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:07.249] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:07.562] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:07.577] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:07.577] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:07.595] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:07.877] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:07.893] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:07.909] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:07.909] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:08.212] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:08.227] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:08.227] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:08.243] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:08.523] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:08.527] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:08.527] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:08.544] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:11.422] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:11.454] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:11.454] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:11.454] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:11.807] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:11.824] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:11.824] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:11.837] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:12.137] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:12.140] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:12.140] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:12.156] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:12.656] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:12.667] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:12.669] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:12.672] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:12.923] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:12.939] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:12.939] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:12.939] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:13.223] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:13.239] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:13.239] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:13.254] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:13.522] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:13.542] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:13.542] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:13.553] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:13.805] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:13.821] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:13.821] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:13.838] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:14.106] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:14.125] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:14.125] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:14.125] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:14.406] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:14.438] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 32ms
[2025-07-30 11:27:14.441] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:14.444] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:14.741] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:14.754] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:14.754] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:14.772] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:15.055] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:15.073] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:15.073] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:15.073] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:15.354] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:15.370] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:15.370] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:15.386] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:15.685] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:15.694] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:15.694] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:15.703] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:16.002] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:16.034] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:16.042] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:16.050] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:16.416] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:16.429] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:16.429] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:16.434] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:16.752] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:16.783] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:16.783] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:16.783] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:17.104] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:17.119] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:17.119] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:17.135] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:17.518] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:17.549] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:17.549] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:17.549] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:17.901] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:18.061] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 32ms
[2025-07-30 11:27:18.188] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:18.204] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:18.500] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:18.509] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:18.516] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:18.516] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:18.817] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:18.832] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:18.832] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:18.848] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:19.100] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:19.123] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:19.123] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:19.132] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:19.418] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:19.450] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:19.450] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:19.450] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:19.717] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:19.734] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:19.734] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:19.749] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:20.064] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:20.096] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:20.096] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:20.096] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:20.482] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:20.514] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 32ms
[2025-07-30 11:27:20.514] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:20.514] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:20.864] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:20.896] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:20.896] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:20.912] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:21.281] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:21.312] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:21.312] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:21.322] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:21.671] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:21.681] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:21.681] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:21.696] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:22.063] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:22.063] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:22.077] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:22.082] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:23.678] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:23.690] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:23.690] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:23.694] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:24.030] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:24.046] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:24.046] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:24.059] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:24.392] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:24.396] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:24.396] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:24.396] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:24.776] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:24.776] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:24.776] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:24.792] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:25.130] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:25.145] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:25.145] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:25.162] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:25.447] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:25.462] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:25.462] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:25.462] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:25.782] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:25.794] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:25.794] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:25.807] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:26.129] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:26.144] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:26.144] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:26.162] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:26.527] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:26.590] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 47ms
[2025-07-30 11:27:26.592] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:26.601] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:26.963] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:26.977] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:26.977] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:26.989] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:27.310] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:27.326] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362969 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:27.326] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362969 bytes
[2025-07-30 11:27:27.341] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362969 bytes JPEG
[2025-07-30 11:27:27.664] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:27.677] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:27.677] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:27.677] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:28.112] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:28.133] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:28.133] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:28.138] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:28.423] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:28.426] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:28.426] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:28.426] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:28.764] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:28.789] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:28.800] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:28.805] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:29.210] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:29.238] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:29.238] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:29.254] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:29.526] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:29.542] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:29.542] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:29.554] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:29.792] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:29.808] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:29.808] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:29.808] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:30.168] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:30.183] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:30.187] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:30.190] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:30.511] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:30.534] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:30.536] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:30.536] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:30.872] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:30.904] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:30.904] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:30.920] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:31.371] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:31.387] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 32ms
[2025-07-30 11:27:31.387] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:31.403] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:31.837] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:31.886] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:31.886] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:31.896] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:32.257] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:32.273] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:32.273] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:32.289] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:32.607] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:32.623] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:32.623] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:32.623] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:32.923] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:32.955] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:32.955] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:32.962] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:33.269] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:33.276] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:33.276] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:33.291] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:33.606] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:33.622] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:33.622] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:33.622] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:33.969] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:33.976] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:33.976] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:33.987] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:34.421] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:34.437] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:34.437] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:34.453] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:34.836] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:34.884] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:34.884] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:34.900] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:36.285] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:36.301] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:36.301] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:36.317] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:36.598] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:36.610] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:36.610] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:36.617] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:36.882] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:36.893] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:36.893] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:36.900] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:37.201] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:37.232] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:37.239] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:37.243] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:37.586] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:37.730] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:37.905] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:37.909] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:38.300] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:38.314] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:38.330] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:38.330] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:38.629] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:38.634] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:38.646] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:38.646] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:38.966] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:38.985] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:38.985] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:38.998] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:39.317] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:39.333] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:39.333] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:39.348] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:39.685] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:39.700] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:39.700] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:39.700] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:40.065] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:40.097] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:40.097] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:40.097] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:40.566] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:40.586] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:40.586] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:40.586] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:40.916] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:40.932] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:40.943] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:40.943] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:41.311] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:41.324] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:41.324] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:41.331] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:41.665] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:41.688] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:41.688] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:41.694] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:42.026] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:42.029] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:42.029] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:42.045] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:42.390] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:42.428] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:42.428] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:42.446] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:42.791] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:42.796] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:42.796] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:42.796] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:43.096] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:43.121] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:43.121] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:43.127] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:43.446] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:43.462] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:43.462] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:43.478] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:43.846] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:43.862] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:43.862] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:43.876] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:44.195] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:44.211] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:44.211] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:44.227] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:44.526] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:44.558] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:44.558] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:44.558] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:44.924] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:44.924] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:44.940] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:44.940] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:45.228] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:45.243] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:45.243] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:45.259] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:45.579] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:45.595] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:45.595] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:45.611] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:45.909] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:45.925] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:45.925] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:45.940] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:46.214] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:46.234] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:46.234] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:46.242] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:46.543] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:46.575] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:46.575] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:46.582] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:46.927] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:46.948] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:46.948] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:46.958] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:47.310] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:47.326] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:47.326] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:47.341] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:48.775] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:48.800] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:48.800] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:48.808] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:49.089] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:49.101] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:49.101] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:49.107] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:49.374] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:49.401] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:49.401] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:49.406] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:49.757] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:49.773] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:49.773] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:49.790] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:50.247] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:50.279] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:50.279] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:50.298] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:50.707] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:50.738] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:50.738] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:50.744] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:51.123] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:51.140] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:51.140] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:51.156] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:51.507] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:51.521] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:51.521] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:51.535] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:51.856] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:51.872] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:51.872] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:51.888] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:52.174] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:52.190] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:52.190] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:52.204] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:52.488] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:52.520] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 32ms
[2025-07-30 11:27:52.520] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:52.520] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:52.892] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:52.910] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:52.910] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:52.919] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:53.238] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:53.254] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:53.254] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:53.269] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:53.935] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:53.937] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:53.937] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:53.953] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:54.239] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:54.253] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:54.253] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:54.269] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:54.587] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:54.613] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:54.613] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:54.613] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:55.034] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:55.034] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:55.034] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:55.051] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:55.370] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:55.515] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 32ms
[2025-07-30 11:27:55.656] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:55.673] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:56.229] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:56.236] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:56.236] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:56.252] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:56.570] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:56.592] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 16ms
[2025-07-30 11:27:56.592] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:56.592] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:56.883] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:56.918] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:56.918] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:56.918] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:57.282] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:57.319] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 32ms
[2025-07-30 11:27:57.319] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:57.330] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:57.666] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:57.713] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:27:57.713] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:27:57.719] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:27:58.067] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:58.067] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 356256 bytes (6.1% ratio) in 0ms
[2025-07-30 11:27:58.083] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 356256 bytes
[2025-07-30 11:27:58.084] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 356256 bytes JPEG
[2025-07-30 11:27:58.400] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:58.407] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:58.407] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:27:58.419] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:27:58.780] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:58.789] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:58.789] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:27:58.796] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:27:59.089] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:59.109] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:59.109] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:27:59.114] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:27:59.563] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:59.575] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 15ms
[2025-07-30 11:27:59.579] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:27:59.579] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:27:59.912] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:27:59.925] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:27:59.925] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:27:59.930] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:00.260] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:00.273] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:00.273] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:28:00.280] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:00.599] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:00.615] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:00.615] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:28:00.615] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:02.344] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:02.344] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:02.359] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:28:02.362] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:02.681] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:02.697] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:02.697] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:28:02.697] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:03.043] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:03.045] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:03.045] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:28:03.060] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:03.428] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:03.444] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:03.444] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:28:03.460] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:03.784] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:03.801] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:03.803] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:28:03.803] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:04.145] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:04.161] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358200 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:04.161] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358200 bytes
[2025-07-30 11:28:04.161] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358200 bytes JPEG
[2025-07-30 11:28:04.511] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:04.527] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:04.527] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:04.543] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:04.974] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:04.974] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:04.974] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:04.990] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:05.311] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:05.327] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:05.327] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:05.344] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:05.625] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:05.657] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:28:05.665] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:05.665] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:06.014] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:06.037] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:06.037] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:06.041] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:06.367] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:06.379] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:06.379] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:06.392] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:06.726] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:06.742] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:06.742] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:06.758] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:07.042] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:07.067] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:07.067] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:07.074] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:07.415] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:07.427] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:07.427] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:07.442] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:07.793] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:07.833] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:28:07.837] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:07.843] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:08.254] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:08.265] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:08.265] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:08.270] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:08.592] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:08.608] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:08.608] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:08.608] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:08.971] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:08.982] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 15ms
[2025-07-30 11:28:08.982] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:08.987] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:09.341] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:09.357] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:09.357] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:09.357] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:09.685] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:09.721] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:09.721] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:09.737] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:10.095] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:10.109] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:10.109] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:10.109] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:10.389] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:10.437] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:10.437] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:10.437] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:10.766] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:10.804] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:10.804] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:10.820] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:11.087] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:11.119] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:11.135] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:11.135] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:11.480] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:11.489] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:11.497] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:11.501] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:11.755] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:11.803] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:11.803] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:11.819] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:12.104] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:12.120] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:12.120] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:12.120] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:12.418] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:12.466] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:12.466] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:12.466] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:12.755] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:12.907] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:13.054] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:13.058] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:13.388] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:13.433] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358300 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:13.433] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358300 bytes
[2025-07-30 11:28:13.449] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358300 bytes JPEG
[2025-07-30 11:28:15.149] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:15.197] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 360866 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:15.197] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 360866 bytes
[2025-07-30 11:28:15.197] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 360866 bytes JPEG
[2025-07-30 11:28:15.627] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:15.667] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 360866 bytes (6.2% ratio) in 15ms
[2025-07-30 11:28:15.667] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 360866 bytes
[2025-07-30 11:28:15.677] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 360866 bytes JPEG
[2025-07-30 11:28:16.028] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:16.071] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 32ms
[2025-07-30 11:28:16.073] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:16.080] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:16.498] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:16.542] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 31ms
[2025-07-30 11:28:16.542] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:16.551] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:16.877] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:16.917] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 15ms
[2025-07-30 11:28:16.923] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:16.930] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:17.346] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:17.382] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 16ms
[2025-07-30 11:28:17.394] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:17.400] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:17.716] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:17.763] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 32ms
[2025-07-30 11:28:17.763] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:17.779] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:18.097] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:18.144] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 32ms
[2025-07-30 11:28:18.159] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:18.163] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:18.429] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:18.468] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 31ms
[2025-07-30 11:28:18.484] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:18.497] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:18.855] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:18.883] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 16ms
[2025-07-30 11:28:18.887] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:18.891] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:19.219] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:19.248] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 31ms
[2025-07-30 11:28:19.252] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:19.258] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:19.278] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:28:19.278] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:28:19.506] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:28:19.508] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:28:19.583] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:19.611] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 15ms
[2025-07-30 11:28:19.611] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:19.621] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:19.882] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:19.914] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 15ms
[2025-07-30 11:28:19.914] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:19.929] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:20.220] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:20.254] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 31ms
[2025-07-30 11:28:20.254] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:20.263] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:20.302] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x0
[2025-07-30 11:28:20.302] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x0
[2025-07-30 11:28:20.509] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x2
[2025-07-30 11:28:20.511] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x2
[2025-07-30 11:28:20.564] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:20.596] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 31ms
[2025-07-30 11:28:20.612] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:20.617] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x0
[2025-07-30 11:28:20.617] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:20.617] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x0
[2025-07-30 11:28:20.799] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x2
[2025-07-30 11:28:20.800] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x2
[2025-07-30 11:28:20.851] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:20.864] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 365550 bytes (6.3% ratio) in 0ms
[2025-07-30 11:28:20.895] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 365550 bytes
[2025-07-30 11:28:20.910] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 365550 bytes JPEG
[2025-07-30 11:28:21.254] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:21.279] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:21.293] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:28:21.293] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:28:21.596] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:21.628] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:21.644] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:28:21.644] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:28:21.929] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:21.964] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:21.964] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:28:21.976] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:28:22.265] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:22.310] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:22.310] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:28:22.310] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:28:22.485] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x20, flags:0x0
[2025-07-30 11:28:22.485] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x20, flags:0x0
[2025-07-30 11:28:22.708] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:22.740] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:22.740] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:28:22.758] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:28:23.047] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:23.064] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362980 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:23.064] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362980 bytes
[2025-07-30 11:28:23.064] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362980 bytes JPEG
[2025-07-30 11:28:23.331] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:23.351] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359363 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:23.353] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359363 bytes
[2025-07-30 11:28:23.358] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359363 bytes JPEG
[2025-07-30 11:28:23.723] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:28:23.732] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:28:23.942] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:23.942] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:28:23.942] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:28:23.942] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358400 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:23.942] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358400 bytes
[2025-07-30 11:28:23.959] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358400 bytes JPEG
[2025-07-30 11:28:24.210] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:24.258] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 360119 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:24.267] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 360119 bytes
[2025-07-30 11:28:24.273] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 360119 bytes JPEG
[2025-07-30 11:28:24.525] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:24.528] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362114 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:24.528] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362114 bytes
[2025-07-30 11:28:24.528] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362114 bytes JPEG
[2025-07-30 11:28:24.857] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:24.868] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359278 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:24.873] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359278 bytes
[2025-07-30 11:28:24.878] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359278 bytes JPEG
[2025-07-30 11:28:25.191] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:25.239] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358400 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:25.239] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358400 bytes
[2025-07-30 11:28:25.255] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358400 bytes JPEG
[2025-07-30 11:28:25.255] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:28:25.255] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:28:25.525] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:28:25.541] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:25.563] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:28:25.587] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358400 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:25.589] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358400 bytes
[2025-07-30 11:28:25.593] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358400 bytes JPEG
[2025-07-30 11:28:25.986] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:26.026] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358400 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:26.026] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358400 bytes
[2025-07-30 11:28:26.041] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358400 bytes JPEG
[2025-07-30 11:28:26.145] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:28:26.145] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:28:26.342] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:26.359] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:26.359] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:26.371] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:26.382] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:28:26.382] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:28:27.523] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:28:27.523] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:28:27.792] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:27.808] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:27.808] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:27.808] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:28.007] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:28.039] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 15ms
[2025-07-30 11:28:28.055] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:28.063] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:28.320] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:28.329] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 15ms
[2025-07-30 11:28:28.331] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:28.334] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:28.476] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:28:28.477] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:28:28.542] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:28.558] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:28.558] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:28.558] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:28.878] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:28.907] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:28.907] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:28.918] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:29.215] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:29.258] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:29.262] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:29.268] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:29.507] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:29.649] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:29.812] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:29.812] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:30.184] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:30.223] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:28:30.225] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:30.225] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:28:30.225] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:30.231] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:30.487] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:30.487] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:30.487] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:30.503] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:30.681] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:28:30.686] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:28:30.724] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:30.755] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:30.771] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:30.771] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:31.122] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:31.138] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:31.138] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:31.138] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:31.187] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:28:31.187] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:28:31.403] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:31.451] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:31.451] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:31.467] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:31.595] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:28:31.595] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:28:31.737] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:31.752] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:31.752] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:31.763] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:31.931] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:28:31.937] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:28:32.003] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:32.057] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363081 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:32.059] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363081 bytes
[2025-07-30 11:28:32.067] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363081 bytes JPEG
[2025-07-30 11:28:32.243] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x42, flags:0x0
[2025-07-30 11:28:32.256] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x42, flags:0x0
[2025-07-30 11:28:32.354] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:32.370] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362991 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:32.370] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362991 bytes
[2025-07-30 11:28:32.370] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362991 bytes JPEG
[2025-07-30 11:28:32.526] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x56, flags:0x0
[2025-07-30 11:28:32.526] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x56, flags:0x0
[2025-07-30 11:28:32.702] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:32.718] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:32.718] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:28:32.718] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:28:32.991] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:33.019] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362878 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:33.019] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362878 bytes
[2025-07-30 11:28:33.019] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362878 bytes JPEG
[2025-07-30 11:28:33.317] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:33.349] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 345619 bytes (5.9% ratio) in 16ms
[2025-07-30 11:28:33.349] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 345619 bytes
[2025-07-30 11:28:33.364] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 345619 bytes JPEG
[2025-07-30 11:28:33.746] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:33.766] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x42, flags:0x0
[2025-07-30 11:28:33.766] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x42, flags:0x0
[2025-07-30 11:28:33.782] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 346230 bytes (6.0% ratio) in 31ms
[2025-07-30 11:28:33.782] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 346230 bytes
[2025-07-30 11:28:33.793] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 346230 bytes JPEG
[2025-07-30 11:28:34.200] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:34.244] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353907 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:34.248] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353907 bytes
[2025-07-30 11:28:34.252] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353907 bytes JPEG
[2025-07-30 11:28:34.613] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:34.659] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353907 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:34.659] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353907 bytes
[2025-07-30 11:28:34.669] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353907 bytes JPEG
[2025-07-30 11:28:34.879] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x42, flags:0x0
[2025-07-30 11:28:34.889] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x42, flags:0x0
[2025-07-30 11:28:34.967] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:35.025] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 32ms
[2025-07-30 11:28:35.025] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:35.035] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:35.196] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x56, flags:0x0
[2025-07-30 11:28:35.196] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x56, flags:0x0
[2025-07-30 11:28:35.335] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:35.351] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 0ms
[2025-07-30 11:28:35.351] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:35.351] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:35.595] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:35.635] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:35.635] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:35.648] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:35.946] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:35.982] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:35.982] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:35.997] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:36.334] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:36.381] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:36.391] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:36.397] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:36.495] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x42, flags:0x0
[2025-07-30 11:28:36.499] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x42, flags:0x0
[2025-07-30 11:28:36.683] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:36.731] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:36.731] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:36.741] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:37.079] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:37.127] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:37.127] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:37.143] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:37.442] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:37.487] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:37.487] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:37.497] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:37.688] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x0
[2025-07-30 11:28:37.702] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x0
[2025-07-30 11:28:37.749] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:37.803] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:37.803] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:37.813] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:37.829] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD, flags:0x2
[2025-07-30 11:28:37.829] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0xD, flags:0x2
[2025-07-30 11:28:38.136] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:38.178] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:38.178] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:38.194] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:39.811] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:39.843] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:28:39.843] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:39.859] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:40.242] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:40.274] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 32ms
[2025-07-30 11:28:40.290] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:40.290] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:40.537] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-1223, y:-605, flags:0x8002
[2025-07-30 11:28:40.539] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-1223,-605 -> 0,0
[2025-07-30 11:28:40.547] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-1223, y:-605, flags:0x8002
[2025-07-30 11:28:40.608] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:40.642] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 32ms
[2025-07-30 11:28:40.642] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:28:40.658] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:28:40.738] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-1223, y:-605, flags:0x8004
[2025-07-30 11:28:40.738] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-1223,-605 -> 0,0
[2025-07-30 11:28:40.752] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-1223, y:-605, flags:0x8004
[2025-07-30 11:28:40.944] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:40.947] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:40.947] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:40.961] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:41.225] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1895, y:681, flags:0x8002
[2025-07-30 11:28:41.227] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1895,681 -> 64681,41323
[2025-07-30 11:28:41.232] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1895, y:681, flags:0x8002
[2025-07-30 11:28:41.333] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:41.375] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358999 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:41.379] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358999 bytes
[2025-07-30 11:28:41.385] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358999 bytes JPEG
[2025-07-30 11:28:41.428] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1895, y:681, flags:0x8004
[2025-07-30 11:28:41.430] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1895,681 -> 64681,41323
[2025-07-30 11:28:41.433] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1895, y:681, flags:0x8004
[2025-07-30 11:28:41.662] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:41.711] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:41.711] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:41.726] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:41.995] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:42.011] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:42.011] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:42.024] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:42.446] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:42.482] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 46ms
[2025-07-30 11:28:42.487] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:42.494] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:42.778] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:42.825] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362956 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:42.825] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362956 bytes
[2025-07-30 11:28:42.833] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362956 bytes JPEG
[2025-07-30 11:28:43.125] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:43.141] [DEBUG] VirtualDesktopCapture: Mouse input request - x:806, y:398, flags:0x8002
[2025-07-30 11:28:43.141] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:806,398 -> 27511,24150
[2025-07-30 11:28:43.173] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:806, y:398, flags:0x8002
[2025-07-30 11:28:43.173] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362956 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:43.173] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362956 bytes
[2025-07-30 11:28:43.189] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362956 bytes JPEG
[2025-07-30 11:28:43.349] [DEBUG] VirtualDesktopCapture: Mouse input request - x:806, y:398, flags:0x8004
[2025-07-30 11:28:43.349] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:806,398 -> 27511,24150
[2025-07-30 11:28:43.363] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:806, y:398, flags:0x8004
[2025-07-30 11:28:43.478] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:43.526] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:43.526] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:43.526] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:43.756] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:43.762] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:43.762] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:43.762] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:43.994] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:44.042] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:44.042] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:44.042] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:44.334] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:44.374] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:44.374] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:44.385] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:44.660] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:44.709] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:44.713] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:44.719] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:44.960] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:44.975] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:44.975] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:44.988] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:45.258] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:45.306] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:45.306] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:45.321] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:45.530] [DEBUG] VirtualDesktopCapture: Mouse input request - x:872, y:298, flags:0x8002
[2025-07-30 11:28:45.530] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:872,298 -> 29763,18082
[2025-07-30 11:28:45.563] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:872, y:298, flags:0x8002
[2025-07-30 11:28:45.675] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:45.691] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:45.691] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:45.705] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:45.723] [DEBUG] VirtualDesktopCapture: Mouse input request - x:872, y:298, flags:0x8004
[2025-07-30 11:28:45.723] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:872,298 -> 29763,18082
[2025-07-30 11:28:45.723] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:872, y:298, flags:0x8004
[2025-07-30 11:28:45.942] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:46.102] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:46.261] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:46.272] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:46.507] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:46.524] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:46.524] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:46.524] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:46.774] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:46.809] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:46.811] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:46.813] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:47.082] [DEBUG] VirtualDesktopCapture: Mouse input request - x:419, y:316, flags:0x8002
[2025-07-30 11:28:47.086] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:419,316 -> 14301,19175
[2025-07-30 11:28:47.094] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:419, y:316, flags:0x8002
[2025-07-30 11:28:47.142] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:47.158] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:47.158] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:47.169] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:47.285] [DEBUG] VirtualDesktopCapture: Mouse input request - x:419, y:316, flags:0x8004
[2025-07-30 11:28:47.301] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:419,316 -> 14301,19175
[2025-07-30 11:28:47.301] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:419, y:316, flags:0x8004
[2025-07-30 11:28:47.567] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:47.598] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:47.598] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:47.618] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:47.891] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:47.906] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:47.906] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:47.917] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:48.354] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:48.354] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:48.367] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:48.367] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:48.791] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:48.807] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:48.807] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:48.816] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:49.108] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:49.126] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:49.126] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:49.137] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:49.503] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:49.526] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:28:49.526] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:28:49.550] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:49.550] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:49.566] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:49.740] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:28:49.756] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:28:49.903] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:49.951] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:49.951] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:49.967] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:50.094] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:28:50.094] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:28:50.303] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:28:50.303] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:28:50.364] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:50.405] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:50.410] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:50.416] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:50.422] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:28:50.422] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:28:50.462] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:28:50.462] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:28:50.510] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:28:50.510] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:28:50.729] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:50.745] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:50.747] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:50.747] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:51.246] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:28:51.262] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:28:52.417] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:52.465] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:52.465] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:52.481] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:52.593] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D, flags:0x0
[2025-07-30 11:28:52.593] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x4D, flags:0x0
[2025-07-30 11:28:52.915] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:52.915] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:52.915] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:52.915] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:53.325] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:53.348] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:28:53.353] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:53.360] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:53.631] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:53.636] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:53.636] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:53.647] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:54.014] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:54.056] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:54.058] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:54.066] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:54.383] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:54.438] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 47ms
[2025-07-30 11:28:54.446] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:28:54.446] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x42, flags:0x0
[2025-07-30 11:28:54.446] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:28:54.446] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x42, flags:0x0
[2025-07-30 11:28:54.766] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:54.813] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358247 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:54.813] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358247 bytes
[2025-07-30 11:28:54.829] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358247 bytes JPEG
[2025-07-30 11:28:54.845] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x42, flags:0x0
[2025-07-30 11:28:54.845] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x42, flags:0x0
[2025-07-30 11:28:55.139] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:55.181] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358247 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:55.181] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358247 bytes
[2025-07-30 11:28:55.199] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358247 bytes JPEG
[2025-07-30 11:28:55.688] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:55.724] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:55.724] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:55.724] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:56.041] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:56.048] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:56.055] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:56.059] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:56.347] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:56.395] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:56.395] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:56.413] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:56.684] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:56.700] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 0ms
[2025-07-30 11:28:56.700] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:56.712] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:56.998] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x42, flags:0x0
[2025-07-30 11:28:56.998] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x42, flags:0x0
[2025-07-30 11:28:57.016] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:57.063] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 32ms
[2025-07-30 11:28:57.063] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:57.079] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:57.459] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:57.492] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:57.506] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:57.510] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:57.880] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:58.054] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:58.054] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:58.066] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:58.290] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x42, flags:0x0
[2025-07-30 11:28:58.293] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x42, flags:0x0
[2025-07-30 11:28:58.410] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:58.457] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:58.457] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:58.474] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:58.829] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:58.877] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 31ms
[2025-07-30 11:28:58.877] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:58.877] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:59.149] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:59.196] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 359110 bytes (6.2% ratio) in 47ms
[2025-07-30 11:28:59.196] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 359110 bytes
[2025-07-30 11:28:59.196] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 359110 bytes JPEG
[2025-07-30 11:28:59.281] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x56, flags:0x0
[2025-07-30 11:28:59.285] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x56, flags:0x0
[2025-07-30 11:28:59.594] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:28:59.648] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 351745 bytes (6.1% ratio) in 32ms
[2025-07-30 11:28:59.648] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 351745 bytes
[2025-07-30 11:28:59.662] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 351745 bytes JPEG
[2025-07-30 11:29:00.025] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:00.061] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 32ms
[2025-07-30 11:29:00.061] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:00.076] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:00.291] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x56, flags:0x0
[2025-07-30 11:29:00.291] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x56, flags:0x0
[2025-07-30 11:29:00.525] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:00.557] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 16ms
[2025-07-30 11:29:00.573] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:00.573] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:00.878] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:00.934] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 47ms
[2025-07-30 11:29:00.936] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:00.942] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:01.291] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:01.339] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:29:01.339] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:01.358] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:01.674] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:01.708] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:29:01.708] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:01.724] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:01.995] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:02.043] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:29:02.043] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:02.061] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:02.320] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:02.347] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 16ms
[2025-07-30 11:29:02.360] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:02.367] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:02.724] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:02.761] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:29:02.771] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:02.776] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:03.076] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:03.123] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 31ms
[2025-07-30 11:29:03.123] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:03.141] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:03.427] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:03.443] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 0ms
[2025-07-30 11:29:03.443] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:03.443] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:03.677] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:03.695] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 0ms
[2025-07-30 11:29:03.695] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:03.706] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:03.927] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:03.943] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 353926 bytes (6.1% ratio) in 0ms
[2025-07-30 11:29:03.943] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 353926 bytes
[2025-07-30 11:29:03.943] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 353926 bytes JPEG
[2025-07-30 11:29:04.437] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1789, y:614, flags:0x8002
[2025-07-30 11:29:04.437] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1789,614 -> 61063,37257
[2025-07-30 11:29:04.453] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1789, y:614, flags:0x8002
[2025-07-30 11:29:04.453] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1789, y:614, flags:0x8004
[2025-07-30 11:29:04.465] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1789,614 -> 61063,37257
[2025-07-30 11:29:04.469] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1789, y:614, flags:0x8004
[2025-07-30 11:29:05.011] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1789, y:614, flags:0x8002
[2025-07-30 11:29:05.011] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1789,614 -> 61063,37257
[2025-07-30 11:29:05.011] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1789, y:614, flags:0x8002
[2025-07-30 11:29:05.027] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1789, y:614, flags:0x8004
[2025-07-30 11:29:05.027] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1789,614 -> 61063,37257
[2025-07-30 11:29:05.027] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1789, y:614, flags:0x8004
[2025-07-30 11:29:05.573] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:05.576] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:05.576] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:05.576] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:06.021] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:06.047] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:06.047] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:06.047] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:06.508] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:06.524] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:06.524] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:06.540] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:06.758] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:06.773] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:06.773] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:06.773] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:06.933] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1430, y:692, flags:0x8002
[2025-07-30 11:29:06.943] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1430,692 -> 48809,41990
[2025-07-30 11:29:06.960] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1430, y:692, flags:0x8002
[2025-07-30 11:29:07.007] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:07.023] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:07.023] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:07.023] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:07.134] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1430, y:692, flags:0x8004
[2025-07-30 11:29:07.145] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1430,692 -> 48809,41990
[2025-07-30 11:29:07.145] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1430, y:692, flags:0x8004
[2025-07-30 11:29:07.256] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:07.303] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:07.303] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:07.319] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:07.494] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1491, y:686, flags:0x8002
[2025-07-30 11:29:07.494] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1491,686 -> 50892,41626
[2025-07-30 11:29:07.526] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1491, y:686, flags:0x8002
[2025-07-30 11:29:07.623] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:07.638] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:07.638] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:07.649] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:07.702] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1491, y:686, flags:0x8004
[2025-07-30 11:29:07.702] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1491,686 -> 50892,41626
[2025-07-30 11:29:07.702] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1491, y:686, flags:0x8004
[2025-07-30 11:29:07.873] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:07.920] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:07.920] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:07.920] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:08.206] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:08.253] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:08.253] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:08.269] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:08.543] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:08.591] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:08.591] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:08.591] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:08.942] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:08.942] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:09.096] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:09.099] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:09.102] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:09.106] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:09.106] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:09.106] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:09.338] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:09.386] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:09.386] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:09.386] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:09.546] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:29:09.546] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:29:09.752] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:09.800] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:09.800] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:09.816] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:09.832] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:29:09.832] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:29:09.832] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:29:09.832] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:29:09.959] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:29:09.959] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:29:10.077] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:10.104] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:29:10.104] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:29:10.120] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:10.120] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:10.129] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:10.152] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:29:10.152] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:29:10.295] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:29:10.295] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:29:10.351] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:10.353] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:10.404] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:29:10.420] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:29:10.420] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:10.442] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:10.442] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:10.442] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:10.628] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x0
[2025-07-30 11:29:10.628] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x0
[2025-07-30 11:29:10.628] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:10.628] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:10.796] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:10.834] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28, flags:0x2
[2025-07-30 11:29:10.834] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x28, flags:0x2
[2025-07-30 11:29:10.834] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:10.844] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:10.850] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:11.104] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:11.152] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:11.152] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:11.168] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:11.437] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:11.481] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 46ms
[2025-07-30 11:29:11.485] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:11.485] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:11.819] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:11.866] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:11.866] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:11.882] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:12.181] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:12.330] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:12.487] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:12.491] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:12.769] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:12.816] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:12.816] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:12.816] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:13.088] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:13.134] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:13.134] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:13.134] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:13.402] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:13.418] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:13.418] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:13.418] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:13.667] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:13.714] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:13.714] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:13.730] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:14.018] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:14.066] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:14.066] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:14.080] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:14.350] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:14.404] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:14.404] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:14.414] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:14.700] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:14.716] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:14.716] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:14.729] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:14.998] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:15.052] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:15.052] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:15.062] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:15.332] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:15.379] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:15.379] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:15.379] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:15.698] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:15.746] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:15.746] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:15.763] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:16.017] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:16.065] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:16.065] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:16.065] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:17.724] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:17.771] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:17.771] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:17.771] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:18.099] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:18.113] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:18.113] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:18.129] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:18.413] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:18.447] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 16ms
[2025-07-30 11:29:18.447] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:18.447] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:18.764] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:18.780] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:18.780] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:18.780] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:19.134] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:19.156] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:19.157] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:19.162] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:19.464] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:19.479] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:19.492] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:19.495] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:19.708] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:19.715] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:19.715] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:19.726] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:19.864] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:19.866] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:19.990] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:20.033] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:20.038] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:20.044] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:20.184] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:20.184] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:20.347] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:20.362] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:20.362] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:20.362] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:20.723] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:20.755] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:20.771] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:20.771] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:21.194] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:21.204] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:21.210] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:21.210] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:21.475] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:21.479] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:21.479] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:21.489] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:21.795] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:21.842] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358335 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:21.842] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358335 bytes
[2025-07-30 11:29:21.855] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358335 bytes JPEG
[2025-07-30 11:29:22.386] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:22.402] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:22.402] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:22.406] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:22.791] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:22.823] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358446 bytes (6.2% ratio) in 16ms
[2025-07-30 11:29:22.823] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358446 bytes
[2025-07-30 11:29:22.830] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358446 bytes JPEG
[2025-07-30 11:29:23.109] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:23.125] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 363036 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:23.125] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 363036 bytes
[2025-07-30 11:29:23.139] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 363036 bytes JPEG
[2025-07-30 11:29:23.375] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:23.392] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362648 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:23.392] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362648 bytes
[2025-07-30 11:29:23.404] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362648 bytes JPEG
[2025-07-30 11:29:23.718] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:23.763] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:23.765] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:23.773] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:24.042] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:24.058] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:29:24.058] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:24.066] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:24.311] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:24.327] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:24.327] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:24.337] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:24.642] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:24.690] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:24.690] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:24.706] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:25.112] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:25.153] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:25.153] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:25.161] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:25.517] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:25.551] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:25.551] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:25.568] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:25.797] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x0
[2025-07-30 11:29:25.799] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x0
[2025-07-30 11:29:25.887] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:25.935] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:25.935] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:25.935] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:26.046] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27, flags:0x2
[2025-07-30 11:29:26.046] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x27, flags:0x2
[2025-07-30 11:29:26.224] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:26.273] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:26.275] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:26.283] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:26.579] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:26.799] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:26.799] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:26.799] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:26.991] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x0
[2025-07-30 11:29:26.991] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x0
[2025-07-30 11:29:27.155] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:27.203] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:27.203] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:27.203] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:27.251] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26, flags:0x2
[2025-07-30 11:29:27.251] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x26, flags:0x2
[2025-07-30 11:29:27.553] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:27.600] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:27.600] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:27.617] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:27.727] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:27.727] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:27.904] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:27.904] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:27.904] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:27.904] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:27.904] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:27.920] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:27.920] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:27.930] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:28.032] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:28.041] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:28.100] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:28.102] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:28.178] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:28.218] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:28.219] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:28.219] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:28.265] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:28.265] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:28.329] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:28.344] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:28.478] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:28.482] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:28.538] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:28.589] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:28.589] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:28.601] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:28.601] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:28.601] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:28.666] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:28.666] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:29.093] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:29.096] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:29.098] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:29.098] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:29.102] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x0
[2025-07-30 11:29:29.102] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x0
[2025-07-30 11:29:29.381] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25, flags:0x2
[2025-07-30 11:29:29.397] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x25, flags:0x2
[2025-07-30 11:29:30.234] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:30.238] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:30.238] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:30.238] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:30.487] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:30.534] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:30.534] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:30.550] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:30.882] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:30.914] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:30.914] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:30.924] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:31.350] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:31.397] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:31.397] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:31.413] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:31.686] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:31.704] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:31.704] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:31.714] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:31.983] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:32.039] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:32.041] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:32.050] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:33.132] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:33.177] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:33.179] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:33.188] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:33.451] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:33.467] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:33.467] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:33.477] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:33.696] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:33.708] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:33.708] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:33.712] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:33.977] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:34.014] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:34.014] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:34.027] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:34.301] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:34.333] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:34.333] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:34.333] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:34.567] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:34.583] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:34.583] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:34.592] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:34.824] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:34.864] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:34.864] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:34.879] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:35.197] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:35.222] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:35.222] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:35.222] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:35.765] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:35.813] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:35.813] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:35.823] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:36.162] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:36.194] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:29:36.194] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:36.200] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:36.464] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:36.480] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:36.480] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:36.480] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:36.745] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:36.793] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:36.793] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:36.809] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:37.139] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:37.177] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:37.177] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:37.193] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:37.646] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:37.805] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:37.961] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:37.965] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:38.262] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:38.309] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:38.309] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:38.326] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:38.894] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:38.926] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:38.942] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:38.945] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:39.258] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:39.258] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:39.258] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:39.277] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:39.496] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:39.512] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:39.512] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:39.512] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:39.778] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:39.825] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:39.825] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:39.825] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:40.129] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:40.176] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:40.176] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:40.176] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:40.449] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:40.461] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:40.461] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:40.461] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:40.726] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:40.774] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:40.774] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:40.784] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:41.040] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:41.050] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:41.050] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:41.050] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:41.292] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:41.339] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:41.339] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:41.355] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:41.626] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:41.674] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:41.674] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:41.690] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:43.236] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:43.280] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 47ms
[2025-07-30 11:29:43.284] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:43.289] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:43.558] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:43.605] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:43.605] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:43.621] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:43.892] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:43.936] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:43.939] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:43.947] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:44.241] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:44.289] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:44.289] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:44.289] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:44.570] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:44.575] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:44.575] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:44.575] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:45.160] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:45.203] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:45.203] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:45.203] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:45.507] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:45.554] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:45.564] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:45.570] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:45.862] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:45.903] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:45.903] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:45.919] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:46.206] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:46.242] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:46.254] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:46.262] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:46.554] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:46.587] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:46.587] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:46.590] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:46.923] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:46.939] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:29:46.939] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:46.948] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:47.284] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:47.332] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:47.348] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:47.348] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:48.167] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:48.172] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:48.172] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:48.185] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:48.466] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:48.477] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:48.479] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:48.484] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:48.849] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:48.888] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:48.897] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:48.902] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:49.249] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:49.291] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:49.291] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:49.301] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:49.631] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:49.676] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:49.676] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:49.687] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:49.986] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:50.001] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:50.001] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:50.016] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:50.301] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:50.328] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:50.328] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:50.334] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:50.652] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:50.700] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:50.700] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:50.718] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:51.164] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:51.196] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:29:51.196] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:51.213] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:51.732] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:51.749] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:29:51.749] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:51.756] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:52.080] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:52.128] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 47ms
[2025-07-30 11:29:52.128] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:52.128] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:52.579] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:52.613] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:52.613] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:52.619] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:52.996] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:53.028] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:53.044] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:53.044] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:53.402] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:53.428] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:29:53.435] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:53.438] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:53.748] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:53.770] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:53.770] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:53.770] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:54.048] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:54.076] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:29:54.076] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:54.081] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:54.613] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:54.629] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:29:54.629] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:54.641] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:54.930] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:55.082] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:55.082] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:55.092] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:55.457] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:55.504] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:55.506] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:55.511] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:57.088] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:57.124] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:57.124] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:57.140] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:57.729] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:57.776] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:57.783] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:57.790] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:58.046] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:58.096] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:58.098] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:58.105] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:58.395] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:58.452] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 47ms
[2025-07-30 11:29:58.454] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:58.459] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:58.751] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:58.792] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:58.792] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:58.806] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:59.101] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:59.139] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:29:59.142] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:59.150] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:59.443] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:59.494] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:59.494] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:59.509] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:29:59.810] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:29:59.857] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:29:59.870] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:29:59.875] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:00.176] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:00.196] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:00.196] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:00.196] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:00.742] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:00.798] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:00.802] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:00.808] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:01.077] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:01.098] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:01.098] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:01.098] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:01.327] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:01.375] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:01.385] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:01.391] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:01.675] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:01.699] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:30:01.699] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:01.705] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:01.943] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:01.961] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:01.961] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:01.970] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:02.219] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:02.264] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:30:02.264] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:02.276] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:02.543] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:02.559] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:02.559] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:02.570] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:02.791] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:02.846] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:02.848] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:02.856] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:03.150] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:03.195] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:30:03.195] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:03.204] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:03.508] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:03.533] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:30:03.533] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:03.533] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:03.958] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:03.974] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:03.974] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:03.974] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:04.207] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:04.223] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:04.223] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:04.236] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:04.532] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:04.576] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:04.576] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:04.586] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:04.873] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:04.921] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:04.921] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:04.931] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:05.230] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:05.246] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:30:05.246] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:05.246] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:05.522] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:05.580] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:05.580] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:05.590] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:05.973] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:06.021] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:30:06.021] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:06.037] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:06.310] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:06.322] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:06.322] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:06.322] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:06.570] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:06.618] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:06.618] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:06.634] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:07.088] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:07.104] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:07.112] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:07.112] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:07.337] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:07.385] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:07.392] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:07.399] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:07.655] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:07.670] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:07.670] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:07.683] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:09.102] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:09.150] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:30:09.150] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:09.166] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:09.485] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:09.533] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:09.533] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:09.550] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:10.119] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:10.165] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:10.165] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:10.181] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:10.436] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:10.452] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:10.452] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:10.452] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:10.750] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:10.798] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:10.798] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:10.814] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:11.069] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:11.084] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:11.084] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:11.084] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:11.349] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:11.397] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:30:11.397] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:11.413] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:11.748] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:11.795] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:11.795] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:11.811] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:12.100] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:12.148] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:30:12.148] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:12.163] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:12.432] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:12.448] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:12.448] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:12.448] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:12.731] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:12.779] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:12.779] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:12.794] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:13.299] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:13.347] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:13.347] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:13.357] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:13.630] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:13.632] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:13.632] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:13.646] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:13.914] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:13.962] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:13.962] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:13.982] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:14.261] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:14.297] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:14.297] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:14.313] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:14.565] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:14.581] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:14.581] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:14.581] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:14.847] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:14.894] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 32ms
[2025-07-30 11:30:14.894] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:14.894] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:15.181] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:15.234] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 46ms
[2025-07-30 11:30:15.234] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:15.244] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:15.509] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:15.515] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:15.515] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:15.515] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:15.977] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:15.987] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:15.989] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:15.992] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:16.510] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:16.557] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:16.557] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:16.573] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:17.011] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:17.038] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:17.042] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:17.046] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:17.339] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:17.347] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:17.347] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:17.355] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:17.712] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:17.739] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:30:17.739] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:17.744] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:17.980] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:17.996] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 15ms
[2025-07-30 11:30:18.002] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:18.004] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:18.374] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:18.405] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:18.421] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:18.428] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:18.743] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:18.759] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:18.770] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:18.770] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:19.175] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:19.223] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:19.223] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:19.232] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:19.827] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:19.843] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:19.843] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:19.857] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:20.265] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:20.296] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 31ms
[2025-07-30 11:30:20.296] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:20.312] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:20.708] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:20.712] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:20.723] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:20.726] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:22.125] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:22.141] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:22.149] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:22.152] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:22.457] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:22.473] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:22.473] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:22.473] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:22.908] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:22.924] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:22.924] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:22.924] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:23.285] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:23.317] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 16ms
[2025-07-30 11:30:23.317] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:23.321] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:23.721] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:23.721] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:23.737] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:23.739] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:24.021] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:24.297] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358238 bytes (6.2% ratio) in 47ms
[2025-07-30 11:30:24.297] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358238 bytes
[2025-07-30 11:30:24.308] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358238 bytes JPEG
[2025-07-30 11:30:24.608] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:24.624] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 358247 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:24.624] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 358247 bytes
[2025-07-30 11:30:24.636] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 358247 bytes JPEG
[2025-07-30 11:30:24.968] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:24.974] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 357855 bytes (6.2% ratio) in 0ms
[2025-07-30 11:30:24.983] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 357855 bytes
[2025-07-30 11:30:24.983] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 357855 bytes JPEG
[2025-07-30 11:30:25.354] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:25.384] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 144516 bytes (2.5% ratio) in 31ms
[2025-07-30 11:30:25.384] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 144516 bytes
[2025-07-30 11:30:25.405] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 144516 bytes JPEG
[2025-07-30 11:30:27.186] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:27.202] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 368525 bytes (6.3% ratio) in 0ms
[2025-07-30 11:30:27.202] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 368525 bytes
[2025-07-30 11:30:27.217] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 368525 bytes JPEG
[2025-07-30 11:30:28.087] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:28.103] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 367264 bytes (6.3% ratio) in 0ms
[2025-07-30 11:30:28.103] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 367264 bytes
[2025-07-30 11:30:28.103] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 367264 bytes JPEG
[2025-07-30 11:30:28.603] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:28.619] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 367264 bytes (6.3% ratio) in 0ms
[2025-07-30 11:30:28.619] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 367264 bytes
[2025-07-30 11:30:28.619] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 367264 bytes JPEG
[2025-07-30 11:30:29.082] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:29.105] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 367264 bytes (6.3% ratio) in 16ms
[2025-07-30 11:30:29.105] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 367264 bytes
[2025-07-30 11:30:29.114] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 367264 bytes JPEG
[2025-07-30 11:30:29.521] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:29.552] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 245690 bytes (4.2% ratio) in 16ms
[2025-07-30 11:30:29.562] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 245690 bytes
[2025-07-30 11:30:29.570] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 245690 bytes JPEG
[2025-07-30 11:30:29.949] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:29.960] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 245690 bytes (4.2% ratio) in 0ms
[2025-07-30 11:30:29.962] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 245690 bytes
[2025-07-30 11:30:29.966] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 245690 bytes JPEG
[2025-07-30 11:30:30.217] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:30.227] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 245690 bytes (4.2% ratio) in 0ms
[2025-07-30 11:30:30.229] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 245690 bytes
[2025-07-30 11:30:30.232] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 245690 bytes JPEG
[2025-07-30 11:30:30.516] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:30.564] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 245669 bytes (4.2% ratio) in 32ms
[2025-07-30 11:30:30.564] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 245669 bytes
[2025-07-30 11:30:30.580] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 245669 bytes JPEG
[2025-07-30 11:30:30.898] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:30.909] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 245668 bytes (4.2% ratio) in 0ms
[2025-07-30 11:30:30.911] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 245668 bytes
[2025-07-30 11:30:30.914] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 245668 bytes JPEG
[2025-07-30 11:30:31.178] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:31.217] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 245668 bytes (4.2% ratio) in 47ms
[2025-07-30 11:30:31.220] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 245668 bytes
[2025-07-30 11:30:31.225] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 245668 bytes JPEG
[2025-07-30 11:30:31.853] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:31.875] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 43941 bytes (0.8% ratio) in 16ms
[2025-07-30 11:30:31.878] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 43941 bytes
[2025-07-30 11:30:31.883] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 43941 bytes JPEG
[2025-07-30 11:30:32.461] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:32.499] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 82695 bytes (1.4% ratio) in 31ms
[2025-07-30 11:30:32.502] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 82695 bytes
[2025-07-30 11:30:32.509] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 82695 bytes JPEG
[2025-07-30 11:30:32.846] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:32.857] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 82749 bytes (1.4% ratio) in 15ms
[2025-07-30 11:30:32.859] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 82749 bytes
[2025-07-30 11:30:32.861] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 82749 bytes JPEG
[2025-07-30 11:30:33.206] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:33.232] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 76978 bytes (1.3% ratio) in 31ms
[2025-07-30 11:30:33.232] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 76978 bytes
[2025-07-30 11:30:33.240] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 76978 bytes JPEG
[2025-07-30 11:30:33.502] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:33.535] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 76307 bytes (1.3% ratio) in 15ms
[2025-07-30 11:30:33.538] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 76307 bytes
[2025-07-30 11:30:33.544] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 76307 bytes JPEG
[2025-07-30 11:30:33.795] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:33.805] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 76312 bytes (1.3% ratio) in 0ms
[2025-07-30 11:30:33.806] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 76312 bytes
[2025-07-30 11:30:33.809] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 76312 bytes JPEG
[2025-07-30 11:30:34.169] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:34.185] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 76241 bytes (1.3% ratio) in 16ms
[2025-07-30 11:30:34.185] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 76241 bytes
[2025-07-30 11:30:34.195] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 76241 bytes JPEG
[2025-07-30 11:30:34.619] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:34.641] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 105218 bytes (1.8% ratio) in 16ms
[2025-07-30 11:30:34.643] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 105218 bytes
[2025-07-30 11:30:34.649] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 105218 bytes JPEG
[2025-07-30 11:30:35.049] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:35.049] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 159168 bytes (2.7% ratio) in 0ms
[2025-07-30 11:30:35.049] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 159168 bytes
[2025-07-30 11:30:35.066] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 159168 bytes JPEG
[2025-07-30 11:30:35.813] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:35.838] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 161809 bytes (2.8% ratio) in 15ms
[2025-07-30 11:30:35.840] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 161809 bytes
[2025-07-30 11:30:35.845] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 161809 bytes JPEG
[2025-07-30 11:30:36.515] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:36.549] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 161907 bytes (2.8% ratio) in 16ms
[2025-07-30 11:30:36.564] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 161907 bytes
[2025-07-30 11:30:36.570] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 161907 bytes JPEG
[2025-07-30 11:30:37.730] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:37.760] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 150212 bytes (2.6% ratio) in 16ms
[2025-07-30 11:30:37.762] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 150212 bytes
[2025-07-30 11:30:37.767] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 150212 bytes JPEG
[2025-07-30 11:30:38.911] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:38.927] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171653 bytes (3.0% ratio) in 0ms
[2025-07-30 11:30:38.927] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171653 bytes
[2025-07-30 11:30:38.927] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171653 bytes JPEG
[2025-07-30 11:30:39.851] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:39.880] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171752 bytes (3.0% ratio) in 16ms
[2025-07-30 11:30:39.884] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171752 bytes
[2025-07-30 11:30:39.890] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171752 bytes JPEG
[2025-07-30 11:30:40.513] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:40.526] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 191609 bytes (3.3% ratio) in 0ms
[2025-07-30 11:30:40.528] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 191609 bytes
[2025-07-30 11:30:40.531] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 191609 bytes JPEG
[2025-07-30 11:30:41.904] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:41.924] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 194128 bytes (3.3% ratio) in 16ms
[2025-07-30 11:30:41.928] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 194128 bytes
[2025-07-30 11:30:41.933] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 194128 bytes JPEG
[2025-07-30 11:30:42.673] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:42.695] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 50208 bytes (0.9% ratio) in 16ms
[2025-07-30 11:30:42.703] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 50208 bytes
[2025-07-30 11:30:42.726] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 50208 bytes JPEG
[2025-07-30 11:30:43.521] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:43.545] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 88957 bytes (1.5% ratio) in 16ms
[2025-07-30 11:30:43.548] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 88957 bytes
[2025-07-30 11:30:43.554] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 88957 bytes JPEG
[2025-07-30 11:30:44.301] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:44.318] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 191902 bytes (3.3% ratio) in 16ms
[2025-07-30 11:30:44.319] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 191902 bytes
[2025-07-30 11:30:44.325] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 191902 bytes JPEG
[2025-07-30 11:30:45.900] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:45.921] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 187723 bytes (3.2% ratio) in 16ms
[2025-07-30 11:30:45.924] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 187723 bytes
[2025-07-30 11:30:45.928] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 187723 bytes JPEG
[2025-07-30 11:30:46.294] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:46.308] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 187388 bytes (3.2% ratio) in 0ms
[2025-07-30 11:30:46.308] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 187388 bytes
[2025-07-30 11:30:46.320] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 187388 bytes JPEG
[2025-07-30 11:30:46.836] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:46.858] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 159909 bytes (2.8% ratio) in 0ms
[2025-07-30 11:30:46.858] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 159909 bytes
[2025-07-30 11:30:46.867] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 159909 bytes JPEG
[2025-07-30 11:30:47.431] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:47.481] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 159777 bytes (2.7% ratio) in 31ms
[2025-07-30 11:30:47.481] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 159777 bytes
[2025-07-30 11:30:47.493] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 159777 bytes JPEG
[2025-07-30 11:30:48.035] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:48.098] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 159909 bytes (2.8% ratio) in 31ms
[2025-07-30 11:30:48.413] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 159909 bytes
[2025-07-30 11:30:48.419] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 159909 bytes JPEG
[2025-07-30 11:30:49.000] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:49.032] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 170455 bytes (2.9% ratio) in 15ms
[2025-07-30 11:30:49.032] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 170455 bytes
[2025-07-30 11:30:49.063] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 170455 bytes JPEG
[2025-07-30 11:30:50.411] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:50.434] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 67517 bytes (1.2% ratio) in 32ms
[2025-07-30 11:30:50.434] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 67517 bytes
[2025-07-30 11:30:50.459] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 67517 bytes JPEG
[2025-07-30 11:30:50.928] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:50.951] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 67477 bytes (1.2% ratio) in 15ms
[2025-07-30 11:30:50.973] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 67477 bytes
[2025-07-30 11:30:50.988] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 67477 bytes JPEG
[2025-07-30 11:30:51.658] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:51.688] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 101858 bytes (1.8% ratio) in 16ms
[2025-07-30 11:30:51.689] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 101858 bytes
[2025-07-30 11:30:51.705] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 101858 bytes JPEG
[2025-07-30 11:30:52.127] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:52.144] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 127527 bytes (2.2% ratio) in 0ms
[2025-07-30 11:30:52.144] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 127527 bytes
[2025-07-30 11:30:52.160] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 127527 bytes JPEG
[2025-07-30 11:30:52.697] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:52.741] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 128168 bytes (2.2% ratio) in 31ms
[2025-07-30 11:30:52.743] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 128168 bytes
[2025-07-30 11:30:52.760] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 128168 bytes JPEG
[2025-07-30 11:30:53.177] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:53.202] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 222642 bytes (3.8% ratio) in 15ms
[2025-07-30 11:30:53.205] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 222642 bytes
[2025-07-30 11:30:53.222] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 222642 bytes JPEG
[2025-07-30 11:30:53.583] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:53.631] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 320444 bytes (5.5% ratio) in 16ms
[2025-07-30 11:30:53.631] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 320444 bytes
[2025-07-30 11:30:53.654] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 320444 bytes JPEG
[2025-07-30 11:30:54.229] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:54.262] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 346151 bytes (6.0% ratio) in 16ms
[2025-07-30 11:30:54.262] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 346151 bytes
[2025-07-30 11:30:54.264] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 346151 bytes JPEG
[2025-07-30 11:30:54.797] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:54.832] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382452 bytes (6.6% ratio) in 15ms
[2025-07-30 11:30:54.832] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382452 bytes
[2025-07-30 11:30:54.864] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382452 bytes JPEG
[2025-07-30 11:30:55.347] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:55.386] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382406 bytes (6.6% ratio) in 16ms
[2025-07-30 11:30:55.398] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382406 bytes
[2025-07-30 11:30:55.416] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382406 bytes JPEG
[2025-07-30 11:30:55.902] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:55.942] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 386208 bytes (6.6% ratio) in 32ms
[2025-07-30 11:30:55.959] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 386208 bytes
[2025-07-30 11:30:55.974] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 386208 bytes JPEG
[2025-07-30 11:30:56.439] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:56.471] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 370325 bytes (6.4% ratio) in 16ms
[2025-07-30 11:30:56.473] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 370325 bytes
[2025-07-30 11:30:56.486] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 370325 bytes JPEG
[2025-07-30 11:30:57.080] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:57.111] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 369866 bytes (6.4% ratio) in 15ms
[2025-07-30 11:30:57.129] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 369866 bytes
[2025-07-30 11:30:57.132] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 369866 bytes JPEG
[2025-07-30 11:30:57.515] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:57.547] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 386440 bytes (6.6% ratio) in 31ms
[2025-07-30 11:30:57.547] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 386440 bytes
[2025-07-30 11:30:57.547] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 386440 bytes JPEG
[2025-07-30 11:30:57.913] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:57.926] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 386440 bytes (6.6% ratio) in 0ms
[2025-07-30 11:30:57.926] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 386440 bytes
[2025-07-30 11:30:57.926] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 386440 bytes JPEG
[2025-07-30 11:30:58.275] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:58.279] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 386640 bytes (6.7% ratio) in 0ms
[2025-07-30 11:30:58.279] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 386640 bytes
[2025-07-30 11:30:58.295] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 386640 bytes JPEG
[2025-07-30 11:30:58.742] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:58.790] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 386440 bytes (6.6% ratio) in 31ms
[2025-07-30 11:30:58.794] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 386440 bytes
[2025-07-30 11:30:58.798] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 386440 bytes JPEG
[2025-07-30 11:30:59.183] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:30:59.223] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 388612 bytes (6.7% ratio) in 31ms
[2025-07-30 11:30:59.226] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 388612 bytes
[2025-07-30 11:30:59.226] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 388612 bytes JPEG
[2025-07-30 11:31:00.442] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:00.457] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 394748 bytes (6.8% ratio) in 0ms
[2025-07-30 11:31:00.457] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 394748 bytes
[2025-07-30 11:31:00.457] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 394748 bytes JPEG
[2025-07-30 11:31:00.825] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:00.841] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 394734 bytes (6.8% ratio) in 0ms
[2025-07-30 11:31:00.841] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 394734 bytes
[2025-07-30 11:31:00.841] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 394734 bytes JPEG
[2025-07-30 11:31:01.423] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:01.441] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 394748 bytes (6.8% ratio) in 0ms
[2025-07-30 11:31:01.455] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 394748 bytes
[2025-07-30 11:31:01.458] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 394748 bytes JPEG
[2025-07-30 11:31:01.790] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:01.822] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 394734 bytes (6.8% ratio) in 16ms
[2025-07-30 11:31:01.838] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 394734 bytes
[2025-07-30 11:31:01.838] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 394734 bytes JPEG
[2025-07-30 11:31:02.333] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:02.361] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 388281 bytes (6.7% ratio) in 15ms
[2025-07-30 11:31:02.363] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 388281 bytes
[2025-07-30 11:31:02.367] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 388281 bytes JPEG
[2025-07-30 11:31:02.790] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:02.836] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 180105 bytes (3.1% ratio) in 31ms
[2025-07-30 11:31:02.837] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 180105 bytes
[2025-07-30 11:31:02.840] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 180105 bytes JPEG
[2025-07-30 11:31:04.003] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:04.034] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 180105 bytes (3.1% ratio) in 31ms
[2025-07-30 11:31:04.034] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 180105 bytes
[2025-07-30 11:31:04.050] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 180105 bytes JPEG
[2025-07-30 11:31:04.371] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:04.421] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 180105 bytes (3.1% ratio) in 31ms
[2025-07-30 11:31:04.426] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 180105 bytes
[2025-07-30 11:31:04.435] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 180105 bytes JPEG
[2025-07-30 11:31:04.865] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:04.897] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 180105 bytes (3.1% ratio) in 32ms
[2025-07-30 11:31:04.897] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 180105 bytes
[2025-07-30 11:31:04.912] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 180105 bytes JPEG
[2025-07-30 11:31:05.333] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:05.476] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 180105 bytes (3.1% ratio) in 31ms
[2025-07-30 11:31:05.635] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 180105 bytes
[2025-07-30 11:31:05.635] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 180105 bytes JPEG
[2025-07-30 11:31:06.049] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:06.098] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 180105 bytes (3.1% ratio) in 31ms
[2025-07-30 11:31:06.098] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 180105 bytes
[2025-07-30 11:31:06.098] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 180105 bytes JPEG
[2025-07-30 11:31:06.504] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:06.537] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 180257 bytes (3.1% ratio) in 31ms
[2025-07-30 11:31:06.538] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 180257 bytes
[2025-07-30 11:31:06.538] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 180257 bytes JPEG
[2025-07-30 11:31:06.902] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:06.902] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 179534 bytes (3.1% ratio) in 0ms
[2025-07-30 11:31:06.902] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 179534 bytes
[2025-07-30 11:31:06.918] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 179534 bytes JPEG
[2025-07-30 11:31:07.203] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:07.235] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 159909 bytes (2.8% ratio) in 31ms
[2025-07-30 11:31:07.235] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 159909 bytes
[2025-07-30 11:31:07.251] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 159909 bytes JPEG
[2025-07-30 11:31:07.648] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:07.652] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 209156 bytes (3.6% ratio) in 0ms
[2025-07-30 11:31:07.652] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 209156 bytes
[2025-07-30 11:31:07.652] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 209156 bytes JPEG
[2025-07-30 11:31:07.936] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:07.968] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 204266 bytes (3.5% ratio) in 16ms
[2025-07-30 11:31:07.984] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 204266 bytes
[2025-07-30 11:31:07.984] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 204266 bytes JPEG
[2025-07-30 11:31:08.304] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:08.333] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 183448 bytes (3.2% ratio) in 15ms
[2025-07-30 11:31:08.335] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 183448 bytes
[2025-07-30 11:31:08.341] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 183448 bytes JPEG
[2025-07-30 11:31:08.958] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:08.992] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 190049 bytes (3.3% ratio) in 15ms
[2025-07-30 11:31:08.994] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 190049 bytes
[2025-07-30 11:31:09.000] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 190049 bytes JPEG
[2025-07-30 11:31:09.666] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:09.698] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 188033 bytes (3.2% ratio) in 16ms
[2025-07-30 11:31:09.698] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 188033 bytes
[2025-07-30 11:31:09.710] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 188033 bytes JPEG
[2025-07-30 11:31:10.163] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:10.198] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 181144 bytes (3.1% ratio) in 32ms
[2025-07-30 11:31:10.202] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 181144 bytes
[2025-07-30 11:31:10.213] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 181144 bytes JPEG
[2025-07-30 11:31:10.616] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:10.644] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 169826 bytes (2.9% ratio) in 16ms
[2025-07-30 11:31:10.646] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 169826 bytes
[2025-07-30 11:31:10.652] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 169826 bytes JPEG
[2025-07-30 11:31:11.021] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:11.052] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 16ms
[2025-07-30 11:31:11.052] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:11.064] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:11.394] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:11.427] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 31ms
[2025-07-30 11:31:11.430] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:11.436] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:11.879] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:11.914] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 15ms
[2025-07-30 11:31:11.917] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:11.922] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:12.281] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:12.313] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 16ms
[2025-07-30 11:31:12.313] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:12.329] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:12.859] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:12.891] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 16ms
[2025-07-30 11:31:12.891] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:12.906] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:13.270] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:13.306] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 16ms
[2025-07-30 11:31:13.306] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:13.316] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:13.776] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:13.824] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 32ms
[2025-07-30 11:31:13.824] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:13.841] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:14.070] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.075] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.232] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:14.264] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 16ms
[2025-07-30 11:31:14.280] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:14.318] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:14.581] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.581] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.581] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.581] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.633] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.633] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.633] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.633] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.684] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.686] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.729] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.738] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.738] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.741] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.777] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.777] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.824] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:14.824] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.824] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.824] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.824] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.840] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 16ms
[2025-07-30 11:31:14.840] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:14.849] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:14.873] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.873] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.921] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.921] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.969] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:14.969] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:14.969] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:14.969] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:15.070] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:15.072] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:15.291] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:15.301] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 0ms
[2025-07-30 11:31:15.301] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:15.301] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:15.346] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:15.346] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:15.346] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:15.350] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:15.657] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:15.662] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:15.664] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:15.665] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:15.666] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:15.667] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:15.668] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:15.669] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:15.982] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:15.984] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:15.986] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:15.986] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:16.030] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:16.030] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:16.105] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:16.110] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:16.284] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:16.288] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:16.295] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:16.326] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 31ms
[2025-07-30 11:31:16.326] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:16.342] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:16.639] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:16.649] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 0ms
[2025-07-30 11:31:16.651] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:16.653] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:16.910] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:16.942] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:16.942] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:16.953] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 31ms
[2025-07-30 11:31:16.958] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:16.958] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:17.085] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:17.085] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:17.327] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:17.343] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 16ms
[2025-07-30 11:31:17.343] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:17.343] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:17.627] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:17.659] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 15ms
[2025-07-30 11:31:17.659] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:17.659] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:18.070] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:18.086] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 15ms
[2025-07-30 11:31:18.086] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:18.086] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:18.289] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.294] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:18.457] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:18.473] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 0ms
[2025-07-30 11:31:18.473] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:18.473] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:18.793] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.793] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:18.793] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.793] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:18.825] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:18.841] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.841] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:18.872] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 31ms
[2025-07-30 11:31:18.874] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:18.876] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:18.896] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.898] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:18.899] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.900] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:18.942] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.942] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:18.990] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.990] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:18.990] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:18.990] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.038] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.038] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.093] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.097] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.100] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.100] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.148] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.150] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.192] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.192] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.192] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.192] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.240] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.240] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.288] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.288] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.288] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.288] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.334] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:19.343] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.345] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.371] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 15ms
[2025-07-30 11:31:19.375] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:19.381] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:19.391] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.393] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.395] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.395] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.455] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x0
[2025-07-30 11:31:19.458] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x0
[2025-07-30 11:31:19.460] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8, flags:0x2
[2025-07-30 11:31:19.461] [DEBUG] VirtualDesktopCapture: Keyboard input sent to virtual desktop - vk:0x8, flags:0x2
[2025-07-30 11:31:19.724] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:19.740] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 0ms
[2025-07-30 11:31:19.740] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:19.740] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:20.120] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:20.168] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 47ms
[2025-07-30 11:31:20.168] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:20.168] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:20.491] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:20.507] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171297 bytes (2.9% ratio) in 16ms
[2025-07-30 11:31:20.507] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171297 bytes
[2025-07-30 11:31:20.507] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171297 bytes JPEG
[2025-07-30 11:31:20.787] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:20.798] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 171472 bytes (3.0% ratio) in 0ms
[2025-07-30 11:31:20.800] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 171472 bytes
[2025-07-30 11:31:20.803] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 171472 bytes JPEG
[2025-07-30 11:31:21.209] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:21.338] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 183937 bytes (3.2% ratio) in 15ms
[2025-07-30 11:31:21.340] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 183937 bytes
[2025-07-30 11:31:21.340] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 183937 bytes JPEG
[2025-07-30 11:31:21.987] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:22.022] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 221960 bytes (3.8% ratio) in 32ms
[2025-07-30 11:31:22.025] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 221960 bytes
[2025-07-30 11:31:22.029] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 221960 bytes JPEG
[2025-07-30 11:31:22.572] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:22.620] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 223774 bytes (3.9% ratio) in 31ms
[2025-07-30 11:31:22.620] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 223774 bytes
[2025-07-30 11:31:22.642] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 223774 bytes JPEG
[2025-07-30 11:31:23.185] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:23.208] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 220666 bytes (3.8% ratio) in 15ms
[2025-07-30 11:31:23.208] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 220666 bytes
[2025-07-30 11:31:23.216] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 220666 bytes JPEG
[2025-07-30 11:31:23.571] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:23.619] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 183317 bytes (3.2% ratio) in 15ms
[2025-07-30 11:31:23.619] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 183317 bytes
[2025-07-30 11:31:23.622] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 183317 bytes JPEG
[2025-07-30 11:31:24.068] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:24.097] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 182590 bytes (3.1% ratio) in 31ms
[2025-07-30 11:31:24.098] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 182590 bytes
[2025-07-30 11:31:24.103] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 182590 bytes JPEG
[2025-07-30 11:31:24.557] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:24.596] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189489 bytes (3.3% ratio) in 47ms
[2025-07-30 11:31:24.599] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189489 bytes
[2025-07-30 11:31:24.604] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189489 bytes JPEG
[2025-07-30 11:31:24.897] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:24.936] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 32ms
[2025-07-30 11:31:24.936] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:24.936] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:25.303] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:25.335] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 31ms
[2025-07-30 11:31:25.335] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:25.351] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:25.686] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:25.733] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 31ms
[2025-07-30 11:31:25.733] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:25.749] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:26.074] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:26.116] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 31ms
[2025-07-30 11:31:26.116] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:26.116] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:26.445] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:26.483] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 31ms
[2025-07-30 11:31:26.483] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:26.498] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:26.914] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:26.946] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 32ms
[2025-07-30 11:31:26.946] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:26.962] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:27.376] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:27.414] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 31ms
[2025-07-30 11:31:27.414] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:27.414] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:27.798] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:27.833] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 31ms
[2025-07-30 11:31:27.836] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:27.842] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:28.185] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:28.203] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 0ms
[2025-07-30 11:31:28.203] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:28.203] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:28.516] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:28.532] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 0ms
[2025-07-30 11:31:28.532] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:28.532] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:28.855] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:28.895] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 189491 bytes (3.3% ratio) in 32ms
[2025-07-30 11:31:28.897] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 189491 bytes
[2025-07-30 11:31:28.901] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 189491 bytes JPEG
[2025-07-30 11:31:29.966] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:30.013] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 159131 bytes (2.7% ratio) in 32ms
[2025-07-30 11:31:30.013] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 159131 bytes
[2025-07-30 11:31:30.013] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 159131 bytes JPEG
[2025-07-30 11:31:30.727] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:30.759] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 162227 bytes (2.8% ratio) in 31ms
[2025-07-30 11:31:30.759] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 162227 bytes
[2025-07-30 11:31:30.775] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 162227 bytes JPEG
[2025-07-30 11:31:31.159] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:31.197] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 162227 bytes (2.8% ratio) in 32ms
[2025-07-30 11:31:31.197] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 162227 bytes
[2025-07-30 11:31:31.197] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 162227 bytes JPEG
[2025-07-30 11:31:31.595] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:31.643] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 162227 bytes (2.8% ratio) in 32ms
[2025-07-30 11:31:31.643] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 162227 bytes
[2025-07-30 11:31:31.643] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 162227 bytes JPEG
[2025-07-30 11:31:31.948] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:31.963] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 162227 bytes (2.8% ratio) in 0ms
[2025-07-30 11:31:31.963] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 162227 bytes
[2025-07-30 11:31:31.963] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 162227 bytes JPEG
[2025-07-30 11:31:32.210] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:32.231] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 162227 bytes (2.8% ratio) in 15ms
[2025-07-30 11:31:32.247] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 162227 bytes
[2025-07-30 11:31:32.247] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 162227 bytes JPEG
[2025-07-30 11:31:32.715] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:32.762] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 160411 bytes (2.8% ratio) in 16ms
[2025-07-30 11:31:32.765] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 160411 bytes
[2025-07-30 11:31:32.765] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 160411 bytes JPEG
[2025-07-30 11:31:33.181] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:33.213] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 160482 bytes (2.8% ratio) in 15ms
[2025-07-30 11:31:33.216] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 160482 bytes
[2025-07-30 11:31:33.223] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 160482 bytes JPEG
[2025-07-30 11:31:33.661] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:33.693] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 183341 bytes (3.2% ratio) in 16ms
[2025-07-30 11:31:33.696] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 183341 bytes
[2025-07-30 11:31:33.700] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 183341 bytes JPEG
[2025-07-30 11:31:34.494] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:34.522] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 35417 bytes (0.6% ratio) in 16ms
[2025-07-30 11:31:34.522] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 35417 bytes
[2025-07-30 11:31:34.530] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 35417 bytes JPEG
[2025-07-30 11:31:35.003] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:35.014] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 51860 bytes (0.9% ratio) in 16ms
[2025-07-30 11:31:35.014] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 51860 bytes
[2025-07-30 11:31:35.027] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 51860 bytes JPEG
[2025-07-30 11:31:35.432] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:35.450] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 158511 bytes (2.7% ratio) in 15ms
[2025-07-30 11:31:35.452] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 158511 bytes
[2025-07-30 11:31:35.457] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 158511 bytes JPEG
[2025-07-30 11:31:35.802] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:35.818] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 158825 bytes (2.7% ratio) in 16ms
[2025-07-30 11:31:35.820] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 158825 bytes
[2025-07-30 11:31:35.825] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 158825 bytes JPEG
[2025-07-30 11:31:36.061] [INFO] InputThread: Socket closed
[2025-07-30 11:31:36.267] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:31:36.297] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 159956 bytes (2.8% ratio) in 31ms
[2025-07-30 11:31:36.299] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 159956 bytes
[2025-07-30 11:31:36.304] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 159956 bytes JPEG
[2025-07-30 11:31:36.333] [INFO] DesktopThread: Socket closed
[2025-07-30 11:31:36.334] [INFO] DesktopThread: Exiting gracefully
[2025-07-30 11:31:36.342] [INFO] InputThread: Connection state reset for reconnection
[2025-07-30 11:31:36.345] [INFO] MainThread: Input thread finished
[2025-07-30 11:31:36.348] [INFO] TurboJPEG wrapper cleaned up
[2025-07-30 11:31:36.349] [INFO] TurboJPEG image processing cleaned up
[2025-07-30 11:31:36.351] [INFO] VirtualDesktopCapture: Shutting down virtual desktop capture system
[2025-07-30 11:31:36.360] [INFO] VirtualDesktopCapture: Virtual desktop capture system shutdown complete
[2025-07-30 11:31:36.361] [INFO] MainThread: Virtual desktop capture system cleaned up
[2025-07-30 11:31:36.363] [INFO] MainThread: Cleanup completed, ready for reconnection
[2025-07-30 11:31:36.364] [INFO] HVNC connection thread finished
[2025-07-30 11:31:36.366] [INFO] HVNC Client logging shutdown
