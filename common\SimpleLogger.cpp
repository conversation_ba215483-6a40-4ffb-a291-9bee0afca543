/**
 * HVNC Simple Logger Implementation
 *
 * This file implements a lightweight, thread-safe logging system for the HVNC application.
 * It provides structured logging with different severity levels and automatic file management.
 *
 * Key Features:
 * - Multiple log levels (Debug, Info, Warning, Error)
 * - Thread-safe file writing
 * - Automatic timestamp generation
 * - Graceful error handling
 * - Minimal performance overhead
 */

#include "SimpleLogger.h"
#include <stdarg.h>
#include <stdio.h>

namespace ModernHVNC {

// ============================================================================
// STATIC MEMBER VARIABLES
// ============================================================================

/**
 * Logger State Variables
 * These static members maintain the global state of the logging system
 */
bool SimpleLogger::initialized_ = false;           // Tracks if logger has been initialized
HANDLE SimpleLogger::log_file_ = INVALID_HANDLE_VALUE; // Handle to the log file

void SimpleLogger::Initialize(const char* log_path) noexcept {
    if (initialized_) return;
    
    try {
        log_file_ = CreateFileA(
            log_path,
            GENERIC_WRITE,
            FILE_SHARE_READ,
            nullptr,
            CREATE_ALWAYS,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );
        
        if (log_file_ != INVALID_HANDLE_VALUE) {
            initialized_ = true;
            Log(LogLevel::Info, "HVNC Client logging initialized");
        }
    } catch (...) {
        // Silent failure for logging initialization
    }
}

void SimpleLogger::Log(LogLevel level, const char* format, ...) noexcept {
    if (!initialized_ || log_file_ == INVALID_HANDLE_VALUE) return;
    
    try {
        char buffer[MAX_LOG_SIZE];
        va_list args;
        va_start(args, format);
        
        const auto timestamp = GetTimestamp();
        const auto level_str = GetLogLevelString(level);
        
        int written = _snprintf_s(buffer, sizeof(buffer), _TRUNCATE,
            "[%s] [%s] ", timestamp.c_str(), level_str);
        
        if (written > 0 && written < static_cast<int>(sizeof(buffer))) {
            written += _vsnprintf_s(buffer + written, sizeof(buffer) - written, 
                _TRUNCATE, format, args);
        }
        
        va_end(args);
        
        if (written > 0) {
            // Add newline
            if (written < static_cast<int>(sizeof(buffer)) - 2) {
                buffer[written++] = '\r';
                buffer[written++] = '\n';
            }
            
            // Write formatted log entry to file with atomic operation
            DWORD bytes_written;
            WriteFile(log_file_, buffer, written, &bytes_written, nullptr);
            FlushFileBuffers(log_file_);  // Ensure immediate disk write for reliability
        }
    } catch (...) {
        // Silent failure for logging to prevent cascading errors
    }
}

/**
 * Log Application Start Event
 * Records when an application is launched, with optional path information
 * Used for tracking application lifecycle and debugging startup issues
 *
 * @param app_name - Name of the application being started
 * @param path - Optional full path to the application executable
 */
void SimpleLogger::LogAppStart(const char* app_name, const char* path) noexcept {
    if (path) {
        Log(LogLevel::Info, "Starting application: %s (Path: %s)", app_name, path);
    } else {
        Log(LogLevel::Info, "Starting application: %s", app_name);
    }
}

/**
 * Log Application End Event
 * Records when an application terminates, including exit code for debugging
 * Helps track application lifecycle and identify abnormal terminations
 *
 * @param app_name - Name of the application that ended
 * @param exit_code - Application exit code (0 = success, non-zero = error)
 */
void SimpleLogger::LogAppEnd(const char* app_name, DWORD exit_code) noexcept {
    Log(LogLevel::Info, "Application ended: %s (Exit code: %lu)", app_name, exit_code);
}

/**
 * Shutdown Logger System
 * Properly closes log file and releases resources
 * Called during application shutdown to ensure clean termination
 */
void SimpleLogger::Shutdown() noexcept {
    if (initialized_) {
        Log(LogLevel::Info, "HVNC Client logging shutdown");

        // Close log file handle and reset state
        if (log_file_ != INVALID_HANDLE_VALUE) {
            CloseHandle(log_file_);
            log_file_ = INVALID_HANDLE_VALUE;
        }
        initialized_ = false;
    }
}

/**
 * Convert Log Level to String Representation
 * Provides human-readable log level names for formatted output
 *
 * @param level - LogLevel enumeration value
 * @return String representation of the log level
 */
const char* SimpleLogger::GetLogLevelString(LogLevel level) noexcept {
    switch (level) {
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Debug: return "DEBUG";
        default: return "UNKNOWN";
    }
}

/**
 * Generate Current Timestamp String
 * Creates formatted timestamp for log entries with millisecond precision
 * Format: YYYY-MM-DD HH:MM:SS.mmm
 *
 * @return Formatted timestamp string
 */
std::string SimpleLogger::GetTimestamp() noexcept {
    try {
        SYSTEMTIME st;
        GetLocalTime(&st);  // Get current local system time

        // Format timestamp with millisecond precision
        char timestamp[64];
        _snprintf_s(timestamp, sizeof(timestamp), _TRUNCATE,
            "%04d-%02d-%02d %02d:%02d:%02d.%03d",
            st.wYear, st.wMonth, st.wDay,
            st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);

        return std::string(timestamp);
    } catch (...) {
        // Return default timestamp on any error
        return "0000-00-00 00:00:00.000";
    }
}

} // namespace ModernHVNC
