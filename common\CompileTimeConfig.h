#pragma once

/**
 * HVNC Compile-Time Performance Configuration
 *
 * This file contains all performance-related settings for the HVNC (Hidden Virtual Network Computing) system.
 * These settings directly impact the quality, performance, and resource usage of the remote desktop experience.
 *
 * IMPORTANT: After modifying any values in this file, you must rebuild the entire project
 * for changes to take effect.
 */

// ============================================================================
// MAIN PERFORMANCE SETTINGS - MODIFY THESE VALUES AS NEEDED
// ============================================================================

/**
 * JPEG Compression Quality (1-100)
 *
 * Controls the quality vs file size trade-off for screen capture compression.
 * This setting directly affects image quality and network bandwidth usage.
 *
 * - Higher values (80-100): Better image quality, larger file sizes, more CPU usage
 * - Medium values (50-79): Balanced quality and performance
 * - Lower values (1-49): Worse quality, smaller files, less CPU usage
 *
 * RECOMMENDED VALUES:
 * - 90 (High Quality): Best visual experience, higher bandwidth
 * - 65 (Balanced): Good compromise between quality and performance
 * - 35 (High Performance): Minimal bandwidth, acceptable quality
 */
#define HVNC_JPEG_QUALITY 90

/**
 * Frame Rate Limit (frames per second)
 *
 * Sets the maximum frames per second for screen capture and transmission.
 * This setting affects the smoothness of the remote desktop experience.
 *
 * - Higher values (45-60): Smoother video, more CPU usage, more bandwidth
 * - Medium values (20-44): Good balance for most use cases
 * - Lower values (10-19): Less smooth but usable, minimal resource usage
 *
 * RECOMMENDED VALUES:
 * - 30 (High Quality): Smooth experience for most applications
 * - 45 (Balanced): Good for gaming or video content
 * - 60 (High Performance): Maximum smoothness, highest resource usage
 */
#define HVNC_FRAME_RATE_LIMIT 30

/**
 * Differential Capture Threshold (0-255)
 *
 * Determines how much a pixel must change to be considered "different" and
 * included in the next frame update. This is crucial for compression efficiency.
 *
 * - Lower values (5-15): More sensitive to changes, more updates sent, better quality
 * - Medium values (16-35): Balanced sensitivity and compression
 * - Higher values (36-100): Less sensitive, better compression, may miss subtle changes
 *
 * RECOMMENDED VALUES:
 * - 25 (High Quality): Good balance between quality and performance
 * - 20 (Balanced): More sensitive to changes, slightly more bandwidth
 * - 15 (High Performance): Very sensitive, captures all changes
 */
#define HVNC_PIXEL_DIFF_THRESHOLD 25

/**
 * LZNT1 Compression Level (1-12)
 *
 * Controls the compression level for network data transmission using LZNT1 algorithm.
 * This affects both compression ratio and CPU usage.
 *
 * - Higher values (9-12): Better compression ratio, more CPU usage
 * - Medium values (5-8): Balanced compression and speed
 * - Lower values (1-4): Faster compression, larger network packets
 *
 * RECOMMENDED VALUES:
 * - 8 (High Quality): Optimal bandwidth usage with acceptable CPU overhead
 * - 6 (Balanced): Good compromise between compression and speed
 * - 4 (High Performance): Faster processing, less compression
 */
#define HVNC_COMPRESSION_LEVEL 8

// ============================================================================
// CALCULATED VALUES - DO NOT MODIFY THESE
// ============================================================================

/**
 * Capture Interval (milliseconds)
 *
 * Automatically calculated from the frame rate limit setting.
 * This determines how often screen captures are taken.
 *
 * Formula: 1000ms / HVNC_FRAME_RATE_LIMIT
 * Example: For 30 FPS, interval = 1000/30 = 33.33ms between captures
 */
#define HVNC_CAPTURE_INTERVAL_MS (1000 / HVNC_FRAME_RATE_LIMIT)

/**
 * Profile Information
 *
 * These constants define the current performance profile for logging and debugging.
 * They help identify which configuration preset is being used.
 */
#define HVNC_PROFILE_NAME "High Quality"
#define HVNC_PROFILE_DESCRIPTION "Design/Quality optimized"

/**
 * Profile Logging Macro
 *
 * Convenience macro to log the current performance configuration.
 * This is useful for debugging and performance analysis.
 *
 * Usage: HVNC_LOG_PROFILE_INFO();
 */
#define HVNC_LOG_PROFILE_INFO() \
    ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, \
        "HVNC Profile: %s | JPEG Quality: %d | Frame Rate: %d FPS | Compression: %d", \
        HVNC_PROFILE_NAME, HVNC_JPEG_QUALITY, HVNC_FRAME_RATE_LIMIT, HVNC_COMPRESSION_LEVEL)
