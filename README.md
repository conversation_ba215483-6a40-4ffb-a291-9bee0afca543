# HVNC - Hidden Virtual Network Computing

### Performance Optimizations
- TurboJPEG compression for superior image quality and speed
- Differential capture reduces bandwidth usage by 60-90%
- Configurable quality and performance settings
- Frame rate limiting and adaptive compression
- Multi-threaded capture and transmission pipeline

### Development Requirements
- Visual Studio 2019 or later with C++ support
- Windows SDK 10.0.19041.0 or later
- MSBuild tools
- Git for version control

## Architecture

### Client Component
The client runs on the target machine and handles:
- Screen capture from virtual or hidden desktops
- Image compression using TurboJPEG
- Network transmission of screen data
- Input command reception and injection
- Virtual desktop management (Windows 11)

### Server Component
The server runs on the control machine and provides:
- GUI interface for viewing remote desktop
- Mouse and keyboard input capture
- Network communication with client
- Virtual desktop control buttons
- Fullscreen and windowed display modes

### Network Protocol
- TCP-based communication with custom framing
- LZNT1 compression for network data
- Magic byte identification for packet validation
- Separate channels for screen data and input commands
- Heartbeat mechanism for connection monitoring

## Configuration

### Performance Settings
Edit `common/CompileTimeConfig.h` to adjust performance characteristics:

```cpp
// JPEG compression quality (1-100)
#define HVNC_JPEG_QUALITY 90

// Frame rate limit (frames per second)
#define HVNC_FRAME_RATE_LIMIT 30

// Differential capture threshold (0-255)
#define HVNC_PIXEL_DIFF_THRESHOLD 25

// Compression level (1-12)
#define HVNC_COMPRESSION_LEVEL 8
```

### Quality Profiles
- **High Quality (90/30/25/8)**: Best visual experience, higher bandwidth
- **Balanced (65/45/20/6)**: Good compromise between quality and performance
- **High Performance (35/60/15/4)**: Minimal bandwidth, acceptable quality

## Building the Project

### Quick Build
```batch

# Build both client and server
build.bat
```

### Manual Build
```batch
# Build client only
msbuild Client/HVNC.vcxproj /p:Configuration=Release /p:Platform=Win32

# Build server only
msbuild Server/Server.vcxproj /p:Configuration=Release /p:Platform=Win32
```

### Build Output
- Client executable: `Client/_bin/Release/Win32/Client.exe`
- Server executable: `Server/_bin/Release/Win32/Server.exe`

## Usage

### Starting the Server
1. Run `Server.exe` on the control machine
2. The server GUI will open with virtual desktop control buttons
3. Note the displayed IP address and port for client connection

### Connecting the Client
1. Run `Client.exe <server-ip> <port>` on the target machine
2. The client will connect and begin screen transmission
3. Use server GUI buttons to create and manage virtual desktops

### Virtual Desktop Controls
- **Create Virtual Desktop**: Creates a new Windows 11 virtual desktop
- **Switch to Virtual Desktop**: Switches client to the virtual desktop
- **Switch to Main Desktop**: Returns client to the original desktop

### Keyboard Shortcuts
- **F11**: Toggle fullscreen mode in server
- **Escape**: Exit fullscreen mode
- **Alt+F4**: Close server application

## Network Configuration

### Default Settings
- Default port: 4043
- Connection timeout: 30 seconds
- Heartbeat interval: 5 seconds
- Maximum packet size: 1 MB

### Firewall Configuration
Ensure the following ports are open:
- TCP port 4043 (or configured port) for main communication
- Allow both inbound and outbound connections for HVNC executables

## Troubleshooting

### Common Issues

**Client fails to connect**
- Verify server is running and listening
- Check firewall settings on both machines
- Ensure network connectivity between client and server
- Verify correct IP address and port

**Poor performance or lag**
- Reduce JPEG quality in configuration
- Lower frame rate limit
- Increase differential threshold
- Check network bandwidth and latency

**Virtual desktop creation fails**
- Requires Windows 11 for full support
- Ensure COM interfaces are properly initialized
- Check Windows virtual desktop feature is enabled
- Verify sufficient system resources

### Debug Information
Enable detailed logging by defining `ENABLE_DETAILED_LOGGING` in CompileTimeConfig.h.
Debug output includes:
- Network communication status
- Compression statistics
- Coordinate transformation details
- Virtual desktop operation results

## Performance Metrics

### Typical Performance
- Screen capture: 30-60 FPS depending on configuration
- Compression ratio: 10:1 to 50:1 depending on content
- Network bandwidth: 1-10 Mbps depending on activity
- Input latency: 10-50ms depending on network conditions

### Optimization Tips
- Use wired network connections when possible
- Close unnecessary applications on both machines
- Adjust quality settings based on network capacity
- Monitor CPU and memory usage during operation