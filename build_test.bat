@echo off
echo Building Keyboard Test Program...

:: Check if Visual Studio tools are available
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo Visual Studio compiler not found in PATH
    echo Trying to locate Visual Studio...
    
    :: Try to find and setup Visual Studio environment
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo Could not find Visual Studio installation
        echo Please run this from a Visual Studio Developer Command Prompt
        pause
        exit /b 1
    )
)

:: Compile the test program
echo Compiling KeyboardTest.cpp...
cl /EHsc /O2 KeyboardTest.cpp /Fe:KeyboardTest.exe user32.lib gdi32.lib

if %errorlevel% equ 0 (
    echo Build successful! KeyboardTest.exe created.
    echo.
    echo You can now run KeyboardTest.exe to test keyboard input functionality.
    echo The program will create a window that displays all keyboard events.
) else (
    echo Build failed!
    pause
    exit /b 1
)

pause
