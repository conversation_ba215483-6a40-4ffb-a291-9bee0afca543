[2025-07-30 11:57:20.789] [INFO] HVNC Client logging initialized
[2025-07-30 11:57:20.789] [INFO] HVNC Client starting - Host: **************, Port: 4043
[2025-07-30 11:57:20.789] [INFO] HVNC connection thread started successfully
[2025-07-30 11:57:20.789] [INFO] TurboJPEG wrapper initialized successfully
[2025-07-30 11:57:20.789] [INFO] TurboJPEG image processing initialized successfully
[2025-07-30 11:57:20.805] [INFO] Capture system initialized with TurboJPEG compression
[2025-07-30 11:57:20.805] [INFO] HVNC Profile: High Quality | JPEG Quality: 90 | Frame Rate: 30 FPS | Compression: 8
[2025-07-30 11:57:20.805] [INFO] DEBUG: Compile-time constants - Quality: 90, FPS: 30, Interval: 33
[2025-07-30 11:57:20.805] [INFO] MainThread: Starting HVNC connection process...
[2025-07-30 11:57:20.805] [INFO] MainThread: Target server: **************:4043
[2025-07-30 11:57:20.805] [INFO] MainThread: Windows 11 detected - will use virtual desktop capture mode
[2025-07-30 11:57:20.813] [INFO] MainThread: Step 1 - Testing connection with server...
[2025-07-30 11:57:20.813] [INFO] Socket created with default configuration
[2025-07-30 11:57:20.853] [INFO] Successfully connected to **************:4043
[2025-07-30 11:57:20.853] [INFO] MainThread: Connection test successful!
[2025-07-30 11:57:20.853] [INFO] MainThread: Step 2 - Preparing virtual desktop environment...
[2025-07-30 11:57:20.868] [INFO] VirtualDesktopCapture: Initializing Windows 11 Virtual Desktop Capture System (no desktop creation)
[2025-07-30 11:57:20.872] [WARN] VirtualDesktopCapture: Failed to create VirtualDesktopManagerInternal: 0x80040154
[2025-07-30 11:57:20.872] [INFO] VirtualDesktopCapture: VirtualDesktopManager created successfully
[2025-07-30 11:57:20.872] [WARN] VirtualDesktopCapture: Cannot store original desktop - no COM interface
[2025-07-30 11:57:20.884] [WARN] VirtualDesktopCapture: Failed to store original desktop
[2025-07-30 11:57:20.884] [INFO] VirtualDesktopCapture: Initializing screen capture system
[2025-07-30 11:57:20.886] [INFO] VirtualDesktopCapture: Desktop dimensions: 1920x1080
[2025-07-30 11:57:20.886] [INFO] VirtualDesktopCapture: Screen capture system initialized successfully
[2025-07-30 11:57:20.886] [INFO] VirtualDesktopCapture: Virtual desktop capture system initialized successfully (no desktop created)
[2025-07-30 11:57:20.886] [INFO] MainThread: Virtual desktop capture system initialized successfully (no desktop created)
[2025-07-30 11:57:20.886] [INFO] MainThread: Step 3 - Starting HVNC connection and control threads...
[2025-07-30 11:57:20.886] [INFO] MainThread: Creating input thread for HVNC control...
[2025-07-30 11:57:20.886] [INFO] MainThread: Input thread created successfully
[2025-07-30 11:57:20.886] [INFO] Socket created with default configuration
[2025-07-30 11:57:20.886] [INFO] Successfully connected to **************:4043
[2025-07-30 11:57:20.900] [INFO] InputThread: Using virtual desktop mode - skipping thread desktop setting
[2025-07-30 11:57:20.996] [INFO] InputThread: Connection established - assigning server to virtual desktop permanently
[2025-07-30 11:57:21.011] [ERROR] VirtualDesktopCapture: Cannot assign server to virtual desktop - missing interfaces
[2025-07-30 11:57:21.013] [INFO] VirtualDesktopCapture: Switching to target desktop using keyboard
[2025-07-30 11:57:21.282] [INFO] VirtualDesktopCapture: Desktop switch command sent
[2025-07-30 11:57:21.282] [INFO] InputThread: Server successfully assigned to virtual desktop - parallel operation active
[2025-07-30 11:57:21.282] [INFO] Socket created with default configuration
[2025-07-30 11:57:21.297] [INFO] Successfully connected to **************:4043
[2025-07-30 11:57:21.298] [INFO] DesktopThread: Using virtual desktop mode - skipping thread desktop setting
[2025-07-30 11:57:21.298] [INFO] Using single-threaded capture mode for compatibility
[2025-07-30 11:57:21.559] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:21.577] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 119974 bytes (9.1% ratio) in 15ms
[2025-07-30 11:57:21.577] [DEBUG] TurboJPEG compression successful: 784x561 -> 119974 bytes
[2025-07-30 11:57:21.577] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 119974 bytes JPEG
[2025-07-30 11:57:22.088] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:22.104] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 119974 bytes (9.1% ratio) in 16ms
[2025-07-30 11:57:22.104] [DEBUG] TurboJPEG compression successful: 784x561 -> 119974 bytes
[2025-07-30 11:57:22.104] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 119974 bytes JPEG
[2025-07-30 11:57:22.405] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:22.420] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 119974 bytes (9.1% ratio) in 15ms
[2025-07-30 11:57:22.420] [DEBUG] TurboJPEG compression successful: 784x561 -> 119974 bytes
[2025-07-30 11:57:22.420] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 119974 bytes JPEG
[2025-07-30 11:57:22.762] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:22.770] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:22.772] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:22.772] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:22.988] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:22.988] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:23.004] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:23.004] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:23.239] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:23.239] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:23.255] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:23.255] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:23.505] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:23.521] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:23.521] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:23.537] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:23.820] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:23.836] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 16ms
[2025-07-30 11:57:23.836] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:23.836] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:24.072] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:24.072] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:24.088] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:24.090] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:24.304] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:24.304] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:24.304] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:24.320] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:24.570] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:24.570] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:24.586] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:24.586] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:24.860] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:24.870] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:24.872] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:24.874] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:25.201] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:25.233] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 16ms
[2025-07-30 11:57:25.233] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:25.233] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:25.487] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:25.487] [DEBUG] TurboJPEG compression: 784x561, 1319472 bytes -> 124223 bytes (9.4% ratio) in 0ms
[2025-07-30 11:57:25.503] [DEBUG] TurboJPEG compression successful: 784x561 -> 124223 bytes
[2025-07-30 11:57:25.503] [DEBUG] BitmapToJpgOptimized: Compressed 784x561 to 124223 bytes JPEG
[2025-07-30 11:57:25.818] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:25.865] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 31ms
[2025-07-30 11:57:25.865] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:25.865] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:27.728] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:27.775] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 32ms
[2025-07-30 11:57:27.775] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:27.775] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:28.241] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:28.283] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 31ms
[2025-07-30 11:57:28.289] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:28.295] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:28.678] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:28.720] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 31ms
[2025-07-30 11:57:28.722] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:28.726] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:29.102] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:29.243] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 31ms
[2025-07-30 11:57:29.373] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:29.384] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:29.714] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:29.730] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 16ms
[2025-07-30 11:57:29.730] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:29.730] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:30.046] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:30.062] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 16ms
[2025-07-30 11:57:30.062] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:30.062] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:30.352] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:30.364] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 15ms
[2025-07-30 11:57:30.366] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:30.368] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:30.507] [INFO] Starting application: Command Prompt
[2025-07-30 11:57:30.507] [WARN] Failed to set thread desktop for CMD
[2025-07-30 11:57:30.522] [INFO] CMD launched successfully with PID: 17888 on desktop: 
[2025-07-30 11:57:30.751] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:30.765] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 0ms
[2025-07-30 11:57:30.765] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:30.765] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:31.080] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:31.096] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 362543 bytes (6.2% ratio) in 0ms
[2025-07-30 11:57:31.096] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 362543 bytes
[2025-07-30 11:57:31.096] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 362543 bytes JPEG
[2025-07-30 11:57:31.411] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:31.428] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248284 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:31.428] [DEBUG] VirtualDesktopCapture: Mouse input request - x:285, y:135, flags:0x8002
[2025-07-30 11:57:31.428] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248284 bytes
[2025-07-30 11:57:31.428] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:285,135 -> 9727,8191
[2025-07-30 11:57:31.428] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248284 bytes JPEG
[2025-07-30 11:57:31.443] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:285, y:135, flags:0x8002
[2025-07-30 11:57:31.651] [DEBUG] VirtualDesktopCapture: Mouse input request - x:285, y:135, flags:0x8004
[2025-07-30 11:57:31.651] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:285,135 -> 9727,8191
[2025-07-30 11:57:31.651] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:285, y:135, flags:0x8004
[2025-07-30 11:57:31.893] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:31.909] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:31.909] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:31.909] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:32.198] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:32.212] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:32.212] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:32.212] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:32.497] [DEBUG] VirtualDesktopCapture: Mouse input request - x:267, y:133, flags:0x8002
[2025-07-30 11:57:32.497] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:267,133 -> 9113,8070
[2025-07-30 11:57:32.513] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:267, y:133, flags:0x8002
[2025-07-30 11:57:32.513] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:32.545] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:32.561] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:57:32.569] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:57:32.705] [DEBUG] VirtualDesktopCapture: Mouse input request - x:267, y:133, flags:0x8004
[2025-07-30 11:57:32.705] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:267,133 -> 9113,8070
[2025-07-30 11:57:32.705] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:267, y:133, flags:0x8004
[2025-07-30 11:57:32.911] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:32.911] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:32.911] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:32.927] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:33.196] [DEBUG] VirtualDesktopCapture: Mouse input request - x:54, y:88, flags:0x8002
[2025-07-30 11:57:33.196] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:54,88 -> 1843,5339
[2025-07-30 11:57:33.212] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:54, y:88, flags:0x8002
[2025-07-30 11:57:33.325] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:33.341] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:33.357] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:57:33.361] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:57:33.452] [DEBUG] VirtualDesktopCapture: Mouse input request - x:54, y:88, flags:0x8004
[2025-07-30 11:57:33.452] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:54,88 -> 1843,5339
[2025-07-30 11:57:33.452] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:54, y:88, flags:0x8004
[2025-07-30 11:57:33.810] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:33.840] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:33.840] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:33.840] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:34.184] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x0 (DOWN)
[2025-07-30 11:57:34.189] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x0
[2025-07-30 11:57:34.316] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x2 (UP)
[2025-07-30 11:57:34.332] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x2
[2025-07-30 11:57:34.994] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:35.010] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247452 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:35.022] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247452 bytes
[2025-07-30 11:57:35.022] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247452 bytes JPEG
[2025-07-30 11:57:35.377] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:35.385] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247452 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:35.385] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247452 bytes
[2025-07-30 11:57:35.394] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247452 bytes JPEG
[2025-07-30 11:57:35.661] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:35.666] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247526 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:35.666] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247526 bytes
[2025-07-30 11:57:35.677] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247526 bytes JPEG
[2025-07-30 11:57:36.080] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:36.092] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247452 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:36.094] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247452 bytes
[2025-07-30 11:57:36.096] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247452 bytes JPEG
[2025-07-30 11:57:36.401] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x0 (DOWN)
[2025-07-30 11:57:36.408] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x0
[2025-07-30 11:57:36.636] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:36.659] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247526 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:36.659] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247526 bytes
[2025-07-30 11:57:36.672] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247526 bytes JPEG
[2025-07-30 11:57:36.843] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x2 (UP)
[2025-07-30 11:57:36.851] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x2
[2025-07-30 11:57:36.999] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:37.007] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247526 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:37.007] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247526 bytes
[2025-07-30 11:57:37.019] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247526 bytes JPEG
[2025-07-30 11:57:37.261] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:37.277] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:37.308] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:37.340] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247526 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:37.345] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247526 bytes
[2025-07-30 11:57:37.350] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247526 bytes JPEG
[2025-07-30 11:57:37.410] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:37.410] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:37.750] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:37.784] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247475 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:37.787] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247475 bytes
[2025-07-30 11:57:37.792] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247475 bytes JPEG
[2025-07-30 11:57:38.240] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:38.283] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247446 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:38.283] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247446 bytes
[2025-07-30 11:57:38.294] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247446 bytes JPEG
[2025-07-30 11:57:38.640] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:38.650] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247475 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:38.650] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247475 bytes
[2025-07-30 11:57:38.658] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247475 bytes JPEG
[2025-07-30 11:57:38.852] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:38.864] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:38.914] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:38.924] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:39.038] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:39.038] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:39.054] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:39.058] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:39.577] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:39.621] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:39.625] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:57:39.629] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:57:40.040] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:40.040] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:40.040] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:57:40.058] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:57:40.365] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:40.399] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 32ms
[2025-07-30 11:57:40.399] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:40.399] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:40.415] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x0 (DOWN)
[2025-07-30 11:57:40.415] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x0
[2025-07-30 11:57:40.574] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x2 (UP)
[2025-07-30 11:57:40.574] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x2
[2025-07-30 11:57:40.627] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x48 (H), flags:0x0 (DOWN)
[2025-07-30 11:57:40.632] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x48 (H), flags:0x0
[2025-07-30 11:57:40.802] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x48 (H), flags:0x2 (UP)
[2025-07-30 11:57:40.802] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x48 (H), flags:0x2
[2025-07-30 11:57:40.802] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:40.834] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247729 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:40.834] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247729 bytes
[2025-07-30 11:57:40.849] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247729 bytes JPEG
[2025-07-30 11:57:41.294] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:41.298] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247729 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:41.298] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247729 bytes
[2025-07-30 11:57:41.309] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247729 bytes JPEG
[2025-07-30 11:57:41.387] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:41.393] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:41.452] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:41.459] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:41.573] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:41.573] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:41.630] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:41.630] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:41.637] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:41.669] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247526 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:41.685] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247526 bytes
[2025-07-30 11:57:41.685] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247526 bytes JPEG
[2025-07-30 11:57:41.685] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:41.685] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:41.843] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:41.856] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:42.025] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:42.068] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247475 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:42.068] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247475 bytes
[2025-07-30 11:57:42.080] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247475 bytes JPEG
[2025-07-30 11:57:42.468] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:42.484] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247446 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:42.484] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247446 bytes
[2025-07-30 11:57:42.484] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247446 bytes JPEG
[2025-07-30 11:57:42.820] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x0 (DOWN)
[2025-07-30 11:57:42.827] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x0
[2025-07-30 11:57:42.869] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x2 (UP)
[2025-07-30 11:57:42.869] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x2
[2025-07-30 11:57:42.916] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:42.947] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247570 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:42.947] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247570 bytes
[2025-07-30 11:57:42.955] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247570 bytes JPEG
[2025-07-30 11:57:43.222] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:43.231] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:43.414] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:43.414] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:43.414] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:43.445] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247515 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:43.445] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247515 bytes
[2025-07-30 11:57:43.445] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247515 bytes JPEG
[2025-07-30 11:57:43.530] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:43.530] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:43.592] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:43.596] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:43.643] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:43.643] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:43.803] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:43.803] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:43.850] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:43.850] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:43.866] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:43.900] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 32ms
[2025-07-30 11:57:43.902] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:43.902] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:43.998] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:43.998] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:44.241] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:44.271] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 32ms
[2025-07-30 11:57:44.271] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:44.278] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:44.373] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x0 (DOWN)
[2025-07-30 11:57:44.373] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x0
[2025-07-30 11:57:44.504] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x2 (UP)
[2025-07-30 11:57:44.572] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:44.623] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x2
[2025-07-30 11:57:44.816] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x48 (H), flags:0x0 (DOWN)
[2025-07-30 11:57:44.816] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x48 (H), flags:0x0
[2025-07-30 11:57:44.821] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4F (O), flags:0x0 (DOWN)
[2025-07-30 11:57:44.821] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4F (O), flags:0x0
[2025-07-30 11:57:44.821] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x48 (H), flags:0x2 (UP)
[2025-07-30 11:57:44.821] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x48 (H), flags:0x2
[2025-07-30 11:57:44.821] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4F (O), flags:0x2 (UP)
[2025-07-30 11:57:44.821] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4F (O), flags:0x2
[2025-07-30 11:57:44.821] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247518 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:44.837] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247518 bytes
[2025-07-30 11:57:44.841] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247518 bytes JPEG
[2025-07-30 11:57:45.264] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x41 (A), flags:0x0 (DOWN)
[2025-07-30 11:57:45.264] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x41 (A), flags:0x0
[2025-07-30 11:57:45.421] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x41 (A), flags:0x2 (UP)
[2025-07-30 11:57:45.423] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x41 (A), flags:0x2
[2025-07-30 11:57:45.425] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D (M), flags:0x0 (DOWN)
[2025-07-30 11:57:45.430] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4D (M), flags:0x0
[2025-07-30 11:57:45.435] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:45.471] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247927 bytes (4.3% ratio) in 32ms
[2025-07-30 11:57:45.473] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247927 bytes
[2025-07-30 11:57:45.480] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247927 bytes JPEG
[2025-07-30 11:57:45.705] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D (M), flags:0x2 (UP)
[2025-07-30 11:57:45.705] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4D (M), flags:0x2
[2025-07-30 11:57:45.818] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:45.818] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247963 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:45.834] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247963 bytes
[2025-07-30 11:57:45.834] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247963 bytes JPEG
[2025-07-30 11:57:46.315] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:46.329] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247939 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:46.331] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247939 bytes
[2025-07-30 11:57:46.335] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247939 bytes JPEG
[2025-07-30 11:57:46.702] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:46.712] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247939 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:46.714] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247939 bytes
[2025-07-30 11:57:46.718] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247939 bytes JPEG
[2025-07-30 11:57:47.080] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:47.096] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247963 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:47.096] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247963 bytes
[2025-07-30 11:57:47.102] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247963 bytes JPEG
[2025-07-30 11:57:47.479] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:47.511] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247939 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:47.511] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247939 bytes
[2025-07-30 11:57:47.527] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247939 bytes JPEG
[2025-07-30 11:57:47.850] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:47.861] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247939 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:47.863] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247939 bytes
[2025-07-30 11:57:47.866] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247939 bytes JPEG
[2025-07-30 11:57:48.582] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x49 (I), flags:0x0 (DOWN)
[2025-07-30 11:57:48.582] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x49 (I), flags:0x0
[2025-07-30 11:57:48.630] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x49 (I), flags:0x2 (UP)
[2025-07-30 11:57:48.645] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x49 (I), flags:0x2
[2025-07-30 11:57:48.817] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:48.817] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:48.833] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:48.833] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:49.163] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:49.163] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:49.163] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:49.181] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:49.548] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:49.582] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248102 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:49.582] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248102 bytes
[2025-07-30 11:57:49.592] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248102 bytes JPEG
[2025-07-30 11:57:49.915] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:49.915] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:49.931] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:49.931] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:50.120] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:50.121] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:50.295] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:50.295] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:50.311] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:50.326] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248052 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:50.326] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248052 bytes
[2025-07-30 11:57:50.332] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248052 bytes JPEG
[2025-07-30 11:57:50.710] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:50.742] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248052 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:50.742] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248052 bytes
[2025-07-30 11:57:50.742] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248052 bytes JPEG
[2025-07-30 11:57:50.984] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:50.984] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.143] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:51.159] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247963 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:51.159] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247963 bytes
[2025-07-30 11:57:51.159] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247963 bytes JPEG
[2025-07-30 11:57:51.510] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.510] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.510] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.510] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.558] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.574] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.574] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.574] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.590] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:51.622] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247927 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:51.622] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247927 bytes
[2025-07-30 11:57:51.622] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.630] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.634] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247927 bytes JPEG
[2025-07-30 11:57:51.634] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.634] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.669] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.669] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.749] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.749] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.749] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.749] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.796] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.796] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.796] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.812] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.844] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.844] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.900] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.900] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.900] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.900] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:51.948] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:51.948] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.011] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.011] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.011] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.011] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.059] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.059] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.059] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.059] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.107] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:52.139] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:52.139] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:52.148] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:52.287] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.291] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.291] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.291] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.298] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.298] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.330] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.330] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.330] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.330] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.330] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.346] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.346] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.346] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.346] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.346] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.378] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.387] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.432] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.432] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.432] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.448] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.487] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.487] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.528] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.539] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.539] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.544] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.560] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:52.587] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:52.587] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:52.592] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:52.602] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 11:57:52.602] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 11:57:52.608] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 11:57:52.608] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 11:57:52.913] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:52.913] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:52.913] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:57:52.929] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:57:53.071] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x0 (DOWN)
[2025-07-30 11:57:53.071] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x0
[2025-07-30 11:57:53.206] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x2 (UP)
[2025-07-30 11:57:53.212] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x2
[2025-07-30 11:57:53.263] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:53.273] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247518 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:53.275] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247518 bytes
[2025-07-30 11:57:53.279] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247518 bytes JPEG
[2025-07-30 11:57:53.595] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:53.632] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247518 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:53.636] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247518 bytes
[2025-07-30 11:57:53.636] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247518 bytes JPEG
[2025-07-30 11:57:53.706] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x48 (H), flags:0x0 (DOWN)
[2025-07-30 11:57:53.706] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x48 (H), flags:0x0
[2025-07-30 11:57:53.864] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x48 (H), flags:0x2 (UP)
[2025-07-30 11:57:53.864] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x48 (H), flags:0x2
[2025-07-30 11:57:53.993] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:53.997] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4F (O), flags:0x0 (DOWN)
[2025-07-30 11:57:53.997] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4F (O), flags:0x0
[2025-07-30 11:57:54.025] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247704 bytes (4.3% ratio) in 32ms
[2025-07-30 11:57:54.029] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247704 bytes
[2025-07-30 11:57:54.035] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247704 bytes JPEG
[2025-07-30 11:57:54.215] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4F (O), flags:0x2 (UP)
[2025-07-30 11:57:54.220] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4F (O), flags:0x2
[2025-07-30 11:57:54.462] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:54.499] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247773 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:54.501] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247773 bytes
[2025-07-30 11:57:54.507] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247773 bytes JPEG
[2025-07-30 11:57:54.861] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:54.875] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247697 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:54.877] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247697 bytes
[2025-07-30 11:57:54.877] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x41 (A), flags:0x0 (DOWN)
[2025-07-30 11:57:54.877] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x41 (A), flags:0x0
[2025-07-30 11:57:54.877] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247697 bytes JPEG
[2025-07-30 11:57:55.088] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x41 (A), flags:0x2 (UP)
[2025-07-30 11:57:55.091] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x41 (A), flags:0x2
[2025-07-30 11:57:55.212] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D (M), flags:0x0 (DOWN)
[2025-07-30 11:57:55.216] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4D (M), flags:0x0
[2025-07-30 11:57:55.305] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:55.335] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247963 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:55.335] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247963 bytes
[2025-07-30 11:57:55.335] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247963 bytes JPEG
[2025-07-30 11:57:55.427] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4D (M), flags:0x2 (UP)
[2025-07-30 11:57:55.431] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4D (M), flags:0x2
[2025-07-30 11:57:55.682] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x49 (I), flags:0x0 (DOWN)
[2025-07-30 11:57:55.682] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x49 (I), flags:0x0
[2025-07-30 11:57:55.759] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:55.798] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:55.801] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:55.801] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:55.942] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x49 (I), flags:0x2 (UP)
[2025-07-30 11:57:55.946] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x49 (I), flags:0x2
[2025-07-30 11:57:56.174] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:56.209] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:56.211] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:56.219] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:56.588] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:56.620] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248102 bytes (4.3% ratio) in 31ms
[2025-07-30 11:57:56.628] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248102 bytes
[2025-07-30 11:57:56.636] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248102 bytes JPEG
[2025-07-30 11:57:56.994] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:57.004] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248102 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:57.008] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248102 bytes
[2025-07-30 11:57:57.012] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248102 bytes JPEG
[2025-07-30 11:57:57.371] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:57.393] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:57.395] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:57.395] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:57.753] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:57.795] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248102 bytes (4.3% ratio) in 15ms
[2025-07-30 11:57:57.801] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248102 bytes
[2025-07-30 11:57:57.801] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248102 bytes JPEG
[2025-07-30 11:57:58.188] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:58.204] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:58.204] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:58.204] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:58.236] [DEBUG] VirtualDesktopCapture: Mouse input request - x:768, y:27, flags:0x8002
[2025-07-30 11:57:58.236] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:768,27 -> 26214,1638
[2025-07-30 11:57:58.252] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:768, y:27, flags:0x8002
[2025-07-30 11:57:58.538] [DEBUG] VirtualDesktopCapture: Mouse input request - x:768, y:27, flags:0x8004
[2025-07-30 11:57:58.538] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:768,27 -> 26214,1638
[2025-07-30 11:57:58.538] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:768, y:27, flags:0x8004
[2025-07-30 11:57:58.570] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:58.602] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 16ms
[2025-07-30 11:57:58.602] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:58.602] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:58.954] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:59.097] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 32ms
[2025-07-30 11:57:59.241] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:59.257] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:57:59.715] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:57:59.731] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 0ms
[2025-07-30 11:57:59.731] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:57:59.747] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:58:00.136] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:00.168] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 15ms
[2025-07-30 11:58:00.184] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:58:00.189] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:58:00.301] [DEBUG] VirtualDesktopCapture: Mouse input request - x:410, y:130, flags:0x8002
[2025-07-30 11:58:00.307] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:410,130 -> 13994,7888
[2025-07-30 11:58:00.311] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:410, y:130, flags:0x8002
[2025-07-30 11:58:00.489] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:00.489] [DEBUG] VirtualDesktopCapture: Mouse input request - x:410, y:130, flags:0x8004
[2025-07-30 11:58:00.498] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:410,130 -> 13994,7888
[2025-07-30 11:58:00.501] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:410, y:130, flags:0x8004
[2025-07-30 11:58:00.505] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248102 bytes (4.3% ratio) in 16ms
[2025-07-30 11:58:00.507] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248102 bytes
[2025-07-30 11:58:00.510] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248102 bytes JPEG
[2025-07-30 11:58:00.836] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:00.852] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248149 bytes (4.3% ratio) in 16ms
[2025-07-30 11:58:00.852] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248149 bytes
[2025-07-30 11:58:00.852] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248149 bytes JPEG
[2025-07-30 11:58:01.302] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:01.329] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248102 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:01.329] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248102 bytes
[2025-07-30 11:58:01.345] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248102 bytes JPEG
[2025-07-30 11:58:02.108] [DEBUG] VirtualDesktopCapture: Mouse input request - x:183, y:-37, flags:0x8002
[2025-07-30 11:58:02.111] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:183,-37 -> 6246,0
[2025-07-30 11:58:02.124] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:183, y:-37, flags:0x8002
[2025-07-30 11:58:02.124] [DEBUG] VirtualDesktopCapture: Mouse input request - x:183, y:-37, flags:0x8004
[2025-07-30 11:58:02.124] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:183,-37 -> 6246,0
[2025-07-30 11:58:02.124] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:183, y:-37, flags:0x8004
[2025-07-30 11:58:02.413] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:02.418] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:02.583] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:02.631] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:02.631] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:02.631] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 31ms
[2025-07-30 11:58:02.631] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:02.640] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:02.936] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:02.952] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 15ms
[2025-07-30 11:58:02.952] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:02.952] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:03.314] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:03.346] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 32ms
[2025-07-30 11:58:03.346] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:03.346] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:03.603] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:03.605] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:03.702] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:03.741] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 31ms
[2025-07-30 11:58:03.748] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:03.748] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:03.770] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:03.770] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:04.218] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:04.244] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 15ms
[2025-07-30 11:58:04.260] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:04.260] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:04.650] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:04.682] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 16ms
[2025-07-30 11:58:04.698] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:04.700] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:05.020] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:05.031] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:05.035] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:05.036] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:05.470] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:05.478] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:05.478] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:05.478] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:05.904] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:05.944] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 31ms
[2025-07-30 11:58:05.944] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:05.944] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:06.315] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:06.331] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 16ms
[2025-07-30 11:58:06.333] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:06.333] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:06.812] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:06.851] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 47ms
[2025-07-30 11:58:06.851] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:06.863] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:07.145] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x5B (WIN), flags:0x0 (DOWN)
[2025-07-30 11:58:07.152] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x5B (WIN), flags:0x0
[2025-07-30 11:58:07.247] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:07.261] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:07.263] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:07.263] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:07.343] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:07.343] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:07.557] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:07.557] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:07.643] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:07.675] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 31ms
[2025-07-30 11:58:07.676] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:07.681] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x5B (WIN), flags:0x2 (UP)
[2025-07-30 11:58:07.683] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:07.684] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x5B (WIN), flags:0x2
[2025-07-30 11:58:08.080] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:08.098] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 16ms
[2025-07-30 11:58:08.100] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:08.105] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:08.348] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:08.358] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:08.365] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:08.365] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:08.380] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x0 (DOWN)
[2025-07-30 11:58:08.380] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x0
[2025-07-30 11:58:08.745] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:08.760] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 16ms
[2025-07-30 11:58:08.760] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:08.760] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:09.047] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:09.047] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:09.063] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:09.063] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:09.396] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:09.396] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:09.412] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:09.415] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:09.844] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:09.859] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 16ms
[2025-07-30 11:58:09.859] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:09.859] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:10.178] [DEBUG] VirtualDesktopCapture: Mouse input request - x:195, y:48, flags:0x8002
[2025-07-30 11:58:10.178] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:195,48 -> 6655,2912
[2025-07-30 11:58:10.178] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:10.194] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:195, y:48, flags:0x8002
[2025-07-30 11:58:10.226] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 32ms
[2025-07-30 11:58:10.226] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:10.226] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:10.605] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:10.634] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 16ms
[2025-07-30 11:58:10.643] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:10.643] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:11.013] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:11.013] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:11.017] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:11.040] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 15ms
[2025-07-30 11:58:11.042] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:11.047] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:11.170] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:11.180] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:11.494] [DEBUG] VirtualDesktopCapture: Mouse input request - x:195, y:48, flags:0x8002
[2025-07-30 11:58:11.506] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:195,48 -> 6655,2912
[2025-07-30 11:58:11.541] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:195, y:48, flags:0x8002
[2025-07-30 11:58:11.557] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:11.575] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287639 bytes (4.9% ratio) in 0ms
[2025-07-30 11:58:11.583] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287639 bytes
[2025-07-30 11:58:11.589] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287639 bytes JPEG
[2025-07-30 11:58:11.953] [DEBUG] VirtualDesktopCapture: Mouse input request - x:195, y:48, flags:0x8004
[2025-07-30 11:58:11.953] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:195,48 -> 6655,2912
[2025-07-30 11:58:11.953] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:195, y:48, flags:0x8004
[2025-07-30 11:58:12.498] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:12.505] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:12.505] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:12.505] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:12.930] [DEBUG] VirtualDesktopCapture: Mouse input request - x:169, y:95, flags:0x8002
[2025-07-30 11:58:12.946] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:169,95 -> 5768,5764
[2025-07-30 11:58:12.966] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:169, y:95, flags:0x8002
[2025-07-30 11:58:13.123] [DEBUG] VirtualDesktopCapture: Mouse input request - x:169, y:95, flags:0x8004
[2025-07-30 11:58:13.123] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:169,95 -> 5768,5764
[2025-07-30 11:58:13.123] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:169, y:95, flags:0x8004
[2025-07-30 11:58:13.171] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:13.187] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:13.187] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:13.199] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:13.323] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:13.330] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:13.463] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:13.463] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:13.759] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:13.786] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287647 bytes (4.9% ratio) in 31ms
[2025-07-30 11:58:13.790] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287647 bytes
[2025-07-30 11:58:13.796] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287647 bytes JPEG
[2025-07-30 11:58:14.560] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:14.576] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287647 bytes (4.9% ratio) in 0ms
[2025-07-30 11:58:14.576] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287647 bytes
[2025-07-30 11:58:14.576] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287647 bytes JPEG
[2025-07-30 11:58:14.889] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:14.923] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287647 bytes (4.9% ratio) in 31ms
[2025-07-30 11:58:14.925] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287647 bytes
[2025-07-30 11:58:14.925] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287647 bytes JPEG
[2025-07-30 11:58:15.355] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:15.387] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287626 bytes (4.9% ratio) in 31ms
[2025-07-30 11:58:15.387] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287626 bytes
[2025-07-30 11:58:15.403] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287626 bytes JPEG
[2025-07-30 11:58:15.742] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:15.758] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287647 bytes (4.9% ratio) in 16ms
[2025-07-30 11:58:15.758] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287647 bytes
[2025-07-30 11:58:15.758] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287647 bytes JPEG
[2025-07-30 11:58:16.154] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:16.201] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287626 bytes (4.9% ratio) in 31ms
[2025-07-30 11:58:16.201] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287626 bytes
[2025-07-30 11:58:16.201] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287626 bytes JPEG
[2025-07-30 11:58:17.563] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:17.602] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287626 bytes (4.9% ratio) in 32ms
[2025-07-30 11:58:17.602] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287626 bytes
[2025-07-30 11:58:17.602] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287626 bytes JPEG
[2025-07-30 11:58:17.624] [DEBUG] VirtualDesktopCapture: Mouse input request - x:739, y:828, flags:0x8002
[2025-07-30 11:58:17.627] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:739,828 -> 25224,50243
[2025-07-30 11:58:17.644] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:739, y:828, flags:0x8002
[2025-07-30 11:58:17.830] [DEBUG] VirtualDesktopCapture: Mouse input request - x:739, y:828, flags:0x8004
[2025-07-30 11:58:17.830] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:739,828 -> 25224,50243
[2025-07-30 11:58:17.830] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:739, y:828, flags:0x8004
[2025-07-30 11:58:17.931] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:17.941] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:17.941] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:17.941] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:18.504] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:18.520] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246276 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:18.520] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246276 bytes
[2025-07-30 11:58:18.520] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246276 bytes JPEG
[2025-07-30 11:58:18.774] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:18.790] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:18.842] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:18.868] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247345 bytes (4.3% ratio) in 15ms
[2025-07-30 11:58:18.868] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247345 bytes
[2025-07-30 11:58:18.868] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247345 bytes JPEG
[2025-07-30 11:58:18.947] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:18.963] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:19.244] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:19.283] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287647 bytes (4.9% ratio) in 31ms
[2025-07-30 11:58:19.292] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287647 bytes
[2025-07-30 11:58:19.299] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287647 bytes JPEG
[2025-07-30 11:58:19.839] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28 (DOWN), flags:0x0 (DOWN)
[2025-07-30 11:58:19.852] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x28 (DOWN), flags:0x0
[2025-07-30 11:58:19.986] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:20.002] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 288304 bytes (5.0% ratio) in 31ms
[2025-07-30 11:58:20.002] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 288304 bytes
[2025-07-30 11:58:20.011] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 288304 bytes JPEG
[2025-07-30 11:58:20.065] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28 (DOWN), flags:0x2 (UP)
[2025-07-30 11:58:20.065] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x28 (DOWN), flags:0x2
[2025-07-30 11:58:20.322] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:20.368] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 288666 bytes (5.0% ratio) in 31ms
[2025-07-30 11:58:20.370] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 288666 bytes
[2025-07-30 11:58:20.370] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 288666 bytes JPEG
[2025-07-30 11:58:20.655] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x0 (DOWN)
[2025-07-30 11:58:20.655] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x0
[2025-07-30 11:58:20.788] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x2 (UP)
[2025-07-30 11:58:20.795] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x2
[2025-07-30 11:58:20.802] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:20.833] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 288685 bytes (5.0% ratio) in 31ms
[2025-07-30 11:58:20.833] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 288685 bytes
[2025-07-30 11:58:20.850] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 288685 bytes JPEG
[2025-07-30 11:58:21.169] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:21.215] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 288685 bytes (5.0% ratio) in 16ms
[2025-07-30 11:58:21.217] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 288685 bytes
[2025-07-30 11:58:21.217] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 288685 bytes JPEG
[2025-07-30 11:58:21.447] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 11:58:21.452] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 11:58:21.583] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:21.591] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 11:58:21.591] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 11:58:21.608] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287950 bytes (5.0% ratio) in 16ms
[2025-07-30 11:58:21.614] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287950 bytes
[2025-07-30 11:58:21.618] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287950 bytes JPEG
[2025-07-30 11:58:22.185] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:22.222] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131080 bytes (2.3% ratio) in 32ms
[2025-07-30 11:58:22.222] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131080 bytes
[2025-07-30 11:58:22.230] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131080 bytes JPEG
[2025-07-30 11:58:23.072] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:23.100] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131080 bytes (2.3% ratio) in 32ms
[2025-07-30 11:58:23.100] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131080 bytes
[2025-07-30 11:58:23.116] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131080 bytes JPEG
[2025-07-30 11:58:23.576] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:23.602] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 131008 bytes (2.3% ratio) in 16ms
[2025-07-30 11:58:23.606] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 131008 bytes
[2025-07-30 11:58:23.612] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 131008 bytes JPEG
[2025-07-30 11:58:24.069] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:24.110] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 255768 bytes (4.4% ratio) in 16ms
[2025-07-30 11:58:24.110] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 255768 bytes
[2025-07-30 11:58:24.120] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 255768 bytes JPEG
[2025-07-30 11:58:24.755] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:24.787] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 284947 bytes (4.9% ratio) in 15ms
[2025-07-30 11:58:24.787] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 284947 bytes
[2025-07-30 11:58:24.809] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 284947 bytes JPEG
[2025-07-30 11:58:25.010] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x0 (DOWN)
[2025-07-30 11:58:25.010] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x0
[2025-07-30 11:58:25.105] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x2 (UP)
[2025-07-30 11:58:25.105] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x2
[2025-07-30 11:58:25.319] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:25.357] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 285832 bytes (4.9% ratio) in 16ms
[2025-07-30 11:58:25.369] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 285832 bytes
[2025-07-30 11:58:25.379] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 285832 bytes JPEG
[2025-07-30 11:58:25.742] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:25.787] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 285142 bytes (4.9% ratio) in 31ms
[2025-07-30 11:58:25.787] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 285142 bytes
[2025-07-30 11:58:25.787] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 285142 bytes JPEG
[2025-07-30 11:58:26.260] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:26.271] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 285247 bytes (4.9% ratio) in 0ms
[2025-07-30 11:58:26.289] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 285247 bytes
[2025-07-30 11:58:26.298] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 285247 bytes JPEG
[2025-07-30 11:58:26.784] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:26.818] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 288626 bytes (5.0% ratio) in 31ms
[2025-07-30 11:58:26.824] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 288626 bytes
[2025-07-30 11:58:26.828] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 288626 bytes JPEG
[2025-07-30 11:58:27.328] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x0 (DOWN)
[2025-07-30 11:58:27.330] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x0
[2025-07-30 11:58:27.432] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:27.455] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x48 (H), flags:0x0 (DOWN)
[2025-07-30 11:58:27.457] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 338461 bytes (5.8% ratio) in 16ms
[2025-07-30 11:58:27.457] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x48 (H), flags:0x0
[2025-07-30 11:58:27.457] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 338461 bytes
[2025-07-30 11:58:27.461] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x2 (UP)
[2025-07-30 11:58:27.461] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x2
[2025-07-30 11:58:27.461] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 338461 bytes JPEG
[2025-07-30 11:58:27.661] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x48 (H), flags:0x2 (UP)
[2025-07-30 11:58:27.663] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x48 (H), flags:0x2
[2025-07-30 11:58:27.974] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:28.013] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:28.020] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:28.025] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:29.157] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:29.173] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:29.173] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:29.173] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:29.548] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:29.559] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246343 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:29.559] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246343 bytes
[2025-07-30 11:58:29.564] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246343 bytes JPEG
[2025-07-30 11:58:30.041] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:30.264] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:30.297] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:30.297] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:30.773] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:30.821] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246343 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:30.825] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246343 bytes
[2025-07-30 11:58:30.831] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246343 bytes JPEG
[2025-07-30 11:58:31.210] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:31.243] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:31.243] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:31.253] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:31.660] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:31.676] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246343 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:31.676] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246343 bytes
[2025-07-30 11:58:31.684] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246343 bytes JPEG
[2025-07-30 11:58:32.098] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:32.114] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 15ms
[2025-07-30 11:58:32.116] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:32.121] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:32.425] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x0 (DOWN)
[2025-07-30 11:58:32.441] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x0
[2025-07-30 11:58:32.488] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:32.537] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:32.537] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:32.537] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:32.800] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x2 (UP)
[2025-07-30 11:58:32.805] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x2
[2025-07-30 11:58:32.912] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:32.926] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:32.926] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:32.926] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:33.296] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:33.338] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:33.338] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:33.338] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:34.088] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x5B (WIN), flags:0x0 (DOWN)
[2025-07-30 11:58:34.088] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x5B (WIN), flags:0x0
[2025-07-30 11:58:34.472] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:34.519] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246343 bytes (4.2% ratio) in 32ms
[2025-07-30 11:58:34.519] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246343 bytes
[2025-07-30 11:58:34.528] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246343 bytes JPEG
[2025-07-30 11:58:34.954] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:34.986] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246343 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:34.986] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246343 bytes
[2025-07-30 11:58:35.006] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246343 bytes JPEG
[2025-07-30 11:58:35.387] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:35.419] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:35.419] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:35.435] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:35.796] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:35.821] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246343 bytes (4.2% ratio) in 15ms
[2025-07-30 11:58:35.823] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246343 bytes
[2025-07-30 11:58:35.829] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246343 bytes JPEG
[2025-07-30 11:58:36.229] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:36.248] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:36.250] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:36.254] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:36.574] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:36.586] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:36.590] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:36.590] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:36.681] [DEBUG] VirtualDesktopCapture: Mouse input request - x:739, y:828, flags:0x8002
[2025-07-30 11:58:36.683] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:739,828 -> 25224,50243
[2025-07-30 11:58:36.695] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:739, y:828, flags:0x8002
[2025-07-30 11:58:36.891] [DEBUG] VirtualDesktopCapture: Mouse input request - x:739, y:828, flags:0x8004
[2025-07-30 11:58:36.891] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:739,828 -> 25224,50243
[2025-07-30 11:58:36.891] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:739, y:828, flags:0x8004
[2025-07-30 11:58:36.907] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x5B (WIN), flags:0x0 (DOWN)
[2025-07-30 11:58:36.908] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x5B (WIN), flags:0x0
[2025-07-30 11:58:36.988] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:37.004] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:37.008] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:37.012] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:37.321] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:37.358] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 32ms
[2025-07-30 11:58:37.360] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:37.362] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:37.777] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:37.800] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246343 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:37.800] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246343 bytes
[2025-07-30 11:58:37.814] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246343 bytes JPEG
[2025-07-30 11:58:38.234] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:38.268] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 32ms
[2025-07-30 11:58:38.268] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:38.283] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:38.393] [DEBUG] VirtualDesktopCapture: Mouse input request - x:99, y:283, flags:0x8002
[2025-07-30 11:58:38.393] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:99,283 -> 3379,17172
[2025-07-30 11:58:38.409] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:99, y:283, flags:0x8002
[2025-07-30 11:58:38.636] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:38.684] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246343 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:38.695] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246343 bytes
[2025-07-30 11:58:38.701] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246343 bytes JPEG
[2025-07-30 11:58:38.842] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xC0 (UNKNOWN), flags:0x0 (DOWN)
[2025-07-30 11:58:38.842] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xC0 (UNKNOWN), flags:0x0
[2025-07-30 11:58:39.024] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xC0 (UNKNOWN), flags:0x2 (UP)
[2025-07-30 11:58:39.024] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xC0 (UNKNOWN), flags:0x2
[2025-07-30 11:58:39.113] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:39.135] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:39.135] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:39.155] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:39.730] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:39.745] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247429 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:39.745] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247429 bytes
[2025-07-30 11:58:39.763] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247429 bytes JPEG
[2025-07-30 11:58:40.171] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:40.207] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 158969 bytes (2.7% ratio) in 31ms
[2025-07-30 11:58:40.207] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 158969 bytes
[2025-07-30 11:58:40.217] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 158969 bytes JPEG
[2025-07-30 11:58:40.619] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:40.651] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 158902 bytes (2.7% ratio) in 16ms
[2025-07-30 11:58:40.651] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 158902 bytes
[2025-07-30 11:58:40.661] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 158902 bytes JPEG
[2025-07-30 11:58:41.073] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:41.099] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 158969 bytes (2.7% ratio) in 16ms
[2025-07-30 11:58:41.099] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 158969 bytes
[2025-07-30 11:58:41.109] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 158969 bytes JPEG
[2025-07-30 11:58:41.838] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:41.861] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 158902 bytes (2.7% ratio) in 15ms
[2025-07-30 11:58:41.870] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 158902 bytes
[2025-07-30 11:58:41.877] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 158902 bytes JPEG
[2025-07-30 11:58:42.430] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:42.464] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 158969 bytes (2.7% ratio) in 16ms
[2025-07-30 11:58:42.464] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 158969 bytes
[2025-07-30 11:58:42.474] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 158969 bytes JPEG
[2025-07-30 11:58:42.613] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xC0 (UNKNOWN), flags:0x0 (DOWN)
[2025-07-30 11:58:42.617] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xC0 (UNKNOWN), flags:0x0
[2025-07-30 11:58:42.775] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xC0 (UNKNOWN), flags:0x2 (UP)
[2025-07-30 11:58:42.775] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xC0 (UNKNOWN), flags:0x2
[2025-07-30 11:58:43.119] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:43.150] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246509 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:43.150] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246509 bytes
[2025-07-30 11:58:43.162] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246509 bytes JPEG
[2025-07-30 11:58:43.434] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:43.475] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 32ms
[2025-07-30 11:58:43.475] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:43.486] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:43.913] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:43.945] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246539 bytes (4.2% ratio) in 15ms
[2025-07-30 11:58:43.945] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246539 bytes
[2025-07-30 11:58:43.958] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246539 bytes JPEG
[2025-07-30 11:58:44.039] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x0 (DOWN)
[2025-07-30 11:58:44.039] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x0
[2025-07-30 11:58:44.193] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xC0 (UNKNOWN), flags:0x0 (DOWN)
[2025-07-30 11:58:44.194] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xC0 (UNKNOWN), flags:0x0
[2025-07-30 11:58:44.378] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xC0 (UNKNOWN), flags:0x2 (UP)
[2025-07-30 11:58:44.378] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xC0 (UNKNOWN), flags:0x2
[2025-07-30 11:58:44.442] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x2 (UP)
[2025-07-30 11:58:44.442] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x2
[2025-07-30 11:58:44.490] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:44.507] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:44.507] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:44.521] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:44.931] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:44.947] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 15ms
[2025-07-30 11:58:44.947] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:44.947] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:45.078] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x0 (DOWN)
[2025-07-30 11:58:45.078] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x0
[2025-07-30 11:58:45.296] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:45.312] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:45.312] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:45.312] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:45.454] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xC0 (UNKNOWN), flags:0x0 (DOWN)
[2025-07-30 11:58:45.454] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xC0 (UNKNOWN), flags:0x0
[2025-07-30 11:58:45.454] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xC0 (UNKNOWN), flags:0x2 (UP)
[2025-07-30 11:58:45.454] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xC0 (UNKNOWN), flags:0x2
[2025-07-30 11:58:45.661] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:45.693] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 15ms
[2025-07-30 11:58:45.709] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:45.715] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:46.017] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:46.054] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:46.054] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:46.066] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:46.223] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x2 (UP)
[2025-07-30 11:58:46.231] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x2
[2025-07-30 11:58:46.402] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:46.430] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:46.430] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:46.437] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:46.812] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:46.828] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246539 bytes (4.2% ratio) in 15ms
[2025-07-30 11:58:46.828] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246539 bytes
[2025-07-30 11:58:46.831] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246539 bytes JPEG
[2025-07-30 11:58:46.880] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x0 (DOWN)
[2025-07-30 11:58:46.880] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x0
[2025-07-30 11:58:47.212] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:47.242] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 15ms
[2025-07-30 11:58:47.242] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:47.258] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:47.726] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:47.773] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246539 bytes (4.2% ratio) in 32ms
[2025-07-30 11:58:47.773] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246539 bytes
[2025-07-30 11:58:47.782] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246539 bytes JPEG
[2025-07-30 11:58:48.085] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x2 (UP)
[2025-07-30 11:58:48.085] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x2
[2025-07-30 11:58:48.215] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:48.215] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246605 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:48.229] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246605 bytes
[2025-07-30 11:58:48.231] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246605 bytes JPEG
[2025-07-30 11:58:48.849] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x0 (DOWN)
[2025-07-30 11:58:48.849] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x0
[2025-07-30 11:58:48.849] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:48.849] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:48.961] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:48.977] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:48.977] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x2 (UP)
[2025-07-30 11:58:48.977] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x2
[2025-07-30 11:58:49.391] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:49.423] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:49.439] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:49.444] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:49.566] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x0 (DOWN)
[2025-07-30 11:58:49.566] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x0
[2025-07-30 11:58:49.715] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:49.733] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:49.794] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:49.809] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:49.809] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:49.816] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:49.857] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:49.857] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:49.905] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x2 (UP)
[2025-07-30 11:58:49.914] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x2
[2025-07-30 11:58:50.159] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:50.207] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 31ms
[2025-07-30 11:58:50.207] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:50.207] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:50.611] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:50.611] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246276 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:50.611] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246276 bytes
[2025-07-30 11:58:50.627] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246276 bytes JPEG
[2025-07-30 11:58:50.890] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:50.897] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246276 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:50.897] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246276 bytes
[2025-07-30 11:58:50.913] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246276 bytes JPEG
[2025-07-30 11:58:50.928] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x0 (DOWN)
[2025-07-30 11:58:50.928] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x0
[2025-07-30 11:58:51.114] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:51.114] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:51.224] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:51.240] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 0ms
[2025-07-30 11:58:51.240] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:51.240] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:51.288] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:51.291] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:51.336] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x2 (UP)
[2025-07-30 11:58:51.352] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x2
[2025-07-30 11:58:51.577] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:51.592] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:51.593] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:51.598] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:51.862] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:51.894] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 32ms
[2025-07-30 11:58:51.894] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:51.910] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:52.354] [DEBUG] VirtualDesktopCapture: Mouse input request - x:201, y:250, flags:0x8002
[2025-07-30 11:58:52.354] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:201,250 -> 6860,15170
[2025-07-30 11:58:52.369] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:52.380] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:201, y:250, flags:0x8002
[2025-07-30 11:58:52.401] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246276 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:52.417] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246276 bytes
[2025-07-30 11:58:52.423] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246276 bytes JPEG
[2025-07-30 11:58:52.561] [DEBUG] VirtualDesktopCapture: Mouse input request - x:201, y:250, flags:0x8004
[2025-07-30 11:58:52.561] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:201,250 -> 6860,15170
[2025-07-30 11:58:52.561] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:201, y:250, flags:0x8004
[2025-07-30 11:58:52.790] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x0 (DOWN)
[2025-07-30 11:58:52.795] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x0
[2025-07-30 11:58:52.795] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:52.806] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:52.806] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:52.812] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:52.838] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x0 (DOWN)
[2025-07-30 11:58:52.838] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x0
[2025-07-30 11:58:53.021] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x1B (ESC), flags:0x2 (UP)
[2025-07-30 11:58:53.026] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x1B (ESC), flags:0x2
[2025-07-30 11:58:53.060] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x2 (UP)
[2025-07-30 11:58:53.060] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x2
[2025-07-30 11:58:53.188] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:53.226] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246394 bytes (4.2% ratio) in 32ms
[2025-07-30 11:58:53.230] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246394 bytes
[2025-07-30 11:58:53.235] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246394 bytes JPEG
[2025-07-30 11:58:53.565] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x0 (DOWN)
[2025-07-30 11:58:53.571] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x0
[2025-07-30 11:58:53.614] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:53.625] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 247459 bytes (4.3% ratio) in 0ms
[2025-07-30 11:58:53.627] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 247459 bytes
[2025-07-30 11:58:53.631] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 247459 bytes JPEG
[2025-07-30 11:58:53.807] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x2 (UP)
[2025-07-30 11:58:53.813] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x2
[2025-07-30 11:58:54.122] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x0 (DOWN)
[2025-07-30 11:58:54.122] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x0
[2025-07-30 11:58:54.154] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:54.186] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 149052 bytes (2.6% ratio) in 31ms
[2025-07-30 11:58:54.186] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 149052 bytes
[2025-07-30 11:58:54.197] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 149052 bytes JPEG
[2025-07-30 11:58:54.299] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x10 (SHIFT), flags:0x2 (UP)
[2025-07-30 11:58:54.299] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x10 (SHIFT), flags:0x2
[2025-07-30 11:58:56.069] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:56.103] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 150463 bytes (2.6% ratio) in 32ms
[2025-07-30 11:58:56.103] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 150463 bytes
[2025-07-30 11:58:56.103] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 150463 bytes JPEG
[2025-07-30 11:58:56.179] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x0 (DOWN)
[2025-07-30 11:58:56.275] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x0
[2025-07-30 11:58:56.469] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x2 (UP)
[2025-07-30 11:58:56.469] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x2
[2025-07-30 11:58:56.533] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:56.565] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 242271 bytes (4.2% ratio) in 15ms
[2025-07-30 11:58:56.565] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 242271 bytes
[2025-07-30 11:58:56.581] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 242271 bytes JPEG
[2025-07-30 11:58:57.538] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:57.554] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 246276 bytes (4.2% ratio) in 16ms
[2025-07-30 11:58:57.554] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 246276 bytes
[2025-07-30 11:58:57.563] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 246276 bytes JPEG
[2025-07-30 11:58:57.807] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25 (LEFT), flags:0x0 (DOWN)
[2025-07-30 11:58:57.817] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x25 (LEFT), flags:0x0
[2025-07-30 11:58:57.891] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:57.934] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 261152 bytes (4.5% ratio) in 16ms
[2025-07-30 11:58:57.934] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 261152 bytes
[2025-07-30 11:58:57.949] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 261152 bytes JPEG
[2025-07-30 11:58:57.965] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25 (LEFT), flags:0x2 (UP)
[2025-07-30 11:58:57.965] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x25 (LEFT), flags:0x2
[2025-07-30 11:58:58.421] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:58.450] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212912 bytes (3.7% ratio) in 31ms
[2025-07-30 11:58:58.452] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212912 bytes
[2025-07-30 11:58:58.452] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212912 bytes JPEG
[2025-07-30 11:58:58.854] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:58.870] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212949 bytes (3.7% ratio) in 15ms
[2025-07-30 11:58:58.870] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212949 bytes
[2025-07-30 11:58:58.876] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212949 bytes JPEG
[2025-07-30 11:58:59.267] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:59.310] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212912 bytes (3.7% ratio) in 16ms
[2025-07-30 11:58:59.313] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212912 bytes
[2025-07-30 11:58:59.318] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212912 bytes JPEG
[2025-07-30 11:58:59.753] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:58:59.769] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212949 bytes (3.7% ratio) in 16ms
[2025-07-30 11:58:59.769] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212949 bytes
[2025-07-30 11:58:59.769] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212949 bytes JPEG
[2025-07-30 11:59:00.085] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:00.116] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212949 bytes (3.7% ratio) in 31ms
[2025-07-30 11:59:00.116] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212949 bytes
[2025-07-30 11:59:00.132] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212949 bytes JPEG
[2025-07-30 11:59:00.468] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:00.500] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212912 bytes (3.7% ratio) in 31ms
[2025-07-30 11:59:00.502] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212912 bytes
[2025-07-30 11:59:00.511] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212912 bytes JPEG
[2025-07-30 11:59:00.933] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:00.966] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212949 bytes (3.7% ratio) in 31ms
[2025-07-30 11:59:00.966] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212949 bytes
[2025-07-30 11:59:00.966] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212949 bytes JPEG
[2025-07-30 11:59:01.319] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:01.367] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212912 bytes (3.7% ratio) in 31ms
[2025-07-30 11:59:01.367] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212912 bytes
[2025-07-30 11:59:01.367] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212912 bytes JPEG
[2025-07-30 11:59:01.750] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:01.766] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212912 bytes (3.7% ratio) in 16ms
[2025-07-30 11:59:01.766] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212912 bytes
[2025-07-30 11:59:01.766] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212912 bytes JPEG
[2025-07-30 11:59:02.133] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:02.181] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212949 bytes (3.7% ratio) in 31ms
[2025-07-30 11:59:02.185] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212949 bytes
[2025-07-30 11:59:02.185] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212949 bytes JPEG
[2025-07-30 11:59:02.565] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4A (J), flags:0x0 (DOWN)
[2025-07-30 11:59:02.581] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4A (J), flags:0x0
[2025-07-30 11:59:02.597] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:02.629] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 212912 bytes (3.7% ratio) in 31ms
[2025-07-30 11:59:02.641] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 212912 bytes
[2025-07-30 11:59:02.645] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 212912 bytes JPEG
[2025-07-30 11:59:02.867] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4A (J), flags:0x2 (UP)
[2025-07-30 11:59:02.867] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4A (J), flags:0x2
[2025-07-30 11:59:02.986] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:03.024] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 213176 bytes (3.7% ratio) in 32ms
[2025-07-30 11:59:03.024] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 213176 bytes
[2025-07-30 11:59:03.034] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 213176 bytes JPEG
[2025-07-30 11:59:03.150] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4A (J), flags:0x0 (DOWN)
[2025-07-30 11:59:03.152] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4A (J), flags:0x0
[2025-07-30 11:59:03.206] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4A (J), flags:0x2 (UP)
[2025-07-30 11:59:03.206] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4A (J), flags:0x2
[2025-07-30 11:59:03.478] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:03.519] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 213341 bytes (3.7% ratio) in 32ms
[2025-07-30 11:59:03.519] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 213341 bytes
[2025-07-30 11:59:03.531] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 213341 bytes JPEG
[2025-07-30 11:59:03.962] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:03.994] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 213314 bytes (3.7% ratio) in 15ms
[2025-07-30 11:59:04.010] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 213314 bytes
[2025-07-30 11:59:04.010] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 213314 bytes JPEG
[2025-07-30 11:59:04.122] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:04.122] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:04.436] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:04.452] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:04.456] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:04.456] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:04.459] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:04.459] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:05.314] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:05.335] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 191846 bytes (3.3% ratio) in 16ms
[2025-07-30 11:59:05.338] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 191846 bytes
[2025-07-30 11:59:05.343] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 191846 bytes JPEG
[2025-07-30 11:59:05.722] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x41 (A), flags:0x0 (DOWN)
[2025-07-30 11:59:05.731] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x41 (A), flags:0x0
[2025-07-30 11:59:05.860] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:05.865] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:05.869] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x41 (A), flags:0x2 (UP)
[2025-07-30 11:59:05.873] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x41 (A), flags:0x2
[2025-07-30 11:59:06.075] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:06.077] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:06.099] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:06.123] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 201561 bytes (3.5% ratio) in 15ms
[2025-07-30 11:59:06.125] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 201561 bytes
[2025-07-30 11:59:06.130] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 201561 bytes JPEG
[2025-07-30 11:59:06.552] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:06.572] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 201285 bytes (3.5% ratio) in 15ms
[2025-07-30 11:59:06.572] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 201285 bytes
[2025-07-30 11:59:06.576] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 201285 bytes JPEG
[2025-07-30 11:59:07.134] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:07.151] [DEBUG] VirtualDesktopCapture: Mouse input request - x:462, y:408, flags:0x8002
[2025-07-30 11:59:07.153] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:462,408 -> 15769,24757
[2025-07-30 11:59:07.161] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 201769 bytes (3.5% ratio) in 15ms
[2025-07-30 11:59:07.161] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 201769 bytes
[2025-07-30 11:59:07.161] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:462, y:408, flags:0x8002
[2025-07-30 11:59:07.174] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 201769 bytes JPEG
[2025-07-30 11:59:07.359] [DEBUG] VirtualDesktopCapture: Mouse input request - x:462, y:408, flags:0x8004
[2025-07-30 11:59:07.364] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:462,408 -> 15769,24757
[2025-07-30 11:59:07.365] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:462, y:408, flags:0x8004
[2025-07-30 11:59:07.492] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:07.519] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 210938 bytes (3.6% ratio) in 32ms
[2025-07-30 11:59:07.522] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 210938 bytes
[2025-07-30 11:59:07.528] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 210938 bytes JPEG
[2025-07-30 11:59:08.225] [DEBUG] VirtualDesktopCapture: Mouse input request - x:497, y:368, flags:0x8002
[2025-07-30 11:59:08.227] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:497,368 -> 16964,22330
[2025-07-30 11:59:08.249] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:497, y:368, flags:0x8002
[2025-07-30 11:59:08.428] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:08.442] [DEBUG] VirtualDesktopCapture: Mouse input request - x:497, y:368, flags:0x8004
[2025-07-30 11:59:08.452] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:497,368 -> 16964,22330
[2025-07-30 11:59:08.455] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:497, y:368, flags:0x8004
[2025-07-30 11:59:08.465] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 210938 bytes (3.6% ratio) in 16ms
[2025-07-30 11:59:08.465] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 210938 bytes
[2025-07-30 11:59:08.475] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 210938 bytes JPEG
[2025-07-30 11:59:08.646] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:08.648] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:08.814] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:08.817] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:08.862] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:08.888] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 181020 bytes (3.1% ratio) in 16ms
[2025-07-30 11:59:08.890] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 181020 bytes
[2025-07-30 11:59:08.895] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 181020 bytes JPEG
[2025-07-30 11:59:09.565] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:09.578] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 257657 bytes (4.4% ratio) in 0ms
[2025-07-30 11:59:09.578] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 257657 bytes
[2025-07-30 11:59:09.578] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 257657 bytes JPEG
[2025-07-30 11:59:10.141] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:10.176] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258291 bytes (4.4% ratio) in 31ms
[2025-07-30 11:59:10.179] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258291 bytes
[2025-07-30 11:59:10.185] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258291 bytes JPEG
[2025-07-30 11:59:10.655] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:10.677] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258388 bytes (4.4% ratio) in 16ms
[2025-07-30 11:59:10.680] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258388 bytes
[2025-07-30 11:59:10.685] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258388 bytes JPEG
[2025-07-30 11:59:11.096] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:11.105] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258348 bytes (4.4% ratio) in 0ms
[2025-07-30 11:59:11.105] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258348 bytes
[2025-07-30 11:59:11.117] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258348 bytes JPEG
[2025-07-30 11:59:11.410] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:11.428] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258348 bytes (4.4% ratio) in 16ms
[2025-07-30 11:59:11.428] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258348 bytes
[2025-07-30 11:59:11.434] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258348 bytes JPEG
[2025-07-30 11:59:11.895] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:12.034] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258348 bytes (4.4% ratio) in 31ms
[2025-07-30 11:59:12.188] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258348 bytes
[2025-07-30 11:59:12.192] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258348 bytes JPEG
[2025-07-30 11:59:12.621] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:12.653] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258348 bytes (4.4% ratio) in 16ms
[2025-07-30 11:59:12.653] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258348 bytes
[2025-07-30 11:59:12.669] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258348 bytes JPEG
[2025-07-30 11:59:13.030] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:13.055] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258348 bytes (4.4% ratio) in 16ms
[2025-07-30 11:59:13.055] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258348 bytes
[2025-07-30 11:59:13.071] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258348 bytes JPEG
[2025-07-30 11:59:13.468] [DEBUG] VirtualDesktopCapture: Mouse input request - x:5, y:61, flags:0x8002
[2025-07-30 11:59:13.468] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:5,61 -> 170,3701
[2025-07-30 11:59:13.479] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:5, y:61, flags:0x8002
[2025-07-30 11:59:13.531] [DEBUG] VirtualDesktopCapture: Mouse input request - x:5, y:61, flags:0x8004
[2025-07-30 11:59:13.531] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:5,61 -> 170,3701
[2025-07-30 11:59:13.538] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:5, y:61, flags:0x8004
[2025-07-30 11:59:13.633] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:13.665] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258466 bytes (4.4% ratio) in 31ms
[2025-07-30 11:59:13.665] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258466 bytes
[2025-07-30 11:59:13.665] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258466 bytes JPEG
[2025-07-30 11:59:14.137] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:14.171] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 210951 bytes (3.6% ratio) in 15ms
[2025-07-30 11:59:14.174] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 210951 bytes
[2025-07-30 11:59:14.181] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 210951 bytes JPEG
[2025-07-30 11:59:14.747] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:14.781] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 210897 bytes (3.6% ratio) in 32ms
[2025-07-30 11:59:14.781] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:14.781] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 210897 bytes
[2025-07-30 11:59:14.781] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:14.788] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 210897 bytes JPEG
[2025-07-30 11:59:14.961] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:14.964] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:15.208] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:15.240] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 170319 bytes (2.9% ratio) in 15ms
[2025-07-30 11:59:15.242] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 170319 bytes
[2025-07-30 11:59:15.248] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 170319 bytes JPEG
[2025-07-30 11:59:15.760] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:15.760] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:15.899] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:15.901] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:16.064] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:16.086] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258405 bytes (4.4% ratio) in 16ms
[2025-07-30 11:59:16.088] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258405 bytes
[2025-07-30 11:59:16.095] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258405 bytes JPEG
[2025-07-30 11:59:16.476] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:16.513] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 202140 bytes (3.5% ratio) in 31ms
[2025-07-30 11:59:16.515] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 202140 bytes
[2025-07-30 11:59:16.515] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 202140 bytes JPEG
[2025-07-30 11:59:16.772] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:16.772] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:16.913] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:16.930] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:16.932] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:16.937] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 209568 bytes (3.6% ratio) in 16ms
[2025-07-30 11:59:16.939] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 209568 bytes
[2025-07-30 11:59:16.944] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 209568 bytes JPEG
[2025-07-30 11:59:17.340] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:17.359] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 207400 bytes (3.6% ratio) in 0ms
[2025-07-30 11:59:17.362] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 207400 bytes
[2025-07-30 11:59:17.366] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 207400 bytes JPEG
[2025-07-30 11:59:17.518] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:17.518] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:17.581] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:17.581] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:17.943] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:17.965] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 209044 bytes (3.6% ratio) in 16ms
[2025-07-30 11:59:17.981] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 209044 bytes
[2025-07-30 11:59:17.987] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 209044 bytes JPEG
[2025-07-30 11:59:18.060] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:18.076] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:18.134] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:18.136] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:18.408] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:18.442] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 188192 bytes (3.2% ratio) in 15ms
[2025-07-30 11:59:18.442] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 188192 bytes
[2025-07-30 11:59:18.459] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 188192 bytes JPEG
[2025-07-30 11:59:18.634] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 11:59:18.639] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 11:59:18.643] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 11:59:18.645] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 11:59:18.991] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:19.024] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 193951 bytes (3.3% ratio) in 32ms
[2025-07-30 11:59:19.027] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 193951 bytes
[2025-07-30 11:59:19.029] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 193951 bytes JPEG
[2025-07-30 11:59:19.364] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:19.391] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 187805 bytes (3.2% ratio) in 16ms
[2025-07-30 11:59:19.406] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 187805 bytes
[2025-07-30 11:59:19.410] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 187805 bytes JPEG
[2025-07-30 11:59:19.841] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:19.875] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 188102 bytes (3.2% ratio) in 15ms
[2025-07-30 11:59:19.875] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 188102 bytes
[2025-07-30 11:59:19.890] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 188102 bytes JPEG
[2025-07-30 11:59:20.190] [DEBUG] VirtualDesktopCapture: Mouse input request - x:560, y:290, flags:0x8002
[2025-07-30 11:59:20.190] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:560,290 -> 19114,17597
[2025-07-30 11:59:20.207] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:560, y:290, flags:0x8002
[2025-07-30 11:59:20.342] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:20.376] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 191581 bytes (3.3% ratio) in 31ms
[2025-07-30 11:59:20.376] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 191581 bytes
[2025-07-30 11:59:20.389] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 191581 bytes JPEG
[2025-07-30 11:59:20.396] [DEBUG] VirtualDesktopCapture: Mouse input request - x:560, y:290, flags:0x8004
[2025-07-30 11:59:20.398] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:560,290 -> 19114,17597
[2025-07-30 11:59:20.400] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:560, y:290, flags:0x8004
[2025-07-30 11:59:20.768] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:20.784] [DEBUG] VirtualDesktopCapture: Mouse input request - x:737, y:183, flags:0x8002
[2025-07-30 11:59:20.784] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:737,183 -> 25155,11104
[2025-07-30 11:59:20.800] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:737, y:183, flags:0x8002
[2025-07-30 11:59:20.800] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211094 bytes (3.6% ratio) in 16ms
[2025-07-30 11:59:20.816] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211094 bytes
[2025-07-30 11:59:20.822] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211094 bytes JPEG
[2025-07-30 11:59:21.165] [DEBUG] VirtualDesktopCapture: Mouse input request - x:737, y:183, flags:0x8004
[2025-07-30 11:59:21.165] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:737,183 -> 25155,11104
[2025-07-30 11:59:21.165] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:737, y:183, flags:0x8004
[2025-07-30 11:59:21.465] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:21.501] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211094 bytes (3.6% ratio) in 31ms
[2025-07-30 11:59:21.505] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211094 bytes
[2025-07-30 11:59:21.511] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211094 bytes JPEG
[2025-07-30 11:59:22.584] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1514, y:220, flags:0x8002
[2025-07-30 11:59:22.584] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1514,220 -> 51677,13349
[2025-07-30 11:59:22.604] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1514, y:220, flags:0x8002
[2025-07-30 11:59:22.680] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:22.728] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211094 bytes (3.6% ratio) in 32ms
[2025-07-30 11:59:22.728] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211094 bytes
[2025-07-30 11:59:22.728] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211094 bytes JPEG
[2025-07-30 11:59:22.793] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1514, y:220, flags:0x8004
[2025-07-30 11:59:22.800] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1514,220 -> 51677,13349
[2025-07-30 11:59:22.800] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1514, y:220, flags:0x8004
[2025-07-30 11:59:23.048] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:23.081] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211043 bytes (3.6% ratio) in 31ms
[2025-07-30 11:59:23.081] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211043 bytes
[2025-07-30 11:59:23.081] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211043 bytes JPEG
[2025-07-30 11:59:23.434] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:23.481] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211043 bytes (3.6% ratio) in 32ms
[2025-07-30 11:59:23.481] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211043 bytes
[2025-07-30 11:59:23.481] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211043 bytes JPEG
[2025-07-30 11:59:23.878] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:23.909] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211094 bytes (3.6% ratio) in 15ms
[2025-07-30 11:59:23.925] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211094 bytes
[2025-07-30 11:59:23.925] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211094 bytes JPEG
[2025-07-30 11:59:24.393] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:24.418] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211043 bytes (3.6% ratio) in 0ms
[2025-07-30 11:59:24.420] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211043 bytes
[2025-07-30 11:59:24.424] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211043 bytes JPEG
[2025-07-30 11:59:24.910] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:25.069] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211094 bytes (3.6% ratio) in 31ms
[2025-07-30 11:59:25.101] [DEBUG] VirtualDesktopCapture: Mouse input request - x:947, y:30, flags:0x8002
[2025-07-30 11:59:25.231] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211094 bytes
[2025-07-30 11:59:25.245] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:947,30 -> 32323,1820
[2025-07-30 11:59:25.252] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211094 bytes JPEG
[2025-07-30 11:59:25.254] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:947, y:30, flags:0x8002
[2025-07-30 11:59:25.259] [DEBUG] VirtualDesktopCapture: Mouse input request - x:947, y:30, flags:0x8004
[2025-07-30 11:59:25.259] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:947,30 -> 32323,1820
[2025-07-30 11:59:25.259] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:947, y:30, flags:0x8004
[2025-07-30 11:59:25.702] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:25.714] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 211043 bytes (3.6% ratio) in 0ms
[2025-07-30 11:59:25.717] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 211043 bytes
[2025-07-30 11:59:25.719] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 211043 bytes JPEG
[2025-07-30 11:59:26.153] [DEBUG] VirtualDesktopCapture: Mouse input request - x:894, y:2, flags:0x8008
[2025-07-30 11:59:26.161] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:894,2 -> 30514,121
[2025-07-30 11:59:26.167] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:894, y:2, flags:0x8008
[2025-07-30 11:59:26.169] [DEBUG] VirtualDesktopCapture: Mouse input request - x:894, y:2, flags:0x8010
[2025-07-30 11:59:26.171] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:894,2 -> 30514,121
[2025-07-30 11:59:26.173] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:894, y:2, flags:0x8010
[2025-07-30 11:59:26.240] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:26.272] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224567 bytes (3.9% ratio) in 16ms
[2025-07-30 11:59:26.288] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224567 bytes
[2025-07-30 11:59:26.288] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224567 bytes JPEG
[2025-07-30 11:59:26.707] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:26.739] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224638 bytes (3.9% ratio) in 31ms
[2025-07-30 11:59:26.739] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224638 bytes
[2025-07-30 11:59:26.755] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224638 bytes JPEG
[2025-07-30 11:59:27.080] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:27.112] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224567 bytes (3.9% ratio) in 15ms
[2025-07-30 11:59:27.128] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224567 bytes
[2025-07-30 11:59:27.128] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224567 bytes JPEG
[2025-07-30 11:59:27.575] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:27.608] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224638 bytes (3.9% ratio) in 16ms
[2025-07-30 11:59:27.608] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224638 bytes
[2025-07-30 11:59:27.608] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224638 bytes JPEG
[2025-07-30 11:59:28.012] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:28.043] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224567 bytes (3.9% ratio) in 31ms
[2025-07-30 11:59:28.043] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224567 bytes
[2025-07-30 11:59:28.059] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224567 bytes JPEG
[2025-07-30 11:59:28.443] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:28.475] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224567 bytes (3.9% ratio) in 16ms
[2025-07-30 11:59:28.491] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224567 bytes
[2025-07-30 11:59:28.491] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224567 bytes JPEG
[2025-07-30 11:59:28.959] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:29.003] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224638 bytes (3.9% ratio) in 31ms
[2025-07-30 11:59:29.003] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224638 bytes
[2025-07-30 11:59:29.019] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224638 bytes JPEG
[2025-07-30 11:59:29.471] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:29.519] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224567 bytes (3.9% ratio) in 32ms
[2025-07-30 11:59:29.519] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224567 bytes
[2025-07-30 11:59:29.519] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224567 bytes JPEG
[2025-07-30 11:59:29.960] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:30.004] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224638 bytes (3.9% ratio) in 31ms
[2025-07-30 11:59:30.004] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224638 bytes
[2025-07-30 11:59:30.004] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224638 bytes JPEG
[2025-07-30 11:59:30.414] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:30.423] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224567 bytes (3.9% ratio) in 0ms
[2025-07-30 11:59:30.423] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224567 bytes
[2025-07-30 11:59:30.423] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224567 bytes JPEG
[2025-07-30 11:59:30.774] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:30.806] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224638 bytes (3.9% ratio) in 16ms
[2025-07-30 11:59:30.821] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224638 bytes
[2025-07-30 11:59:30.821] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224638 bytes JPEG
[2025-07-30 11:59:31.112] [INFO] Starting application: Microsoft Edge
[2025-07-30 11:59:31.112] [INFO] Edge launched successfully with PID: 10384
[2025-07-30 11:59:31.271] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:31.302] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224638 bytes (3.9% ratio) in 16ms
[2025-07-30 11:59:31.302] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224638 bytes
[2025-07-30 11:59:31.302] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224638 bytes JPEG
[2025-07-30 11:59:31.962] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:31.989] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 145615 bytes (2.5% ratio) in 15ms
[2025-07-30 11:59:31.992] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 145615 bytes
[2025-07-30 11:59:31.997] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 145615 bytes JPEG
[2025-07-30 11:59:32.913] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:32.939] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 208987 bytes (3.6% ratio) in 15ms
[2025-07-30 11:59:32.944] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 208987 bytes
[2025-07-30 11:59:32.951] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 208987 bytes JPEG
[2025-07-30 11:59:33.434] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:33.453] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 297360 bytes (5.1% ratio) in 15ms
[2025-07-30 11:59:33.453] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 297360 bytes
[2025-07-30 11:59:33.468] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 297360 bytes JPEG
[2025-07-30 11:59:34.208] [DEBUG] VirtualDesktopCapture: Mouse input request - x:753, y:66, flags:0x8002
[2025-07-30 11:59:34.210] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:753,66 -> 25702,4004
[2025-07-30 11:59:34.239] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:753, y:66, flags:0x8002
[2025-07-30 11:59:34.400] [DEBUG] VirtualDesktopCapture: Mouse input request - x:753, y:66, flags:0x8004
[2025-07-30 11:59:34.400] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:753,66 -> 25702,4004
[2025-07-30 11:59:34.406] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:753, y:66, flags:0x8004
[2025-07-30 11:59:34.626] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:34.667] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 305509 bytes (5.3% ratio) in 15ms
[2025-07-30 11:59:34.674] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 305509 bytes
[2025-07-30 11:59:34.685] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 305509 bytes JPEG
[2025-07-30 11:59:35.409] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:35.446] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 306136 bytes (5.3% ratio) in 15ms
[2025-07-30 11:59:35.446] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 306136 bytes
[2025-07-30 11:59:35.453] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 306136 bytes JPEG
[2025-07-30 11:59:35.730] [DEBUG] VirtualDesktopCapture: Mouse input request - x:721, y:64, flags:0x8002
[2025-07-30 11:59:35.732] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:721,64 -> 24609,3883
[2025-07-30 11:59:35.750] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:721, y:64, flags:0x8002
[2025-07-30 11:59:35.927] [DEBUG] VirtualDesktopCapture: Mouse input request - x:721, y:64, flags:0x8004
[2025-07-30 11:59:35.933] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:721,64 -> 24609,3883
[2025-07-30 11:59:35.933] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:721, y:64, flags:0x8004
[2025-07-30 11:59:36.022] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:36.075] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 299459 bytes (5.2% ratio) in 31ms
[2025-07-30 11:59:36.075] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 299459 bytes
[2025-07-30 11:59:36.075] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 299459 bytes JPEG
[2025-07-30 11:59:36.977] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:37.004] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 299188 bytes (5.1% ratio) in 31ms
[2025-07-30 11:59:37.008] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 299188 bytes
[2025-07-30 11:59:37.010] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 299188 bytes JPEG
[2025-07-30 11:59:37.299] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x0 (DOWN)
[2025-07-30 11:59:37.299] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x0
[2025-07-30 11:59:37.444] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x2 (UP)
[2025-07-30 11:59:37.444] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x2
[2025-07-30 11:59:37.641] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:37.677] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 299278 bytes (5.1% ratio) in 16ms
[2025-07-30 11:59:37.677] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 299278 bytes
[2025-07-30 11:59:37.693] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 299278 bytes JPEG
[2025-07-30 11:59:38.255] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:38.284] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 203612 bytes (3.5% ratio) in 15ms
[2025-07-30 11:59:38.290] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 203612 bytes
[2025-07-30 11:59:38.296] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 203612 bytes JPEG
[2025-07-30 11:59:38.862] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x0 (DOWN)
[2025-07-30 11:59:38.869] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x0
[2025-07-30 11:59:38.875] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x9 (TAB), flags:0x2 (UP)
[2025-07-30 11:59:38.881] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x9 (TAB), flags:0x2
[2025-07-30 11:59:39.507] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x0 (DOWN)
[2025-07-30 11:59:39.515] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x0
[2025-07-30 11:59:39.736] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x2 (UP)
[2025-07-30 11:59:39.738] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x2
[2025-07-30 11:59:40.162] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25 (LEFT), flags:0x0 (DOWN)
[2025-07-30 11:59:40.317] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:40.339] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x25 (LEFT), flags:0x0
[2025-07-30 11:59:40.371] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 270243 bytes (4.6% ratio) in 15ms
[2025-07-30 11:59:40.375] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 270243 bytes
[2025-07-30 11:59:40.380] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 270243 bytes JPEG
[2025-07-30 11:59:40.436] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25 (LEFT), flags:0x2 (UP)
[2025-07-30 11:59:40.437] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x25 (LEFT), flags:0x2
[2025-07-30 11:59:40.911] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x0 (DOWN)
[2025-07-30 11:59:40.912] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x0
[2025-07-30 11:59:41.148] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:41.177] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x2 (UP)
[2025-07-30 11:59:41.180] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x2
[2025-07-30 11:59:41.181] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 248955 bytes (4.3% ratio) in 31ms
[2025-07-30 11:59:41.183] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 248955 bytes
[2025-07-30 11:59:41.191] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 248955 bytes JPEG
[2025-07-30 11:59:41.785] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x0 (DOWN)
[2025-07-30 11:59:41.785] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x0
[2025-07-30 11:59:41.919] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:41.951] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 360197 bytes (6.2% ratio) in 15ms
[2025-07-30 11:59:41.967] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 360197 bytes
[2025-07-30 11:59:41.973] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 360197 bytes JPEG
[2025-07-30 11:59:41.999] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x2 (UP)
[2025-07-30 11:59:41.999] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x2
[2025-07-30 11:59:42.613] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x0 (DOWN)
[2025-07-30 11:59:42.621] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x0
[2025-07-30 11:59:42.621] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x2 (UP)
[2025-07-30 11:59:42.621] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x2
[2025-07-30 11:59:42.972] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x0 (DOWN)
[2025-07-30 11:59:42.972] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x0
[2025-07-30 11:59:43.121] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x2 (UP)
[2025-07-30 11:59:43.126] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x2
[2025-07-30 11:59:43.436] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:43.469] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 265769 bytes (4.6% ratio) in 16ms
[2025-07-30 11:59:43.473] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 265769 bytes
[2025-07-30 11:59:43.475] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 265769 bytes JPEG
[2025-07-30 11:59:44.068] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:44.082] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 266280 bytes (4.6% ratio) in 16ms
[2025-07-30 11:59:44.086] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 266280 bytes
[2025-07-30 11:59:44.086] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 266280 bytes JPEG
[2025-07-30 11:59:44.400] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:44.400] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 266280 bytes (4.6% ratio) in 0ms
[2025-07-30 11:59:44.400] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 266280 bytes
[2025-07-30 11:59:44.416] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 266280 bytes JPEG
[2025-07-30 11:59:44.783] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:44.833] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 270359 bytes (4.7% ratio) in 31ms
[2025-07-30 11:59:44.836] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 270359 bytes
[2025-07-30 11:59:44.844] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 270359 bytes JPEG
[2025-07-30 11:59:45.171] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28 (DOWN), flags:0x0 (DOWN)
[2025-07-30 11:59:45.173] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x28 (DOWN), flags:0x0
[2025-07-30 11:59:45.266] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:45.308] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 227930 bytes (3.9% ratio) in 16ms
[2025-07-30 11:59:45.308] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 227930 bytes
[2025-07-30 11:59:45.318] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 227930 bytes JPEG
[2025-07-30 11:59:45.605] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x28 (DOWN), flags:0x2 (UP)
[2025-07-30 11:59:45.609] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x28 (DOWN), flags:0x2
[2025-07-30 11:59:45.839] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26 (UP), flags:0x0 (DOWN)
[2025-07-30 11:59:45.843] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x26 (UP), flags:0x0
[2025-07-30 11:59:45.949] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:45.990] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 209838 bytes (3.6% ratio) in 31ms
[2025-07-30 11:59:45.994] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 209838 bytes
[2025-07-30 11:59:46.004] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 209838 bytes JPEG
[2025-07-30 11:59:46.076] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x26 (UP), flags:0x2 (UP)
[2025-07-30 11:59:46.084] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x26 (UP), flags:0x2
[2025-07-30 11:59:46.865] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:46.883] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 272017 bytes (4.7% ratio) in 0ms
[2025-07-30 11:59:46.885] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 272017 bytes
[2025-07-30 11:59:46.889] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 272017 bytes JPEG
[2025-07-30 11:59:47.469] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 11:59:47.472] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 11:59:47.515] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:47.547] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 272017 bytes (4.7% ratio) in 31ms
[2025-07-30 11:59:47.547] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 272017 bytes
[2025-07-30 11:59:47.560] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 272017 bytes JPEG
[2025-07-30 11:59:47.873] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 11:59:47.877] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 11:59:48.032] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:48.062] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 272017 bytes (4.7% ratio) in 0ms
[2025-07-30 11:59:48.078] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 272017 bytes
[2025-07-30 11:59:48.078] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 272017 bytes JPEG
[2025-07-30 11:59:48.603] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:48.636] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 282743 bytes (4.9% ratio) in 16ms
[2025-07-30 11:59:48.636] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 282743 bytes
[2025-07-30 11:59:48.644] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 282743 bytes JPEG
[2025-07-30 11:59:48.987] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25 (LEFT), flags:0x0 (DOWN)
[2025-07-30 11:59:48.992] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x25 (LEFT), flags:0x0
[2025-07-30 11:59:49.173] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x25 (LEFT), flags:0x2 (UP)
[2025-07-30 11:59:49.175] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x25 (LEFT), flags:0x2
[2025-07-30 11:59:49.277] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:49.322] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 268291 bytes (4.6% ratio) in 31ms
[2025-07-30 11:59:49.322] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 268291 bytes
[2025-07-30 11:59:49.331] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 268291 bytes JPEG
[2025-07-30 11:59:49.730] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x0 (DOWN)
[2025-07-30 11:59:49.732] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x0
[2025-07-30 11:59:49.732] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x27 (RIGHT), flags:0x2 (UP)
[2025-07-30 11:59:49.732] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x27 (RIGHT), flags:0x2
[2025-07-30 11:59:50.331] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:50.363] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 282805 bytes (4.9% ratio) in 31ms
[2025-07-30 11:59:50.363] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 282805 bytes
[2025-07-30 11:59:50.378] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 282805 bytes JPEG
[2025-07-30 11:59:51.309] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:51.346] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 282780 bytes (4.9% ratio) in 32ms
[2025-07-30 11:59:51.346] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 282780 bytes
[2025-07-30 11:59:51.363] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 282780 bytes JPEG
[2025-07-30 11:59:51.735] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:51.868] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 326588 bytes (5.6% ratio) in 15ms
[2025-07-30 11:59:52.037] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 326588 bytes
[2025-07-30 11:59:52.037] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 326588 bytes JPEG
[2025-07-30 11:59:52.469] [DEBUG] VirtualDesktopCapture: Mouse input request - x:384, y:64, flags:0x8002
[2025-07-30 11:59:52.469] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:384,64 -> 13107,3883
[2025-07-30 11:59:52.497] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:384, y:64, flags:0x8002
[2025-07-30 11:59:52.679] [DEBUG] VirtualDesktopCapture: Mouse input request - x:384, y:64, flags:0x8004
[2025-07-30 11:59:52.681] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:384,64 -> 13107,3883
[2025-07-30 11:59:52.684] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:384, y:64, flags:0x8004
[2025-07-30 11:59:52.757] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:52.789] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 15ms
[2025-07-30 11:59:52.800] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:52.805] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:53.100] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:53.156] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 47ms
[2025-07-30 11:59:53.156] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:53.156] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:53.640] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:53.688] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 31ms
[2025-07-30 11:59:53.688] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 11:59:53.697] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 11:59:54.344] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:54.383] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 31ms
[2025-07-30 11:59:54.387] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 11:59:54.387] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 11:59:54.771] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:54.813] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 31ms
[2025-07-30 11:59:54.817] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:54.820] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:54.916] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-741, y:80, flags:0x8002
[2025-07-30 11:59:54.916] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-741,80 -> 0,4854
[2025-07-30 11:59:54.942] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-741, y:80, flags:0x8002
[2025-07-30 11:59:55.122] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-741, y:80, flags:0x8004
[2025-07-30 11:59:55.122] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-741,80 -> 0,4854
[2025-07-30 11:59:55.122] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-741, y:80, flags:0x8004
[2025-07-30 11:59:55.285] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:55.323] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 31ms
[2025-07-30 11:59:55.327] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:55.333] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:55.693] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:55.730] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 16ms
[2025-07-30 11:59:55.730] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 11:59:55.744] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 11:59:56.055] [DEBUG] VirtualDesktopCapture: Mouse input request - x:836, y:266, flags:0x8002
[2025-07-30 11:59:56.066] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:836,266 -> 28535,16141
[2025-07-30 11:59:56.071] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:836, y:266, flags:0x8002
[2025-07-30 11:59:56.175] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:56.206] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 31ms
[2025-07-30 11:59:56.218] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:56.218] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:56.266] [DEBUG] VirtualDesktopCapture: Mouse input request - x:836, y:266, flags:0x8004
[2025-07-30 11:59:56.266] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:836,266 -> 28535,16141
[2025-07-30 11:59:56.266] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:836, y:266, flags:0x8004
[2025-07-30 11:59:56.656] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:56.687] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 16ms
[2025-07-30 11:59:56.703] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 11:59:56.707] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 11:59:57.239] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:154, flags:0x8002
[2025-07-30 11:59:57.239] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:57.239] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,154 -> 2082,9344
[2025-07-30 11:59:57.255] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 16ms
[2025-07-30 11:59:57.258] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:57.260] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:154, flags:0x8002
[2025-07-30 11:59:57.260] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:57.260] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:154, flags:0x8004
[2025-07-30 11:59:57.260] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,154 -> 2082,9344
[2025-07-30 11:59:57.270] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:154, flags:0x8004
[2025-07-30 11:59:57.638] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:57.669] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 15ms
[2025-07-30 11:59:57.669] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 11:59:57.679] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 11:59:57.780] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:154, flags:0x8002
[2025-07-30 11:59:57.780] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,154 -> 2082,9344
[2025-07-30 11:59:57.796] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:154, flags:0x8002
[2025-07-30 11:59:57.987] [DEBUG] VirtualDesktopCapture: Mouse input request - x:61, y:154, flags:0x8004
[2025-07-30 11:59:57.987] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:61,154 -> 2082,9344
[2025-07-30 11:59:57.987] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:61, y:154, flags:0x8004
[2025-07-30 11:59:58.088] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:58.120] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 31ms
[2025-07-30 11:59:58.120] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:58.136] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:58.528] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:58.550] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 16ms
[2025-07-30 11:59:58.553] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:58.558] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:58.936] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:58.967] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 31ms
[2025-07-30 11:59:58.967] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 11:59:58.979] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 11:59:59.079] [DEBUG] VirtualDesktopCapture: Mouse input request - x:824, y:279, flags:0x8002
[2025-07-30 11:59:59.079] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:824,279 -> 28125,16929
[2025-07-30 11:59:59.095] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:824, y:279, flags:0x8002
[2025-07-30 11:59:59.294] [DEBUG] VirtualDesktopCapture: Mouse input request - x:824, y:279, flags:0x8004
[2025-07-30 11:59:59.299] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:824,279 -> 28125,16929
[2025-07-30 11:59:59.302] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:824, y:279, flags:0x8004
[2025-07-30 11:59:59.320] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:59.370] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 31ms
[2025-07-30 11:59:59.370] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 11:59:59.377] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 11:59:59.839] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 11:59:59.869] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 15ms
[2025-07-30 11:59:59.883] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 11:59:59.889] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 12:00:00.369] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:00.410] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 31ms
[2025-07-30 12:00:00.413] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 12:00:00.416] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 12:00:00.494] [DEBUG] VirtualDesktopCapture: Mouse input request - x:158, y:15, flags:0x8002
[2025-07-30 12:00:00.496] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:158,15 -> 5392,910
[2025-07-30 12:00:00.496] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:158, y:15, flags:0x8002
[2025-07-30 12:00:00.797] [DEBUG] VirtualDesktopCapture: Mouse input request - x:158, y:15, flags:0x8004
[2025-07-30 12:00:00.797] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:158,15 -> 5392,910
[2025-07-30 12:00:00.813] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:158, y:15, flags:0x8004
[2025-07-30 12:00:01.334] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:01.375] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 31ms
[2025-07-30 12:00:01.375] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 12:00:01.384] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 12:00:01.784] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:01.826] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 31ms
[2025-07-30 12:00:01.828] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 12:00:01.832] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 12:00:01.914] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-778, y:55, flags:0x8002
[2025-07-30 12:00:01.914] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-778,55 -> 0,3337
[2025-07-30 12:00:01.929] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-778, y:55, flags:0x8002
[2025-07-30 12:00:02.209] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-778, y:55, flags:0x8004
[2025-07-30 12:00:02.213] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-778,55 -> 0,3337
[2025-07-30 12:00:02.215] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-778, y:55, flags:0x8004
[2025-07-30 12:00:02.251] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:02.251] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-778, y:55, flags:0x8004
[2025-07-30 12:00:02.266] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-778,55 -> 0,3337
[2025-07-30 12:00:02.267] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-778, y:55, flags:0x8004
[2025-07-30 12:00:02.283] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325120 bytes (5.6% ratio) in 47ms
[2025-07-30 12:00:02.283] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325120 bytes
[2025-07-30 12:00:02.299] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325120 bytes JPEG
[2025-07-30 12:00:02.780] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:02.815] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325142 bytes (5.6% ratio) in 31ms
[2025-07-30 12:00:02.828] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325142 bytes
[2025-07-30 12:00:02.828] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325142 bytes JPEG
[2025-07-30 12:00:02.860] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-777, y:57, flags:0x8002
[2025-07-30 12:00:02.876] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-777,57 -> 0,3458
[2025-07-30 12:00:02.876] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-777, y:57, flags:0x8002
[2025-07-30 12:00:03.066] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-777, y:57, flags:0x8004
[2025-07-30 12:00:03.066] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-777,57 -> 0,3458
[2025-07-30 12:00:03.081] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-777, y:57, flags:0x8004
[2025-07-30 12:00:03.134] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x44 (D), flags:0x0 (DOWN)
[2025-07-30 12:00:03.134] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x44 (D), flags:0x0
[2025-07-30 12:00:03.289] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x44 (D), flags:0x2 (UP)
[2025-07-30 12:00:03.289] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x44 (D), flags:0x2
[2025-07-30 12:00:03.486] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:03.524] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 355723 bytes (6.1% ratio) in 32ms
[2025-07-30 12:00:03.532] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 355723 bytes
[2025-07-30 12:00:03.542] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 355723 bytes JPEG
[2025-07-30 12:00:04.834] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x44 (D), flags:0x0 (DOWN)
[2025-07-30 12:00:04.853] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x44 (D), flags:0x0
[2025-07-30 12:00:04.962] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x44 (D), flags:0x2 (UP)
[2025-07-30 12:00:04.964] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x44 (D), flags:0x2
[2025-07-30 12:00:05.092] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:05.118] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 301747 bytes (5.2% ratio) in 15ms
[2025-07-30 12:00:05.124] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 301747 bytes
[2025-07-30 12:00:05.124] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 301747 bytes JPEG
[2025-07-30 12:00:06.030] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:06.078] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325220 bytes (5.6% ratio) in 31ms
[2025-07-30 12:00:06.081] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325220 bytes
[2025-07-30 12:00:06.091] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325220 bytes JPEG
[2025-07-30 12:00:06.930] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:06.979] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325220 bytes (5.6% ratio) in 32ms
[2025-07-30 12:00:06.979] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325220 bytes
[2025-07-30 12:00:06.995] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325220 bytes JPEG
[2025-07-30 12:00:07.330] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:07.346] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-739, y:93, flags:0x8002
[2025-07-30 12:00:07.362] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-739,93 -> 0,5643
[2025-07-30 12:00:07.370] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325220 bytes (5.6% ratio) in 15ms
[2025-07-30 12:00:07.372] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325220 bytes
[2025-07-30 12:00:07.377] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325220 bytes JPEG
[2025-07-30 12:00:07.383] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-739, y:93, flags:0x8002
[2025-07-30 12:00:07.557] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-739, y:93, flags:0x8004
[2025-07-30 12:00:07.565] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-739,93 -> 0,5643
[2025-07-30 12:00:07.576] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-739, y:93, flags:0x8004
[2025-07-30 12:00:07.779] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:07.827] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 325332 bytes (5.6% ratio) in 31ms
[2025-07-30 12:00:07.827] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 325332 bytes
[2025-07-30 12:00:07.843] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 325332 bytes JPEG
[2025-07-30 12:00:08.348] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:08.376] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 299392 bytes (5.2% ratio) in 15ms
[2025-07-30 12:00:08.391] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 299392 bytes
[2025-07-30 12:00:08.391] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 299392 bytes JPEG
[2025-07-30 12:00:09.047] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:09.096] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 299424 bytes (5.2% ratio) in 47ms
[2025-07-30 12:00:09.098] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 299424 bytes
[2025-07-30 12:00:09.106] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 299424 bytes JPEG
[2025-07-30 12:00:09.428] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:09.480] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 299392 bytes (5.2% ratio) in 32ms
[2025-07-30 12:00:09.484] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 299392 bytes
[2025-07-30 12:00:09.490] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 299392 bytes JPEG
[2025-07-30 12:00:09.695] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-410, y:71, flags:0x8002
[2025-07-30 12:00:09.906] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-410,71 -> 0,4308
[2025-07-30 12:00:09.914] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-410, y:71, flags:0x8002
[2025-07-30 12:00:09.916] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-410, y:71, flags:0x8004
[2025-07-30 12:00:09.917] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-410,71 -> 0,4308
[2025-07-30 12:00:09.919] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-410, y:71, flags:0x8004
[2025-07-30 12:00:09.968] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:09.986] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 299424 bytes (5.2% ratio) in 31ms
[2025-07-30 12:00:09.986] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 299424 bytes
[2025-07-30 12:00:09.998] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 299424 bytes JPEG
[2025-07-30 12:00:10.441] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:10.473] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 299392 bytes (5.2% ratio) in 32ms
[2025-07-30 12:00:10.473] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 299392 bytes
[2025-07-30 12:00:10.489] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 299392 bytes JPEG
[2025-07-30 12:00:11.068] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:11.087] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 283788 bytes (4.9% ratio) in 16ms
[2025-07-30 12:00:11.096] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 283788 bytes
[2025-07-30 12:00:11.100] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 283788 bytes JPEG
[2025-07-30 12:00:11.752] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:11.786] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 283803 bytes (4.9% ratio) in 31ms
[2025-07-30 12:00:11.786] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 283803 bytes
[2025-07-30 12:00:11.806] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 283803 bytes JPEG
[2025-07-30 12:00:12.132] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x56 (V), flags:0x0 (DOWN)
[2025-07-30 12:00:12.132] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x56 (V), flags:0x0
[2025-07-30 12:00:12.292] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:12.323] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 283869 bytes (4.9% ratio) in 31ms
[2025-07-30 12:00:12.323] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 283869 bytes
[2025-07-30 12:00:12.332] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 283869 bytes JPEG
[2025-07-30 12:00:12.353] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x56 (V), flags:0x2 (UP)
[2025-07-30 12:00:12.359] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x56 (V), flags:0x2
[2025-07-30 12:00:12.933] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:12.955] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 284163 bytes (4.9% ratio) in 16ms
[2025-07-30 12:00:12.971] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 284163 bytes
[2025-07-30 12:00:12.971] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x0 (DOWN)
[2025-07-30 12:00:12.971] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 284163 bytes JPEG
[2025-07-30 12:00:12.971] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x0
[2025-07-30 12:00:13.166] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x2 (UP)
[2025-07-30 12:00:13.169] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x2
[2025-07-30 12:00:13.521] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:13.547] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 258141 bytes (4.4% ratio) in 15ms
[2025-07-30 12:00:13.550] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 258141 bytes
[2025-07-30 12:00:13.555] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 258141 bytes JPEG
[2025-07-30 12:00:14.306] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:14.338] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 280251 bytes (4.8% ratio) in 16ms
[2025-07-30 12:00:14.346] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 280251 bytes
[2025-07-30 12:00:14.346] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 280251 bytes JPEG
[2025-07-30 12:00:15.206] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:15.231] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 280165 bytes (4.8% ratio) in 16ms
[2025-07-30 12:00:15.234] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 280165 bytes
[2025-07-30 12:00:15.237] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 280165 bytes JPEG
[2025-07-30 12:00:15.274] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x0 (DOWN)
[2025-07-30 12:00:15.276] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x0
[2025-07-30 12:00:15.418] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x57 (W), flags:0x2 (UP)
[2025-07-30 12:00:15.420] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x57 (W), flags:0x2
[2025-07-30 12:00:16.335] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:16.388] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 280280 bytes (4.8% ratio) in 47ms
[2025-07-30 12:00:16.390] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 280280 bytes
[2025-07-30 12:00:16.396] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 280280 bytes JPEG
[2025-07-30 12:00:16.826] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:16.852] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 280313 bytes (4.8% ratio) in 16ms
[2025-07-30 12:00:16.852] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 280313 bytes
[2025-07-30 12:00:16.872] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 280313 bytes JPEG
[2025-07-30 12:00:17.395] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:17.421] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 285755 bytes (4.9% ratio) in 15ms
[2025-07-30 12:00:17.421] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 285755 bytes
[2025-07-30 12:00:17.429] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 285755 bytes JPEG
[2025-07-30 12:00:18.628] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:18.643] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 285755 bytes (4.9% ratio) in 16ms
[2025-07-30 12:00:18.658] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 285755 bytes
[2025-07-30 12:00:18.661] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 285755 bytes JPEG
[2025-07-30 12:00:19.087] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:19.111] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 285788 bytes (4.9% ratio) in 15ms
[2025-07-30 12:00:19.113] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 285788 bytes
[2025-07-30 12:00:19.119] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 285788 bytes JPEG
[2025-07-30 12:00:19.572] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:19.586] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 285755 bytes (4.9% ratio) in 16ms
[2025-07-30 12:00:19.586] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 285755 bytes
[2025-07-30 12:00:19.604] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 285755 bytes JPEG
[2025-07-30 12:00:20.087] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:20.121] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 270710 bytes (4.7% ratio) in 15ms
[2025-07-30 12:00:20.121] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 270710 bytes
[2025-07-30 12:00:20.134] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 270710 bytes JPEG
[2025-07-30 12:00:20.172] [DEBUG] VirtualDesktopCapture: Mouse input request - x:337, y:26, flags:0x8002
[2025-07-30 12:00:20.172] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:337,26 -> 11502,1577
[2025-07-30 12:00:20.190] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:337, y:26, flags:0x8002
[2025-07-30 12:00:20.379] [DEBUG] VirtualDesktopCapture: Mouse input request - x:337, y:26, flags:0x8004
[2025-07-30 12:00:20.384] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:337,26 -> 11502,1577
[2025-07-30 12:00:20.387] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:337, y:26, flags:0x8004
[2025-07-30 12:00:20.635] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:20.656] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 261245 bytes (4.5% ratio) in 16ms
[2025-07-30 12:00:20.661] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 261245 bytes
[2025-07-30 12:00:20.664] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 261245 bytes JPEG
[2025-07-30 12:00:21.036] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:21.051] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 261277 bytes (4.5% ratio) in 16ms
[2025-07-30 12:00:21.051] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 261277 bytes
[2025-07-30 12:00:21.068] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 261277 bytes JPEG
[2025-07-30 12:00:21.885] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:21.903] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 261245 bytes (4.5% ratio) in 16ms
[2025-07-30 12:00:21.903] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 261245 bytes
[2025-07-30 12:00:21.916] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 261245 bytes JPEG
[2025-07-30 12:00:22.316] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:22.332] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 261277 bytes (4.5% ratio) in 16ms
[2025-07-30 12:00:22.337] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 261277 bytes
[2025-07-30 12:00:22.339] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 261277 bytes JPEG
[2025-07-30 12:00:22.683] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:22.702] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 261245 bytes (4.5% ratio) in 0ms
[2025-07-30 12:00:22.702] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 261245 bytes
[2025-07-30 12:00:22.702] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 261245 bytes JPEG
[2025-07-30 12:00:23.155] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-23, y:23, flags:0x8002
[2025-07-30 12:00:23.155] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-23,23 -> 0,1395
[2025-07-30 12:00:23.170] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-23, y:23, flags:0x8002
[2025-07-30 12:00:23.202] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:23.220] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 320856 bytes (5.5% ratio) in 0ms
[2025-07-30 12:00:23.237] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 320856 bytes
[2025-07-30 12:00:23.244] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 320856 bytes JPEG
[2025-07-30 12:00:23.455] [DEBUG] VirtualDesktopCapture: Mouse input request - x:-23, y:23, flags:0x8004
[2025-07-30 12:00:23.459] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:-23,23 -> 0,1395
[2025-07-30 12:00:23.459] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:-23, y:23, flags:0x8004
[2025-07-30 12:00:23.825] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:23.872] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224981 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:23.872] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224981 bytes
[2025-07-30 12:00:23.882] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224981 bytes JPEG
[2025-07-30 12:00:25.150] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:25.168] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224981 bytes (3.9% ratio) in 0ms
[2025-07-30 12:00:25.179] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224981 bytes
[2025-07-30 12:00:25.184] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224981 bytes JPEG
[2025-07-30 12:00:25.656] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:25.671] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225078 bytes (3.9% ratio) in 0ms
[2025-07-30 12:00:25.679] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225078 bytes
[2025-07-30 12:00:25.683] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225078 bytes JPEG
[2025-07-30 12:00:26.012] [DEBUG] VirtualDesktopCapture: Mouse input request - x:931, y:13, flags:0x8002
[2025-07-30 12:00:26.012] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:931,13 -> 31777,788
[2025-07-30 12:00:26.028] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:931, y:13, flags:0x8002
[2025-07-30 12:00:26.160] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:26.200] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224981 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:26.204] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224981 bytes
[2025-07-30 12:00:26.210] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224981 bytes JPEG
[2025-07-30 12:00:26.220] [DEBUG] VirtualDesktopCapture: Mouse input request - x:931, y:13, flags:0x8004
[2025-07-30 12:00:26.220] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:931,13 -> 31777,788
[2025-07-30 12:00:26.236] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:931, y:13, flags:0x8004
[2025-07-30 12:00:26.609] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:26.625] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225078 bytes (3.9% ratio) in 0ms
[2025-07-30 12:00:26.625] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225078 bytes
[2025-07-30 12:00:26.634] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225078 bytes JPEG
[2025-07-30 12:00:27.025] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:27.072] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:27.072] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:27.079] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:27.216] [DEBUG] VirtualDesktopCapture: Mouse input request - x:931, y:13, flags:0x8002
[2025-07-30 12:00:27.254] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:931,13 -> 31777,788
[2025-07-30 12:00:27.279] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:931, y:13, flags:0x8002
[2025-07-30 12:00:27.338] [DEBUG] VirtualDesktopCapture: Mouse input request - x:931, y:13, flags:0x8004
[2025-07-30 12:00:27.339] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:931,13 -> 31777,788
[2025-07-30 12:00:27.341] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:931, y:13, flags:0x8004
[2025-07-30 12:00:27.379] [DEBUG] VirtualDesktopCapture: Mouse input request - x:931, y:13, flags:0x8004
[2025-07-30 12:00:27.379] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:931,13 -> 31777,788
[2025-07-30 12:00:27.379] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:931, y:13, flags:0x8004
[2025-07-30 12:00:27.539] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:27.571] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225084 bytes (3.9% ratio) in 15ms
[2025-07-30 12:00:27.587] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225084 bytes
[2025-07-30 12:00:27.595] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225084 bytes JPEG
[2025-07-30 12:00:28.038] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:28.070] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 15ms
[2025-07-30 12:00:28.086] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:28.088] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:28.197] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1106, y:181, flags:0x8002
[2025-07-30 12:00:28.197] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1106,181 -> 37750,10983
[2025-07-30 12:00:28.213] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1106, y:181, flags:0x8002
[2025-07-30 12:00:28.360] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1106, y:181, flags:0x8004
[2025-07-30 12:00:28.364] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1106,181 -> 37750,10983
[2025-07-30 12:00:28.367] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1106, y:181, flags:0x8004
[2025-07-30 12:00:28.570] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:28.604] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 32ms
[2025-07-30 12:00:28.604] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:28.614] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:28.748] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1203, y:466, flags:0x8002
[2025-07-30 12:00:28.748] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1203,466 -> 41061,28277
[2025-07-30 12:00:28.763] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1203, y:466, flags:0x8002
[2025-07-30 12:00:28.909] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1203, y:466, flags:0x8004
[2025-07-30 12:00:28.909] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1203,466 -> 41061,28277
[2025-07-30 12:00:28.909] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1203, y:466, flags:0x8004
[2025-07-30 12:00:28.957] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:28.957] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1203, y:466, flags:0x8004
[2025-07-30 12:00:28.966] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1203,466 -> 41061,28277
[2025-07-30 12:00:28.966] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1203, y:466, flags:0x8004
[2025-07-30 12:00:28.988] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225084 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:28.988] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225084 bytes
[2025-07-30 12:00:28.995] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225084 bytes JPEG
[2025-07-30 12:00:29.395] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:29.421] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 15ms
[2025-07-30 12:00:29.421] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:29.435] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:29.871] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:29.918] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225084 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:29.918] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225084 bytes
[2025-07-30 12:00:29.927] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225084 bytes JPEG
[2025-07-30 12:00:30.077] [DEBUG] VirtualDesktopCapture: Mouse input request - x:940, y:36, flags:0x8002
[2025-07-30 12:00:30.077] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:940,36 -> 32084,2184
[2025-07-30 12:00:30.096] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:940, y:36, flags:0x8002
[2025-07-30 12:00:30.290] [DEBUG] VirtualDesktopCapture: Mouse input request - x:940, y:36, flags:0x8004
[2025-07-30 12:00:30.290] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:940,36 -> 32084,2184
[2025-07-30 12:00:30.290] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:940, y:36, flags:0x8004
[2025-07-30 12:00:30.363] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:30.385] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 16ms
[2025-07-30 12:00:30.403] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:30.409] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:30.854] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:30.885] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225084 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:30.885] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225084 bytes
[2025-07-30 12:00:30.902] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225084 bytes JPEG
[2025-07-30 12:00:31.163] [DEBUG] VirtualDesktopCapture: Mouse input request - x:941, y:39, flags:0x8002
[2025-07-30 12:00:31.167] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:941,39 -> 32118,2366
[2025-07-30 12:00:31.180] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:941, y:39, flags:0x8002
[2025-07-30 12:00:31.337] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:31.385] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:31.385] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:31.401] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:31.418] [DEBUG] VirtualDesktopCapture: Mouse input request - x:941, y:39, flags:0x8004
[2025-07-30 12:00:31.418] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:941,39 -> 32118,2366
[2025-07-30 12:00:31.431] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:941, y:39, flags:0x8004
[2025-07-30 12:00:31.740] [DEBUG] VirtualDesktopCapture: Mouse input request - x:941, y:39, flags:0x8002
[2025-07-30 12:00:31.851] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:31.962] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:941,39 -> 32118,2366
[2025-07-30 12:00:32.057] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:941, y:39, flags:0x8002
[2025-07-30 12:00:32.057] [DEBUG] VirtualDesktopCapture: Mouse input request - x:941, y:39, flags:0x8004
[2025-07-30 12:00:32.065] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:941,39 -> 32118,2366
[2025-07-30 12:00:32.065] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:941, y:39, flags:0x8004
[2025-07-30 12:00:32.078] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225084 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:32.078] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225084 bytes
[2025-07-30 12:00:32.089] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225084 bytes JPEG
[2025-07-30 12:00:32.266] [DEBUG] VirtualDesktopCapture: Mouse input request - x:940, y:38, flags:0x8004
[2025-07-30 12:00:32.266] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:940,38 -> 32084,2305
[2025-07-30 12:00:32.274] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:940, y:38, flags:0x8004
[2025-07-30 12:00:32.475] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:32.503] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:32.503] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:32.518] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:32.875] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:32.919] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:32.919] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:32.929] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:33.302] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:33.334] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225084 bytes (3.9% ratio) in 16ms
[2025-07-30 12:00:33.350] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225084 bytes
[2025-07-30 12:00:33.350] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225084 bytes JPEG
[2025-07-30 12:00:33.831] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:33.863] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 224956 bytes (3.9% ratio) in 15ms
[2025-07-30 12:00:33.879] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 224956 bytes
[2025-07-30 12:00:33.883] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 224956 bytes JPEG
[2025-07-30 12:00:33.926] [DEBUG] VirtualDesktopCapture: Mouse input request - x:933, y:20, flags:0x8002
[2025-07-30 12:00:33.926] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:933,20 -> 31845,1213
[2025-07-30 12:00:33.942] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:933, y:20, flags:0x8002
[2025-07-30 12:00:34.147] [DEBUG] VirtualDesktopCapture: Mouse input request - x:933, y:20, flags:0x8004
[2025-07-30 12:00:34.147] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:933,20 -> 31845,1213
[2025-07-30 12:00:34.147] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:933, y:20, flags:0x8004
[2025-07-30 12:00:34.347] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:34.351] [DEBUG] VirtualDesktopCapture: Mouse input request - x:933, y:20, flags:0x8008
[2025-07-30 12:00:34.356] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:933,20 -> 31845,1213
[2025-07-30 12:00:34.370] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:933, y:20, flags:0x8008
[2025-07-30 12:00:34.379] [DEBUG] VirtualDesktopCapture: Mouse input request - x:933, y:20, flags:0x8010
[2025-07-30 12:00:34.379] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:933,20 -> 31845,1213
[2025-07-30 12:00:34.383] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 225084 bytes (3.9% ratio) in 31ms
[2025-07-30 12:00:34.383] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:933, y:20, flags:0x8010
[2025-07-30 12:00:34.383] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 225084 bytes
[2025-07-30 12:00:34.386] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 225084 bytes JPEG
[2025-07-30 12:00:34.843] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:34.879] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 239576 bytes (4.1% ratio) in 31ms
[2025-07-30 12:00:34.879] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 239576 bytes
[2025-07-30 12:00:34.879] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 239576 bytes JPEG
[2025-07-30 12:00:35.184] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:35.234] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 239630 bytes (4.1% ratio) in 16ms
[2025-07-30 12:00:35.236] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 239630 bytes
[2025-07-30 12:00:35.236] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 239630 bytes JPEG
[2025-07-30 12:00:35.604] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:35.633] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 239630 bytes (4.1% ratio) in 16ms
[2025-07-30 12:00:35.649] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 239630 bytes
[2025-07-30 12:00:35.649] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 239630 bytes JPEG
[2025-07-30 12:00:35.760] [DEBUG] VirtualDesktopCapture: Mouse input request - x:933, y:20, flags:0x8008
[2025-07-30 12:00:35.760] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:933,20 -> 31845,1213
[2025-07-30 12:00:35.791] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:933, y:20, flags:0x8008
[2025-07-30 12:00:35.791] [DEBUG] VirtualDesktopCapture: Mouse input request - x:933, y:20, flags:0x8010
[2025-07-30 12:00:35.791] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:933,20 -> 31845,1213
[2025-07-30 12:00:35.791] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:933, y:20, flags:0x8010
[2025-07-30 12:00:36.259] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:36.307] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251807 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:36.307] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251807 bytes
[2025-07-30 12:00:36.314] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251807 bytes JPEG
[2025-07-30 12:00:36.700] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:36.728] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251807 bytes (4.3% ratio) in 16ms
[2025-07-30 12:00:36.743] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251807 bytes
[2025-07-30 12:00:36.743] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251807 bytes JPEG
[2025-07-30 12:00:37.135] [DEBUG] VirtualDesktopCapture: Mouse input request - x:822, y:201, flags:0x8002
[2025-07-30 12:00:37.135] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:822,201 -> 28057,12196
[2025-07-30 12:00:37.153] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:822, y:201, flags:0x8002
[2025-07-30 12:00:37.311] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:37.326] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251412 bytes (4.3% ratio) in 15ms
[2025-07-30 12:00:37.342] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251412 bytes
[2025-07-30 12:00:37.343] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251412 bytes JPEG
[2025-07-30 12:00:37.343] [DEBUG] VirtualDesktopCapture: Mouse input request - x:822, y:201, flags:0x8004
[2025-07-30 12:00:37.343] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:822,201 -> 28057,12196
[2025-07-30 12:00:37.343] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:822, y:201, flags:0x8004
[2025-07-30 12:00:38.213] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 12:00:38.213] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 12:00:38.213] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 12:00:38.213] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 12:00:38.629] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:38.657] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 16ms
[2025-07-30 12:00:38.657] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:38.664] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:39.094] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:39.126] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251077 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:39.126] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251077 bytes
[2025-07-30 12:00:39.142] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251077 bytes JPEG
[2025-07-30 12:00:39.307] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 12:00:39.311] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 12:00:39.595] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:39.633] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:39.633] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:39.643] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:39.787] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 12:00:39.803] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 12:00:39.999] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:40.032] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 16ms
[2025-07-30 12:00:40.032] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:40.048] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:40.159] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 12:00:40.159] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 12:00:40.550] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 12:00:40.550] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 12:00:40.940] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:40.958] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 16ms
[2025-07-30 12:00:40.958] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:40.972] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:41.458] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 12:00:41.458] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 12:00:41.590] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:41.625] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251077 bytes (4.3% ratio) in 15ms
[2025-07-30 12:00:41.633] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251077 bytes
[2025-07-30 12:00:41.633] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251077 bytes JPEG
[2025-07-30 12:00:41.713] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 12:00:41.728] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 12:00:41.776] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 12:00:41.792] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 12:00:41.948] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 12:00:41.948] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 12:00:42.092] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:42.124] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:42.124] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:42.141] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:42.639] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:42.686] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251077 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:42.694] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251077 bytes
[2025-07-30 12:00:42.698] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251077 bytes JPEG
[2025-07-30 12:00:43.166] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:43.183] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 16ms
[2025-07-30 12:00:43.185] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:43.192] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:43.348] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x0 (DOWN)
[2025-07-30 12:00:43.363] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x0
[2025-07-30 12:00:43.551] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x0 (DOWN)
[2025-07-30 12:00:43.551] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x0
[2025-07-30 12:00:43.655] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:43.686] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 16ms
[2025-07-30 12:00:43.689] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:43.693] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:44.023] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x2 (UP)
[2025-07-30 12:00:44.023] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x2
[2025-07-30 12:00:44.023] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x2 (UP)
[2025-07-30 12:00:44.041] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x2
[2025-07-30 12:00:44.403] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:44.442] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:44.444] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:44.448] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:44.795] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:44.825] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251077 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:44.825] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251077 bytes
[2025-07-30 12:00:44.845] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251077 bytes JPEG
[2025-07-30 12:00:45.225] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:45.245] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x0 (DOWN)
[2025-07-30 12:00:45.245] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x0
[2025-07-30 12:00:45.273] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 32ms
[2025-07-30 12:00:45.280] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:45.285] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:45.484] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x0 (DOWN)
[2025-07-30 12:00:45.675] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x0
[2025-07-30 12:00:45.695] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:45.771] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x2 (UP)
[2025-07-30 12:00:45.775] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x2
[2025-07-30 12:00:45.777] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x58 (X), flags:0x0 (DOWN)
[2025-07-30 12:00:45.782] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x58 (X), flags:0x0
[2025-07-30 12:00:45.802] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251125 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:45.802] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251125 bytes
[2025-07-30 12:00:45.813] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251125 bytes JPEG
[2025-07-30 12:00:45.996] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x58 (X), flags:0x2 (UP)
[2025-07-30 12:00:46.010] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x58 (X), flags:0x2
[2025-07-30 12:00:46.112] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x5A (Z), flags:0x0 (DOWN)
[2025-07-30 12:00:46.112] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x5A (Z), flags:0x0
[2025-07-30 12:00:46.176] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:46.212] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251352 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:46.212] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251352 bytes
[2025-07-30 12:00:46.223] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251352 bytes JPEG
[2025-07-30 12:00:46.350] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x5A (Z), flags:0x2 (UP)
[2025-07-30 12:00:46.350] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x5A (Z), flags:0x2
[2025-07-30 12:00:46.398] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x58 (X), flags:0x0 (DOWN)
[2025-07-30 12:00:46.398] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x58 (X), flags:0x0
[2025-07-30 12:00:46.597] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x58 (X), flags:0x2 (UP)
[2025-07-30 12:00:46.597] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x58 (X), flags:0x2
[2025-07-30 12:00:46.697] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:46.704] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x0 (DOWN)
[2025-07-30 12:00:46.712] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x0
[2025-07-30 12:00:46.735] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251352 bytes (4.3% ratio) in 16ms
[2025-07-30 12:00:46.735] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251352 bytes
[2025-07-30 12:00:46.746] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251352 bytes JPEG
[2025-07-30 12:00:46.950] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x2 (UP)
[2025-07-30 12:00:46.954] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x2
[2025-07-30 12:00:47.078] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x2 (UP)
[2025-07-30 12:00:47.082] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x2
[2025-07-30 12:00:47.171] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:47.203] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251352 bytes (4.3% ratio) in 15ms
[2025-07-30 12:00:47.219] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251352 bytes
[2025-07-30 12:00:47.224] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251352 bytes JPEG
[2025-07-30 12:00:47.478] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x0 (DOWN)
[2025-07-30 12:00:47.478] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x0
[2025-07-30 12:00:47.589] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x0 (DOWN)
[2025-07-30 12:00:47.599] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x0
[2025-07-30 12:00:47.604] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:47.636] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251352 bytes (4.3% ratio) in 31ms
[2025-07-30 12:00:47.636] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251352 bytes
[2025-07-30 12:00:47.636] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251352 bytes JPEG
[2025-07-30 12:00:47.863] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x53 (S), flags:0x2 (UP)
[2025-07-30 12:00:47.863] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x53 (S), flags:0x2
[2025-07-30 12:00:47.911] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x44 (D), flags:0x0 (DOWN)
[2025-07-30 12:00:47.911] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x44 (D), flags:0x0
[2025-07-30 12:00:48.055] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:48.069] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 251496 bytes (4.3% ratio) in 15ms
[2025-07-30 12:00:48.069] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 251496 bytes
[2025-07-30 12:00:48.069] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 251496 bytes JPEG
[2025-07-30 12:00:48.133] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x44 (D), flags:0x2 (UP)
[2025-07-30 12:00:48.149] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x44 (D), flags:0x2
[2025-07-30 12:00:48.199] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x46 (F), flags:0x0 (DOWN)
[2025-07-30 12:00:48.199] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x46 (F), flags:0x0
[2025-07-30 12:00:48.484] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x46 (F), flags:0x2 (UP)
[2025-07-30 12:00:48.486] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x46 (F), flags:0x2
[2025-07-30 12:00:48.489] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x11 (CTRL), flags:0x2 (UP)
[2025-07-30 12:00:48.492] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x11 (CTRL), flags:0x2
[2025-07-30 12:00:48.771] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 12:00:48.771] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 12:00:49.020] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:49.041] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 278778 bytes (4.8% ratio) in 15ms
[2025-07-30 12:00:49.046] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 278778 bytes
[2025-07-30 12:00:49.054] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 278778 bytes JPEG
[2025-07-30 12:00:49.105] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 12:00:49.105] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 12:00:49.484] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:49.516] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 287100 bytes (4.9% ratio) in 0ms
[2025-07-30 12:00:49.516] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 287100 bytes
[2025-07-30 12:00:49.532] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 287100 bytes JPEG
[2025-07-30 12:00:50.056] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:50.104] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 301699 bytes (5.2% ratio) in 32ms
[2025-07-30 12:00:50.104] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 301699 bytes
[2025-07-30 12:00:50.104] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 301699 bytes JPEG
[2025-07-30 12:00:50.504] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:50.541] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 304956 bytes (5.2% ratio) in 31ms
[2025-07-30 12:00:50.541] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 304956 bytes
[2025-07-30 12:00:50.549] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 304956 bytes JPEG
[2025-07-30 12:00:50.814] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x0 (DOWN)
[2025-07-30 12:00:50.820] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x0
[2025-07-30 12:00:50.921] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:50.961] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 305145 bytes (5.3% ratio) in 47ms
[2025-07-30 12:00:50.961] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 305145 bytes
[2025-07-30 12:00:50.965] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 305145 bytes JPEG
[2025-07-30 12:00:50.965] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x43 (C), flags:0x2 (UP)
[2025-07-30 12:00:50.965] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x43 (C), flags:0x2
[2025-07-30 12:00:50.965] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4C (L), flags:0x0 (DOWN)
[2025-07-30 12:00:50.965] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4C (L), flags:0x0
[2025-07-30 12:00:51.140] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x45 (E), flags:0x0 (DOWN)
[2025-07-30 12:00:51.140] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x45 (E), flags:0x0
[2025-07-30 12:00:51.155] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x4C (L), flags:0x2 (UP)
[2025-07-30 12:00:51.155] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x4C (L), flags:0x2
[2025-07-30 12:00:51.204] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x41 (A), flags:0x0 (DOWN)
[2025-07-30 12:00:51.208] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x41 (A), flags:0x0
[2025-07-30 12:00:51.258] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x52 (R), flags:0x0 (DOWN)
[2025-07-30 12:00:51.258] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x52 (R), flags:0x0
[2025-07-30 12:00:51.322] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x45 (E), flags:0x2 (UP)
[2025-07-30 12:00:51.340] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x45 (E), flags:0x2
[2025-07-30 12:00:51.342] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x41 (A), flags:0x2 (UP)
[2025-07-30 12:00:51.342] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x41 (A), flags:0x2
[2025-07-30 12:00:51.401] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x52 (R), flags:0x2 (UP)
[2025-07-30 12:00:51.406] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x52 (R), flags:0x2
[2025-07-30 12:00:51.449] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:51.449] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 12:00:51.449] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 12:00:51.480] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 305913 bytes (5.3% ratio) in 32ms
[2025-07-30 12:00:51.480] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 305913 bytes
[2025-07-30 12:00:51.490] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 305913 bytes JPEG
[2025-07-30 12:00:51.623] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 12:00:51.623] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 12:00:51.868] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:51.882] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329048 bytes (5.7% ratio) in 16ms
[2025-07-30 12:00:51.882] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329048 bytes
[2025-07-30 12:00:51.898] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329048 bytes JPEG
[2025-07-30 12:00:52.407] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:52.413] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 328975 bytes (5.7% ratio) in 0ms
[2025-07-30 12:00:52.413] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 328975 bytes
[2025-07-30 12:00:52.428] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 328975 bytes JPEG
[2025-07-30 12:00:52.710] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:52.753] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 328975 bytes (5.7% ratio) in 47ms
[2025-07-30 12:00:52.755] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 328975 bytes
[2025-07-30 12:00:52.762] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 328975 bytes JPEG
[2025-07-30 12:00:53.223] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:53.247] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329048 bytes (5.7% ratio) in 15ms
[2025-07-30 12:00:53.263] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329048 bytes
[2025-07-30 12:00:53.268] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329048 bytes JPEG
[2025-07-30 12:00:53.636] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:53.668] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 328975 bytes (5.7% ratio) in 31ms
[2025-07-30 12:00:53.668] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 328975 bytes
[2025-07-30 12:00:53.684] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 328975 bytes JPEG
[2025-07-30 12:00:53.828] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x45 (E), flags:0x0 (DOWN)
[2025-07-30 12:00:53.828] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x45 (E), flags:0x0
[2025-07-30 12:00:53.979] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x45 (E), flags:0x2 (UP)
[2025-07-30 12:00:53.979] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x45 (E), flags:0x2
[2025-07-30 12:00:54.046] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:54.086] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329270 bytes (5.7% ratio) in 31ms
[2025-07-30 12:00:54.090] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329270 bytes
[2025-07-30 12:00:54.096] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329270 bytes JPEG
[2025-07-30 12:00:54.704] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 12:00:54.712] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 12:00:54.712] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 12:00:54.720] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 12:00:54.943] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x58 (X), flags:0x0 (DOWN)
[2025-07-30 12:00:54.962] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x58 (X), flags:0x0
[2025-07-30 12:00:55.108] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x58 (X), flags:0x2 (UP)
[2025-07-30 12:00:55.113] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x58 (X), flags:0x2
[2025-07-30 12:00:55.347] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 12:00:55.347] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 12:00:55.413] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:55.427] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329261 bytes (5.7% ratio) in 0ms
[2025-07-30 12:00:55.427] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329261 bytes
[2025-07-30 12:00:55.445] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329261 bytes JPEG
[2025-07-30 12:00:55.513] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 12:00:55.517] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 12:00:55.830] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:55.861] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329261 bytes (5.7% ratio) in 15ms
[2025-07-30 12:00:55.861] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329261 bytes
[2025-07-30 12:00:55.878] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329261 bytes JPEG
[2025-07-30 12:00:55.893] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x49 (I), flags:0x0 (DOWN)
[2025-07-30 12:00:55.893] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x49 (I), flags:0x0
[2025-07-30 12:00:56.036] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x49 (I), flags:0x2 (UP)
[2025-07-30 12:00:56.044] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x49 (I), flags:0x2
[2025-07-30 12:00:56.215] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:56.248] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329442 bytes (5.7% ratio) in 15ms
[2025-07-30 12:00:56.248] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329442 bytes
[2025-07-30 12:00:56.264] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329442 bytes JPEG
[2025-07-30 12:00:56.359] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 12:00:56.359] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 12:00:56.407] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 12:00:56.415] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 12:00:56.600] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:56.646] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329376 bytes (5.7% ratio) in 32ms
[2025-07-30 12:00:56.648] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329376 bytes
[2025-07-30 12:00:56.651] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329376 bytes JPEG
[2025-07-30 12:00:56.744] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x54 (T), flags:0x0 (DOWN)
[2025-07-30 12:00:56.744] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x54 (T), flags:0x0
[2025-07-30 12:00:56.871] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x54 (T), flags:0x2 (UP)
[2025-07-30 12:00:56.884] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x54 (T), flags:0x2
[2025-07-30 12:00:57.026] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:57.065] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329543 bytes (5.7% ratio) in 31ms
[2025-07-30 12:00:57.065] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329543 bytes
[2025-07-30 12:00:57.070] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x0 (DOWN)
[2025-07-30 12:00:57.070] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329543 bytes JPEG
[2025-07-30 12:00:57.070] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x0
[2025-07-30 12:00:57.241] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0x8 (BACKSPACE), flags:0x2 (UP)
[2025-07-30 12:00:57.241] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0x8 (BACKSPACE), flags:0x2
[2025-07-30 12:00:57.480] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:57.519] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 329461 bytes (5.7% ratio) in 32ms
[2025-07-30 12:00:57.519] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 329461 bytes
[2025-07-30 12:00:57.530] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 329461 bytes JPEG
[2025-07-30 12:00:57.576] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x0 (DOWN)
[2025-07-30 12:00:57.576] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x0
[2025-07-30 12:00:57.766] [DEBUG] VirtualDesktopCapture: Keyboard input request - vk:0xD (ENTER), flags:0x2 (UP)
[2025-07-30 12:00:57.766] [DEBUG] VirtualDesktopCapture: Keyboard input sent successfully - vk:0xD (ENTER), flags:0x2
[2025-07-30 12:00:57.949] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:57.965] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382617 bytes (6.6% ratio) in 16ms
[2025-07-30 12:00:57.965] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382617 bytes
[2025-07-30 12:00:57.976] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382617 bytes JPEG
[2025-07-30 12:00:58.736] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:58.751] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382617 bytes (6.6% ratio) in 0ms
[2025-07-30 12:00:58.752] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382617 bytes
[2025-07-30 12:00:58.756] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382617 bytes JPEG
[2025-07-30 12:00:59.152] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:59.178] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382617 bytes (6.6% ratio) in 31ms
[2025-07-30 12:00:59.193] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382617 bytes
[2025-07-30 12:00:59.200] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382617 bytes JPEG
[2025-07-30 12:00:59.629] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:00:59.677] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382617 bytes (6.6% ratio) in 31ms
[2025-07-30 12:00:59.677] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382617 bytes
[2025-07-30 12:00:59.690] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382617 bytes JPEG
[2025-07-30 12:00:59.756] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1172, y:516, flags:0x8002
[2025-07-30 12:00:59.756] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1172,516 -> 40003,31311
[2025-07-30 12:00:59.772] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1172, y:516, flags:0x8002
[2025-07-30 12:00:59.968] [DEBUG] VirtualDesktopCapture: Mouse input request - x:1172, y:516, flags:0x8004
[2025-07-30 12:00:59.968] [DEBUG] VirtualDesktopCapture: Virtual desktop conversion - virtual:1920x1080 at (0,0), input:1172,516 -> 40003,31311
[2025-07-30 12:00:59.968] [INFO] VirtualDesktopCapture: Mouse input sent successfully to virtual desktop - x:1172, y:516, flags:0x8004
[2025-07-30 12:01:00.098] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:01:00.128] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382617 bytes (6.6% ratio) in 16ms
[2025-07-30 12:01:00.143] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382617 bytes
[2025-07-30 12:01:00.143] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382617 bytes JPEG
[2025-07-30 12:01:00.448] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:01:00.464] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382617 bytes (6.6% ratio) in 16ms
[2025-07-30 12:01:00.464] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382617 bytes
[2025-07-30 12:01:00.470] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382617 bytes JPEG
[2025-07-30 12:01:00.828] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:01:00.864] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382617 bytes (6.6% ratio) in 31ms
[2025-07-30 12:01:00.866] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382617 bytes
[2025-07-30 12:01:00.874] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382617 bytes JPEG
[2025-07-30 12:01:01.285] [DEBUG] BitmapToJpgOptimized: Using JPEG quality 90 (compile-time: 90)
[2025-07-30 12:01:01.298] [DEBUG] TurboJPEG compression: 1920x1009, 5811840 bytes -> 382617 bytes (6.6% ratio) in 16ms
[2025-07-30 12:01:01.300] [DEBUG] TurboJPEG compression successful: 1920x1009 -> 382617 bytes
[2025-07-30 12:01:01.304] [DEBUG] BitmapToJpgOptimized: Compressed 1920x1009 to 382617 bytes JPEG
