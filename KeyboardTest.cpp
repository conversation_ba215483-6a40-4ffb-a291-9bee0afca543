/**
 * Keyboard Input Test Program
 * 
 * This program tests the keyboard input functionality of the HVNC system.
 * It creates a simple window that displays received keyboard input to verify
 * that all keys are being properly captured and processed.
 */

#include <Windows.h>
#include <iostream>
#include <string>
#include <map>

// Global variables
HWND g_hWnd = NULL;
std::string g_displayText = "Keyboard Test - Press any key...\n";
std::map<WPARAM, std::string> g_keyNames;

// Initialize key name mapping
void InitializeKeyNames() {
    g_keyNames[VK_SPACE] = "SPACE";
    g_keyNames[VK_RETURN] = "ENTER";
    g_keyNames[VK_ESCAPE] = "ESC";
    g_keyNames[VK_TAB] = "TAB";
    g_keyNames[VK_BACK] = "BACKSPACE";
    g_keyNames[VK_DELETE] = "DELETE";
    g_keyNames[VK_SHIFT] = "SHIFT";
    g_keyNames[VK_CONTROL] = "CTRL";
    g_keyNames[VK_MENU] = "ALT";
    g_keyNames[VK_LWIN] = "WIN";
    g_keyNames[VK_UP] = "UP";
    g_keyNames[VK_DOWN] = "DOWN";
    g_keyNames[VK_LEFT] = "LEFT";
    g_keyNames[VK_RIGHT] = "RIGHT";
    g_keyNames[VK_HOME] = "HOME";
    g_keyNames[VK_END] = "END";
    g_keyNames[VK_PRIOR] = "PAGE_UP";
    g_keyNames[VK_NEXT] = "PAGE_DOWN";
    g_keyNames[VK_INSERT] = "INSERT";
    g_keyNames[VK_F1] = "F1";
    g_keyNames[VK_F2] = "F2";
    g_keyNames[VK_F3] = "F3";
    g_keyNames[VK_F4] = "F4";
    g_keyNames[VK_F5] = "F5";
    g_keyNames[VK_F6] = "F6";
    g_keyNames[VK_F7] = "F7";
    g_keyNames[VK_F8] = "F8";
    g_keyNames[VK_F9] = "F9";
    g_keyNames[VK_F10] = "F10";
    g_keyNames[VK_F11] = "F11";
    g_keyNames[VK_F12] = "F12";
}

// Get key name for display
std::string GetKeyName(WPARAM wParam) {
    auto it = g_keyNames.find(wParam);
    if (it != g_keyNames.end()) {
        return it->second;
    }
    
    // Handle alphanumeric keys
    if (wParam >= 'A' && wParam <= 'Z') {
        return std::string(1, (char)wParam);
    }
    if (wParam >= '0' && wParam <= '9') {
        return std::string(1, (char)wParam);
    }
    
    // Return hex code for unknown keys
    char buffer[16];
    sprintf_s(buffer, "0x%02X", (unsigned int)wParam);
    return std::string(buffer);
}

// Window procedure
LRESULT CALLBACK WindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hWnd, &ps);
            
            RECT rect;
            GetClientRect(hWnd, &rect);
            
            // Clear background
            FillRect(hdc, &rect, (HBRUSH)(COLOR_WINDOW + 1));
            
            // Draw text
            DrawTextA(hdc, g_displayText.c_str(), -1, &rect, DT_LEFT | DT_TOP | DT_WORDBREAK);
            
            EndPaint(hWnd, &ps);
            return 0;
        }
        
        case WM_KEYDOWN: {
            std::string keyName = GetKeyName(wParam);
            std::string message = "KEY DOWN: " + keyName + " (VK=" + std::to_string(wParam) + ")\n";
            g_displayText += message;
            
            // Keep only last 20 lines
            size_t lineCount = 0;
            size_t pos = g_displayText.length();
            while (pos > 0 && lineCount < 20) {
                pos = g_displayText.rfind('\n', pos - 1);
                if (pos == std::string::npos) break;
                lineCount++;
            }
            if (lineCount >= 20 && pos != std::string::npos) {
                g_displayText = g_displayText.substr(pos + 1);
            }
            
            InvalidateRect(hWnd, NULL, TRUE);
            printf("KEYBOARD TEST: %s", message.c_str());
            return 0;
        }
        
        case WM_KEYUP: {
            std::string keyName = GetKeyName(wParam);
            std::string message = "KEY UP: " + keyName + " (VK=" + std::to_string(wParam) + ")\n";
            g_displayText += message;
            
            InvalidateRect(hWnd, NULL, TRUE);
            printf("KEYBOARD TEST: %s", message.c_str());
            return 0;
        }
        
        case WM_CHAR: {
            char ch = (char)wParam;
            std::string message = "CHAR: '" + std::string(1, ch) + "' (Code=" + std::to_string(wParam) + ")\n";
            g_displayText += message;
            
            InvalidateRect(hWnd, NULL, TRUE);
            printf("KEYBOARD TEST: %s", message.c_str());
            return 0;
        }
        
        case WM_SYSKEYDOWN: {
            std::string keyName = GetKeyName(wParam);
            std::string message = "SYSKEY DOWN: " + keyName + " (VK=" + std::to_string(wParam) + ")\n";
            g_displayText += message;
            
            InvalidateRect(hWnd, NULL, TRUE);
            printf("KEYBOARD TEST: %s", message.c_str());
            return 0;
        }
        
        case WM_SYSKEYUP: {
            std::string keyName = GetKeyName(wParam);
            std::string message = "SYSKEY UP: " + keyName + " (VK=" + std::to_string(wParam) + ")\n";
            g_displayText += message;
            
            InvalidateRect(hWnd, NULL, TRUE);
            printf("KEYBOARD TEST: %s", message.c_str());
            return 0;
        }
        
        case WM_CLOSE:
            DestroyWindow(hWnd);
            return 0;
            
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
    }
    
    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    InitializeKeyNames();
    
    // Register window class
    WNDCLASSA wc = {};
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.lpszClassName = "KeyboardTestWindow";
    wc.hCursor = LoadCursor(NULL, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    
    if (!RegisterClassA(&wc)) {
        MessageBoxA(NULL, "Failed to register window class", "Error", MB_OK);
        return 1;
    }
    
    // Create window
    g_hWnd = CreateWindowExA(
        0,
        "KeyboardTestWindow",
        "HVNC Keyboard Input Test",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        800, 600,
        NULL, NULL, hInstance, NULL
    );
    
    if (!g_hWnd) {
        MessageBoxA(NULL, "Failed to create window", "Error", MB_OK);
        return 1;
    }
    
    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);
    
    printf("Keyboard Test Window Created - Press keys to test input handling\n");
    printf("This window will display all keyboard events received\n");
    printf("Close the window or press Ctrl+C to exit\n\n");
    
    // Message loop
    MSG msg;
    while (GetMessage(&msg, NULL, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }
    
    return 0;
}
