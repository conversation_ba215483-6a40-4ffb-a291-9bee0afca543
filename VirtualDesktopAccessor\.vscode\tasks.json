{"version": "2.0.0", "tasks": [{"type": "cargo", "command": "build", "args": ["--workspace"], "problemMatcher": ["$rustc"], "group": "build", "label": "build --workspace"}, {"type": "cargo", "command": "run", "args": ["--package", "testbin"], "problemMatcher": ["$rustc"], "group": "build", "label": "testbin", "env": {"RUST_BACKTRACE": "1"}}, {"type": "cargo", "command": "run", "args": ["--package", "testbin", "--release"], "problemMatcher": ["$rustc"], "group": "build", "label": "testbin-release"}]}