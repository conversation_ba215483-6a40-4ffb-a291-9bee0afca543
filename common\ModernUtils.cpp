#include "ModernUtils.h"
#include <stdarg.h>
#include <vector>

namespace ModernHVNC {

/**
 * Convert UTF-16 Wide String to UTF-8 String
 * Performs safe conversion from Windows wide character strings to UTF-8 encoded strings
 * Used for cross-platform compatibility and network transmission
 *
 * @param utf16 - Input UTF-16 wide character string
 * @return UTF-8 encoded string, empty string on error
 */
std::string Utf16toUtf8String(const wchar_t* utf16) {
    if (!utf16) return {};  // Handle null input gracefully

    // Calculate required buffer size for UTF-8 conversion
    const int size = WideCharToMultiByte(CP_UTF8, 0, utf16, -1, nullptr, 0, nullptr, nullptr);
    if (size <= 0) return {};  // Conversion failed

    // Allocate buffer and perform conversion
    std::vector<char> buffer(size);
    WideCharToMultiByte(CP_UTF8, 0, utf16, -1, buffer.data(), size, nullptr, nullptr);

    return std::string(buffer.data());
}

/**
 * Convert UTF-8 String to UTF-16 Wide String
 * Performs safe conversion from UTF-8 encoded strings to Windows wide character strings
 * Essential for Windows API calls that require wide character strings
 *
 * @param utf8 - Input UTF-8 encoded string
 * @return UTF-16 wide character string, empty string on error
 */
std::wstring Utf8toUtf16String(const char* utf8) {
    if (!utf8) return {};  // Handle null input gracefully

    // Calculate required buffer size for UTF-16 conversion
    const int size = MultiByteToWideChar(CP_UTF8, 0, utf8, -1, nullptr, 0);
    if (size <= 0) return {};  // Conversion failed

    // Allocate buffer and perform conversion
    std::vector<wchar_t> buffer(size);
    MultiByteToWideChar(CP_UTF8, 0, utf8, -1, buffer.data(), size);

    return std::wstring(buffer.data());
}

/**
 * Format String with Variable Arguments (ANSI)
 * Type-safe string formatting using printf-style format specifiers
 * Automatically calculates buffer size to prevent overflow
 *
 * @param format - Printf-style format string
 * @param ... - Variable arguments for formatting
 * @return Formatted string
 */
std::string FormatString(const char* format, ...) {
    va_list args;
    va_start(args, format);

    // Calculate required buffer size for formatted string
    const int size = _vscprintf(format, args) + 1;
    std::vector<char> buffer(size);

    // Perform safe formatting with truncation protection
    _vsnprintf_s(buffer.data(), size, _TRUNCATE, format, args);
    va_end(args);

    return std::string(buffer.data());
}

/**
 * Format Wide String with Variable Arguments (Unicode)
 * Type-safe wide string formatting using wprintf-style format specifiers
 * Automatically calculates buffer size to prevent overflow
 *
 * @param format - Wprintf-style format string
 * @param ... - Variable arguments for formatting
 * @return Formatted wide string
 */
std::wstring FormatWString(const wchar_t* format, ...) {
    va_list args;
    va_start(args, format);

    // Calculate required buffer size for formatted wide string
    const int size = _vscwprintf(format, args) + 1;
    std::vector<wchar_t> buffer(size);

    // Perform safe formatting with truncation protection
    _vsnwprintf_s(buffer.data(), size, _TRUNCATE, format, args);
    va_end(args);

    return std::wstring(buffer.data());
}

/**
 * Get File Size Using Modern Structured Binding
 * Returns file size as a pair of 32-bit values for compatibility with legacy APIs
 * Uses LARGE_INTEGER internally for 64-bit file size support
 *
 * @param hFile - Handle to the file
 * @return Pair of {LowPart, HighPart} representing 64-bit file size, {0, 0} on error
 */
std::pair<DWORD, DWORD> GetFileSize(HANDLE hFile) noexcept {
    try {
        LARGE_INTEGER size;
        if (GetFileSizeEx(hFile, &size)) {
            // Return 64-bit size as two 32-bit parts for structured binding
            return {size.LowPart, size.HighPart};
        }
    } catch (...) {
        // Silent error handling to prevent exceptions in utility functions
    }
    return {0, 0};  // Error case: return zero size
}

/**
 * Get Window Dimensions Using Modern Structured Binding
 * Returns window dimensions as a tuple with success flag and dimensions
 * Provides type-safe access to window size information
 *
 * @param hWnd - Handle to the window
 * @return Tuple of {success, width, height}, {false, 0, 0} on error
 */
std::tuple<bool, DWORD, DWORD> GetWindowDimensions(HWND hWnd) noexcept {
    try {
        RECT rect;
        if (GetWindowRect(hWnd, &rect)) {
            // Calculate window dimensions from rectangle coordinates
            const DWORD width = static_cast<DWORD>(rect.right - rect.left);
            const DWORD height = static_cast<DWORD>(rect.bottom - rect.top);
            return {true, width, height};  // Success case
        }
    } catch (...) {
        // Silent error handling to prevent exceptions in utility functions
    }
    return {false, 0, 0};  // Error case: return failure flag with zero dimensions
}

} // namespace ModernHVNC
