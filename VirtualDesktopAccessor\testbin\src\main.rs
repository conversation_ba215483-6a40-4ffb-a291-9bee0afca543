use winit::{
    event::Event,
    event_loop::{ControlFlow, EventLoopBuilder},
};
use winvd::*;

#[derive(<PERSON><PERSON>, Debug)]
enum MyCustomEvents {
    #[allow(dead_code)]
    MyEvent1,

    DesktopEvent(DesktopEvent),
}

impl Default for MyCustomEvents {
    fn default() -> Self {
        MyCustomEvents::MyEvent1
    }
}

// From DesktopEvent
impl From<DesktopEvent> for MyCustomEvents {
    fn from(e: DesktopEvent) -> Self {
        MyCustomEvents::DesktopEvent(e)
    }
}

fn main() {
    let event_loop = EventLoopBuilder::<MyCustomEvents>::default()
        .build()
        .unwrap();
    // let attrs = WindowAttributes::default().with_title("Hello, world!");
    let proxy = event_loop.create_proxy();

    let mut _thread = listen_desktop_events(DesktopEventSender::Winit(proxy)).unwrap();

    event_loop.set_control_flow(ControlFlow::Wait);

    event_loop
        .run(move |event, _evtloop| {
            match event {
                // Main window events
                // Event::WindowEvent {
                //     event: WindowEvent::CloseRequested,
                //     window_id,
                // } if window_id == your_app_window.id() => {
                //     let _ = _thread.stop();

                //     elewt.exit();
                // }

                // User events
                Event::UserEvent(e) => match e {
                    MyCustomEvents::MyEvent1 => {
                        println!("MyEvent1");
                    }
                    MyCustomEvents::DesktopEvent(e) => {
                        println!("DesktopEvent: {:?}", e);
                    }
                },
                _ => (),
            }
        })
        .unwrap();
}
