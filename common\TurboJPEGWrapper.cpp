/**
 * TurboJPEG Wrapper Implementation
 *
 * This file implements a high-performance JPEG compression and decompression wrapper
 * around the TurboJPEG library. It provides significant performance improvements over
 * standard Windows JPEG APIs, especially for real-time screen capture applications.
 *
 * Key Features:
 * - Hardware-accelerated JPEG compression/decompression
 * - Thread-safe operations with critical section protection
 * - Performance statistics and monitoring
 * - Memory management and error handling
 * - Support for various pixel formats and quality settings
 * - Optimized for screen capture use cases
 */

#include "TurboJPEGWrapper.h"
#include "SimpleLogger.h"
#include <malloc.h>

// ============================================================================
// STATIC MEMBER VARIABLES
// ============================================================================

/**
 * Global State Management
 * These static members track the global state of TurboJPEG usage across all instances
 */
BOOL TurboJPEGWrapper::s_globalInitialized = FALSE;    // Global initialization flag
LONG TurboJPEGWrapper::s_instanceCount = 0;            // Number of active instances

/**
 * Global Instance for C-Style Functions
 * Provides compatibility with C-style callback functions that need access to TurboJPEG
 */
static TurboJPEGWrapper* g_globalWrapper = nullptr;

TurboJPEGWrapper::TurboJPEGWrapper()
    : m_compressor(nullptr)
    , m_decompressor(nullptr)
    , m_initialized(FALSE)
{
    InitializeCriticalSection(&m_criticalSection);
    memset(&m_stats, 0, sizeof(m_stats));
    InterlockedIncrement(&s_instanceCount);
}

TurboJPEGWrapper::~TurboJPEGWrapper()
{
    Cleanup();
    DeleteCriticalSection(&m_criticalSection);
    InterlockedDecrement(&s_instanceCount);
}

BOOL TurboJPEGWrapper::Initialize()
{
    EnterCriticalSection(&m_criticalSection);
    
    if (m_initialized) {
        LeaveCriticalSection(&m_criticalSection);
        return TRUE;
    }

    BOOL success = InitializeCompressor();
    if (success) {
        m_initialized = TRUE;
        s_globalInitialized = TRUE;
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, 
            "TurboJPEG wrapper initialized successfully");
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, 
            "Failed to initialize TurboJPEG wrapper");
    }

    LeaveCriticalSection(&m_criticalSection);
    return success;
}

void TurboJPEGWrapper::Cleanup()
{
    EnterCriticalSection(&m_criticalSection);
    
    if (m_initialized) {
        CleanupCompressor();
        m_initialized = FALSE;
        
        if (s_instanceCount == 1) {
            s_globalInitialized = FALSE;
        }
        
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, 
            "TurboJPEG wrapper cleaned up");
    }
    
    LeaveCriticalSection(&m_criticalSection);
}

/**
 * Initialize TurboJPEG Compressor and Decompressor
 * Creates the hardware-accelerated JPEG compression and decompression handles
 * This function sets up the core TurboJPEG engines for high-performance image processing
 *
 * @return TRUE if compressor initialization successful, FALSE otherwise
 */
BOOL TurboJPEGWrapper::InitializeCompressor()
{
    // Create TurboJPEG compressor handle for encoding screen captures to JPEG
    m_compressor = tjInitCompress();
    if (!m_compressor) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "Failed to create TurboJPEG compressor: %s", tjGetErrorStr());
        return FALSE;
    }

    // Create decompressor handle for potential bidirectional operations
    // This is optional but useful for server-side decompression
    m_decompressor = tjInitDecompress();
    if (!m_decompressor) {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning,
            "Failed to create TurboJPEG decompressor: %s", tjGetErrorStr());
        // Continue without decompressor - compression is the primary requirement
    }

    return TRUE;
}

/**
 * Cleanup TurboJPEG Resources
 * Properly releases compressor and decompressor handles to prevent memory leaks
 * Called during wrapper destruction or reinitialization
 */
void TurboJPEGWrapper::CleanupCompressor()
{
    // Release compressor handle and associated resources
    if (m_compressor) {
        tjDestroy(m_compressor);
        m_compressor = nullptr;
    }

    // Release decompressor handle and associated resources
    if (m_decompressor) {
        tjDestroy(m_decompressor);
        m_decompressor = nullptr;
    }
}

/**
 * Compress Bitmap Data to JPEG Format
 * High-performance hardware-accelerated JPEG compression of raw bitmap data
 * This is the core compression function used for screen capture optimization
 *
 * @param bitmapData - Raw bitmap pixel data buffer
 * @param width - Image width in pixels
 * @param height - Image height in pixels
 * @param pixelFormat - TurboJPEG pixel format (TJPF_RGB, TJPF_BGR, etc.)
 * @param quality - JPEG quality level (1-100, higher = better quality)
 * @param jpegBuffer - Output buffer for compressed JPEG data (allocated by TurboJPEG)
 * @param jpegSize - Output size of compressed JPEG data in bytes
 * @return TRUE if compression successful, FALSE otherwise
 */
BOOL TurboJPEGWrapper::CompressBitmap(
    BYTE* bitmapData,
    int width,
    int height,
    int pixelFormat,
    int quality,
    BYTE** jpegBuffer,
    unsigned long* jpegSize)
{
    // Validate input parameters and initialization state
    if (!m_initialized || !bitmapData || !jpegBuffer || !jpegSize) {
        return FALSE;
    }

    // Thread-safe compression using critical section
    EnterCriticalSection(&m_criticalSection);

    DWORD startTime = GetTickCount();  // Start performance timing
    BOOL success = FALSE;

    // Calculate input data size for compression ratio analysis
    int pixelSize = tjPixelSize[pixelFormat];
    DWORD inputSize = width * height * pixelSize;

    // Perform hardware-accelerated JPEG compression
    int result = tjCompress2(
        m_compressor,           // TurboJPEG compressor handle
        bitmapData,             // Source raw bitmap data
        width,                  // Image width in pixels
        0,                      // Pitch (0 = automatic calculation: width * pixel size)
        height,                 // Image height in pixels
        pixelFormat,            // Input pixel format (RGB, BGR, etc.)
        jpegBuffer,             // Output JPEG buffer (allocated by TurboJPEG)
        jpegSize,               // Output size of compressed JPEG data
        TJSAMP_420,             // Chrominance subsampling (4:2:0 for optimal compression)
        quality,                // JPEG quality level (1-100)
        TJFLAG_FASTDCT          // Use fast DCT algorithm for speed optimization
    );

    // Process compression results and update performance statistics
    if (result == 0) {
        success = TRUE;
        DWORD compressionTime = GetTickCount() - startTime;
        UpdateStats(inputSize, *jpegSize, compressionTime);

        // Log detailed compression statistics for performance monitoring
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Debug,
            "TurboJPEG compression: %dx%d, %d bytes -> %lu bytes (%.1f%% ratio) in %dms",
            width, height, inputSize, *jpegSize,
            (double)*jpegSize / inputSize * 100.0, compressionTime);
    } else {
        // Log compression failure with TurboJPEG error details
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "TurboJPEG compression failed: %s", tjGetErrorStr());
    }

    LeaveCriticalSection(&m_criticalSection);
    return success;
}

BOOL TurboJPEGWrapper::CompressFromHBITMAP(
    HDC hdc,
    HBITMAP hBitmap,
    int width,
    int height,
    int quality,
    BYTE** jpegBuffer,
    unsigned long* jpegSize)
{
    if (!m_initialized || !hdc || !hBitmap || !jpegBuffer || !jpegSize) {
        return FALSE;
    }

    BYTE* bitmapData = nullptr;
    int pixelFormat = TJPF_BGR; // Windows default
    BOOL success = FALSE;

    // Extract bitmap data
    if (ExtractBitmapData(hdc, hBitmap, width, height, &bitmapData, &pixelFormat)) {
        // Compress the extracted data
        success = CompressBitmap(bitmapData, width, height, pixelFormat, 
                               quality, jpegBuffer, jpegSize);
        
        // Free the extracted bitmap data
        FreeBitmapData(bitmapData);
    }

    return success;
}

BOOL TurboJPEGWrapper::CompressBitmapToBuffer(
    BYTE* bitmapData,
    int width,
    int height,
    int pixelFormat,
    int quality,
    BYTE* jpegBuffer,
    unsigned long bufferSize,
    unsigned long* jpegSize)
{
    if (!m_initialized || !bitmapData || !jpegBuffer || !jpegSize) {
        return FALSE;
    }

    EnterCriticalSection(&m_criticalSection);

    DWORD startTime = GetTickCount();
    BOOL success = FALSE;

    // Calculate input size
    int pixelSize = tjPixelSize[pixelFormat];
    DWORD inputSize = width * height * pixelSize;

    // Use a temporary pointer for TurboJPEG
    BYTE* tempBuffer = jpegBuffer;
    unsigned long tempSize = bufferSize;

    // Compress the image
    int result = tjCompress2(
        m_compressor,           // Compressor handle
        bitmapData,             // Source image buffer
        width,                  // Image width
        0,                      // Pitch (0 = width * pixel size)
        height,                 // Image height
        pixelFormat,            // Pixel format
        &tempBuffer,            // Compressed image buffer
        &tempSize,              // Size of compressed image
        TJSAMP_420,             // Chrominance subsampling
        quality,                // JPEG quality (1-100)
        TJFLAG_FASTDCT          // Use fast DCT algorithm
    );

    if (result == 0 && tempSize <= bufferSize) {
        *jpegSize = tempSize;
        success = TRUE;

        DWORD compressionTime = GetTickCount() - startTime;
        UpdateStats(inputSize, *jpegSize, compressionTime);
    } else {
        ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error,
            "TurboJPEG buffer compression failed: %s", tjGetErrorStr());
    }

    LeaveCriticalSection(&m_criticalSection);
    return success;
}

BOOL TurboJPEGWrapper::ExtractBitmapData(HDC hdc, HBITMAP hBitmap, int width, int height,
                                        BYTE** bitmapData, int* pixelFormat)
{
    // Create a BITMAPINFO structure for 24-bit RGB
    BITMAPINFO bmi = {0};
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = width;
    bmi.bmiHeader.biHeight = -height; // Negative for top-down DIB
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 24; // 24-bit RGB
    bmi.bmiHeader.biCompression = BI_RGB;
    
    // Calculate the size needed for the bitmap data
    int rowSize = ((width * 3 + 3) & ~3); // Align to 4-byte boundary
    int dataSize = rowSize * height;
    
    // Allocate memory for bitmap data
    *bitmapData = (BYTE*)malloc(dataSize);
    if (!*bitmapData) {
        return FALSE;
    }
    
    // Extract the bitmap data
    int result = GetDIBits(hdc, hBitmap, 0, height, *bitmapData, &bmi, DIB_RGB_COLORS);
    if (result == 0) {
        free(*bitmapData);
        *bitmapData = nullptr;
        return FALSE;
    }
    
    *pixelFormat = TJPF_BGR; // Windows DIBs are BGR format
    return TRUE;
}

void TurboJPEGWrapper::FreeBitmapData(BYTE* bitmapData)
{
    if (bitmapData) {
        free(bitmapData);
    }
}

void TurboJPEGWrapper::UpdateStats(DWORD inputSize, DWORD outputSize, DWORD compressionTime)
{
    m_stats.totalCompressions++;
    m_stats.totalInputBytes += inputSize;
    m_stats.totalOutputBytes += outputSize;
    m_stats.totalCompressionTime += compressionTime;
    
    // Calculate running averages
    m_stats.averageCompressionRatio = (double)m_stats.totalOutputBytes / m_stats.totalInputBytes;
    m_stats.averageCompressionTime = (double)m_stats.totalCompressionTime / m_stats.totalCompressions;
}

void TurboJPEGWrapper::ResetStats()
{
    EnterCriticalSection(&m_criticalSection);
    memset(&m_stats, 0, sizeof(m_stats));
    LeaveCriticalSection(&m_criticalSection);
}

int TurboJPEGWrapper::GetOptimalPixelFormat()
{
    // BGR is optimal for Windows bitmaps
    return TJPF_BGR;
}

unsigned long TurboJPEGWrapper::GetMaxCompressedSize(int width, int height)
{
    // TurboJPEG provides this calculation
    return tjBufSize(width, height, TJSAMP_420);
}

// Global C-style functions for easy integration
extern "C" {

BOOL InitializeTurboJPEG()
{
    if (!g_globalWrapper) {
        g_globalWrapper = new TurboJPEGWrapper();
        if (g_globalWrapper && g_globalWrapper->Initialize()) {
            return TRUE;
        } else {
            delete g_globalWrapper;
            g_globalWrapper = nullptr;
            return FALSE;
        }
    }
    return TRUE;
}

void CleanupTurboJPEG()
{
    if (g_globalWrapper) {
        delete g_globalWrapper;
        g_globalWrapper = nullptr;
    }
}

BOOL TurboJPEG_CompressBitmap(
    HDC hdc,
    HBITMAP hBitmap,
    int width,
    int height,
    int quality,
    BYTE** jpegBuffer,
    unsigned long* jpegSize)
{
    if (!g_globalWrapper) {
        return FALSE;
    }
    
    return g_globalWrapper->CompressFromHBITMAP(hdc, hBitmap, width, height, 
                                               quality, jpegBuffer, jpegSize);
}

void TurboJPEG_FreeBuffer(BYTE* buffer)
{
    if (buffer) {
        tjFree(buffer); // Use TurboJPEG's free function
    }
}

} // extern "C"
