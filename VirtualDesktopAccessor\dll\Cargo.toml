[package]
name = "dll"
version = "0.1.0"
authors = ["<PERSON><PERSON> <<EMAIL>>"]
edition = "2021"
publish = false

[lib]
name = "VirtualDesktopAccessor"
crate-type = ["cdylib"]

# [profile.release]
# strip = true  # Automatically strip symbols from the binary.
# opt-level = "z"  # Optimize for size.
# lto = true
# codegen-units = 1
# panic = "abort"

[dependencies]
winvd = { path = "../", features = ["crossbeam-channel"] }
once_cell = "1.5.0"
crossbeam-channel = { version = "0.5" }
windows = { version = "0.58", features = [
    "implement",
    "Win32_System_Com",
    "Win32_UI_Shell_Common", # for IObjectArray
    "Win32_Foundation",
] }
